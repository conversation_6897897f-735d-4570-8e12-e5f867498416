from sqlalchemy.dialects.postgresql.psycopg2 import PGDialect_psycopg2

class KingbaseDialect(PGDialect_psycopg2):
    def _get_server_version_info(self, connection):
        # 这里直接返回一个假定的版本信息，以避免版本解析错误
        return (8, 4)  # 你可以根据实际需要调整这个版本号

    def initialize(self, connection):
        # 调用父类的 initialize 方法
        super(KingbaseDialect, self).initialize(connection)
        # 设置 server_version_info，避免再次尝试解析版本
        self.server_version_info = self._get_server_version_info(connection)

# 注册自定义方言
from sqlalchemy.dialects import registry
registry.register("kingbase.psycopg2", __name__, "KingbaseDialect")
from app.celery import app
from app.agents.vm_tasks import VmCreateTask
from sqlalchemy.orm import joinedload
from utils.db import get_dbi
from api.libvirt.client import Client as LibvirtClient
from utils.tools import convert_to_bytes
from api.iscsi.iscsi_client import ISCSIManager

@app.task(name="create_sub_disk")
def create_sub_disk(vm_name, form):
    print(f"create_sub_disk:{vm_name} {form}")
    res = []
    for disk in form:
        storage_type_code = disk.get("storage_type_code")
        type_code = disk.get("type_code")
        # 检查卷是否存在
        is_exist = disk.get("is_exist")
        if is_exist:
            print("存储卷已存在，跳过")
            continue
        
        print("存储卷不存在，进行创建")
        print("存储池类型为：", disk.get("type_code"))
        print("存储卷类型为：", disk.get("storage_type_code"))
        if type_code == "IP-SAN":
            # 如果是IP-SAN存储卷，直接返回表单数据
            print("IP-SAN存储卷，直接返回表单数据")
            form["size"] = form.get("capacity", 0)
            form["name"] = form["name"] + "." + form["volume_format"]
            iscsi_form = {
                "path": disk.get("path", ""),
                "name": disk["disk_name"],
                "size": disk["size"],
                "size_unit": disk.get("disk_unit_type", "GB"),
                "format": disk.get("storage_type_code", "qcow2"),
            }
            res = ISCSIManager.create_disk_volume(iscsi_form)
            if res:
                print("创建IP-SAN存储卷成功")
                
                disk["status"] = 4
                disk["allocation"] = disk["capacity"]
                disk["create_res"] = True
                
            else:
                print("创建IP-SAN存储卷失败")
                disk["create_res"] = False
                
        elif type_code == "local":
            libvirtClient = LibvirtClient()
            pool_name = disk.get("pool_name")
            disk_name = disk.get("disk_name")
            storage_type_code = disk.get("storage_type_code", "qcow2")
            virt_form = {
                "storage_device_id": disk.get("disk_id"),
                "storage_pool_id": disk.get("pool_id"),
                "name": disk_name,
                "path": disk.get("path", ""),
                "encrypt": 0,
                "volume_type": storage_type_code,
                "join_type": 3,
                "capacity": disk.get("capacity", 0),  # 容量单位为字节
                "preallocation": 1,
                "remark": "",
                "storage_pool_name": pool_name,
                "storage_type_code": disk.get("type_code", "local"),
                "volume_format" : storage_type_code,
            }
            
            r = libvirtClient.create_storage_pool_volume(libvirtClient, virt_form)

            if r:
                # 获取创建的卷的详情
                volume = libvirtClient.get_storage_pool_volume_info(libvirtClient, pool_name, disk_name+ "." + storage_type_code)
                print("将存储卷信息转换成数据库信息", volume)
                disk["status"] = volume["type"]
                disk["allocation"] = volume["allocation"]
                disk["create_res"] = True
            else:
                print("创建本地存储卷失败")
                disk["create_res"] = False
            
            
        res.append(disk)
            
        # form1 = {
        #     'storage_pool_name': disk.get("pool"),
        #     # 'name': ".".join([disk.get("disk_name"), disk.get("storage_type_code")]),
        #     'name': ".".join([disk.get("disk_name"), disk.get("disk_type")]),
        #     'storage_type_code': disk.get('storage_type_code', 'qcow2'),
        #     'capacity': convert_to_bytes(disk.get('size'), disk.get('disk_unit_type')) ,  # 转换成字节
        # }
        # if not libvirtClient.ensure_remote_disk_exists(libvirtClient, form1):
        #     return {'status': 'failed', 'message': f"Failed to create volume {form1.get('name')} in pool {form1.get('storage_pool_name')}"}

    return res
import time

from contextlib import contextmanager
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy import create_engine

from sqlalchemy.exc import OperationalError

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import config.settings as settings
import traceback


dbi = None  # Initially set to None

class Database:
    def __init__(self, settings=settings):
        if settings.DRIVER == "mysql":
        
            db_url = "{0}://{1}:{2}@{3}:{4}/{5}".format(
                "mysql+pymysql",
                settings.DB_USERNAME,
                settings.DB_PASSWORD,
                settings.DB_HOSTNAME,
                settings.DB_PORT,
                settings.DB_DATABASE
            )
            #print(db_url)
            #self.engine = create_engine(db_url, pool_recycle=3600, pool_timeout=5)
            self.engine = create_engine(db_url, 
                                    pool_size=50,
                                    max_overflow=20, 
                                    pool_recycle=3600, 
                                    pool_timeout=10, 
                                    pool_pre_ping=True)
            
        if settings.DRIVER == "kingbase":
            import kingbase_dialect
        
            db_url = "{0}://{1}:{2}@{3}:{4}/{5}".format(
                "mysql+pymysql",
                settings.DB_USERNAME,
                settings.DB_PASSWORD,
                settings.DB_HOSTNAME,
                settings.DB_PORT,
                settings.DB_DATABASE
            )
            #print(db_url)
            #self.engine = create_engine(db_url, pool_recycle=3600, pool_timeout=5)
            self.engine = create_engine(db_url, 
                                    pool_size=50,
                                    max_overflow=20, 
                                    pool_recycle=3600, 
                                    pool_timeout=10, 
                                    pool_pre_ping=True)

        if settings.DRIVER == "dqlite":
            from sqlalchemy.pool import NullPool
            from sqlalchemy.dialects import registry
            registry.register("dqlite.pydqlite", "sqlalchemy_dqlite.pydqlite", "dialect")
            db_url = 'dqlite+pydqlite://{}:{}/{}'.format(
                settings.DB_HOSTNAME,
                settings.DB_PORT,
                settings.DB_DATABASE
            )
        #print(db_url)
            self.engine = create_engine(db_url,
                                    pool_pre_ping=True,
									echo=True,
         							isolation_level="READ UNCOMMITTED",
                					poolclass=NullPool)
            
    @contextmanager
    def session_scope(self, ):
        session = sessionmaker(bind=self.engine)()
        for _ in range(5):
            try:
                yield session
                session.commit()
                break
            except OperationalError:
                session.rollback()
                traceback.print_exc()
                print("Lost connection to the database, retrying...")
                time.sleep(5)  # wait for 5 seconds before retrying
            except Exception:
                session.rollback()
                traceback.print_exc()
                raise
            finally:
                session.close()
                
    # def __del__(self):
    #     # Close the engine when the Database object is being destroyed
    #     if self.engine:
    #         self.engine.dispose()

    # def close(self):
    #     if self.engine:
    #         self.engine.dispose()
        

def init_db():
    global dbi
    dbi = Database(settings)
    print(f"dbi after init_worker: {dbi}")
    return dbi

def get_dbi():
    global dbi
    if dbi is None:
        init_db()
    return dbi
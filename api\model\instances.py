# -*- coding: utf-8 -*-

from dataclasses import dataclass
import json


@dataclass
class Instances(object):
    id : str
    name : str
    """
    def __repr__(self):
        return json.dumps(self.__dict__)
    def toJson(self):
        return json.dumps(self.__dict__)
    """
    
class InstancesAddtoGroup(object):
    id : str
    group_id : int
    
@dataclass
class InstanceDetail(object):
    id : str
    name : str
    status : str
    created : str
    updated : str
    hostId : str
    addresses : dict
    image : dict
    flavor : dict
    
@dataclass
class InstanceGetDetail(object):
    id : str
    name : str
    status : str
    created : str
    updated : str
    hostId : str
    addresses : dict
    flavor : dict

@dataclass
class ServerRes:
    id : str

class Groups(object):
    instances : dict
    name : str

class Group(object):
    id : int
    
class DeleteGroup(object):
    id : int

class CreateGroup(object):
    name : str
    id : int

class GroupRename(object):
    id : int
    new_name : str

class Clusters(object):
    nodes : dict
    name : str
    
class InstancesDetailFrom(object):
    page: int
    pagecount: int
    search: str
    
    
class InstanceCreateFrom(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid : str
    availability_zone : str
    ipv4 : str

class InstanceCreateFromV2(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid : str
    availability_zone : str
    ipv4 : str
    os_type: str

class InstanceISOCreateFrom(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid : str
    uuid2 : str
    availability_zone : str
    ipv4 : str
    
class InstanceISOCreateFromDriver(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid : str
    uuid2 : str
    availability_zone : str
    uuid_driver : str
    ipv4 : str
    
class InstanceDeleteFrom(object):
    id : str
    
class InstanceActionFrom(object):
    id : str
    action : str
    data : str
    
class InstanceEditFrom(object):
    id : str
    name : str

class InstanceISOEditFrom(object):
    id : str
    description : str


class InstanceDetachFrom(object):
    vmid : str
    volumeid : str
    
class ServerWithISOFrom(object):
    name : str
    imageRef: str
    flavorRef: str
    networks: str
    count: int

@dataclass
class ComputeServiceFrom(object):
    binary : str
    host : str
    status : str
    state : str
    updated_at : str

@dataclass
class InterfaceDetail(object):
    net_id : str
    port_id : str
    port_state : str
    fixed_ips : list
    
@dataclass
class Ports(object):
    id : str
    name : str
    status : str
    fixed_ips : list


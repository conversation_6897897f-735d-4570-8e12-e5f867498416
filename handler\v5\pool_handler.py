# -*- coding: utf-8 -*-

from calendar import c
from hmac import new
from math import e
from unittest import result
from venv import create
import tornado.ioloop
from sqlalchemy import asc, desc

import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom
from api.prometheus.client import Client as Pclient
from api.log.log import CustomLogger
from celery import Celery, chord


import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class PoolHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    @get(_path="/v5/pool/detail/{pool_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_pool_detail(self, pool_id):

        with self.session_scope() as session:
            #查询pool记录
            pool = session.query(Pool).filter(Pool.id == pool_id).first()
            if pool:
                data =  Pool.to_dict_merge(pool)
                return data
            

        return {"msg":"没有找到虚机", "code": 404}


    @post(_path="/v5/pool/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_create(self, form):


        role = self.get_cookie('username', "")
        pool_name = form.get("name", "")
        
        
        try:
            with self.session_scope() as session:
                pool = Pool()
                pool.name = pool_name
                pool.remark = form.get("remark", "")
                session.add(pool)
                
        except Exception as e:
            new_logger.log(
                self.username, "资源池", "创建资源池", "失败", role,
                "创建资源池: {},失败".format(pool_name)
            ) 
            traceback.print_exc()
            return {"msg": "创建失败", "code": 500}
        
        new_logger.log(
            self.username, "资源池", "创建资源池", "成功", role,
            "创建资源池: {},成功".format(pool_name)
        )
            
        
        return {"msg": "创建完成", "code": 200}



    
    @post(_path="/v5/pool/name/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_update(self, form):
        role = self.get_cookie('role', "")
        pool_id = form.get("id", "")
        pool_name = form.get("name", "")
        re = form.get("remark", "")
        try:
            with self.session_scope() as session:
                session.query(Pool).filter(Pool.id == pool_id).update({Pool.name:pool_name, Pool.remark:re})
                
        except Exception as e:
            new_logger.log(
                self.username, "资源池", "更新资源池", "失败", role,
                "更新资源池: {},失败".format(pool_name)
            ) 
            traceback.print_exc()
            return {"msg": "更新资源池失败", "code": 500}
        
        new_logger.log(
            self.username, "资源池", "更新资源池", "成功", role,
            "更新资源池: {},成功".format(pool_name)
        )
            
        
        return {"msg": "更新资源池成功", "code": 200}
    
    @post(_path="/v5/pool/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_delete(self, form):
        role = self.get_cookie('role', "")
        pool_id = form.get("id", "")

        try:
            with self.session_scope() as session:
                pool = session.query(Pool).filter(Pool.id == pool_id).first()
                if pool.clusters:
                    return {"msg": "删除池失败，请确认池下是否存在集群", "code": 400}
                session.query(Pool).filter(Pool.id == pool_id).delete()

        except  Exception as e:
            new_logger.log(
                self.username, "资源池", "删除资源池", "失败", role,
                "删除资源池: {},失败".format(pool_id))

            traceback.print_exc()
            return {"msg": "删除资源池失败", "code": 500}
        
        new_logger.log(
            self.username, "资源池", "删除资源池", "成功", role,
            "删除资源池: {},成功".format(pool_id)
        )
        
        return {"msg": "删除资源池成功", "code": 200}

    @get(_path="/v5/pool/tree", _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_tree(self):
        role = self.get_cookie('role', "")
        # Step 4: 构建 zNodes 树状结构
        zNodes = []

        with self.session_scope() as session:
            # Step 1: 查询所有 Pools
            pools = session.query(Pool).all()

            # Step 2: 查询所有 Clusters
            clusters = session.query(Cluster).all()

            # Step 3: 查询所有 Hosts
            hosts = session.query(Host).all()

            # Step 4: 查询所有虚拟机
            domains = session.query(Domain).filter(Domain.domain_recycle_id == "0").all()


            # 添加 "资源节点"
            zNodes.append({"id": "1", "pid": "0", "name": "资源节点", "type": "ziyuan"})

            # 添加 Pool 节点 ("主机池")
            for pool in pools:
                zNodes.append({
                    "id": str(pool.id),
                    "pid": "1",
                    "name": pool.name,
                    "type": "pool"
                })

                # 添加 Cluster 节点 ("集群")
                for cluster in [c for c in clusters if c.pool_id == str(pool.id)]:
                    zNodes.append({
                        "id": str(cluster.id),
                        "pid": str(pool.id),
                        "name": cluster.name,
                        "type": "cluster"
                    })

                    # 添加 Host 节点 ("主机")
                    for host in [h for h in hosts if h.cluster_id == str(cluster.id)]:
                        zNodes.append({
                            "id": str(host.id),
                            "pid": str(cluster.id),
                            "ip": host.ip,
                            "name": host.name,
                            "type": "host"
                        })

                        # 添加 Domain 节点 ("虚拟机")
                        for domain in [d for d in domains if d.host_id == str(host.id)]:
                            zNodes.append({
                                "id": str(domain.id),
                                "pid": str(host.id),
                                "name": domain.name,
                                "type": "domain"
                            })
        return {"msg": "查询资源树成功", "code": 200, "data": zNodes}

    @get(_path="/v5/resource/overview", _produces=mediatypes.APPLICATION_JSON)
    def hci_resource_overview(self):
        role = self.get_cookie('role', "")
        data = {}

        with self.session_scope() as session:
            # Step 1: 查询所有 Pools
            pool_count = session.query(Pool).count()
            data["pool_count"] = pool_count

            # Step 2: 查询所有 Clusters
            cluster_count = session.query(Cluster).count()
            data["cluster_count"] = cluster_count

            # Step 3: 查询所有 Hosts
            host_count = session.query(Host).count()
            data["host_count"] = host_count

            # Step 4: 查询所有 Host 下的所有虚拟机数量
            vms = session.query(Domain).all()
            data["vm_count"] = str(len(vms))
            logic_cpu_count = 0
            logic_mem_count = 0
            logic_disk_count = 0
            data["cpu_all_hz"] = ""
            data["cpu_use_hz"] = ""
            for vm in vms:
                logic_cpu_count += int(vm.vcpu)
                logic_mem_count += int(vm.memory)

            # Step 5: 查询所有 物理cpu，逻辑cpu，内存，磁盘
            # data["logic_cpu_count"] = str(logic_cpu_count)
            # data["logic_cpu_rate"] = str(0)
            # data["logic_mem_count"] = str(logic_mem_count)
            # data["logic_disk_count"] = str(logic_disk_count) # 存疑

            c = Pclient()
            # all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            all_cpu_hz = c.query_vector_by_query('sum (node_cpu_scaling_frequency_hertz{job=\'node_exporter\'})')
            use_cpu_hz = c.query_vector_by_query('sum((node_cpu_scaling_frequency_hertz / node_cpu_frequency_max_hertz))')
            if all_cpu_hz:
                data["cpu_all_hz"] = all_cpu_hz[0]["value"][1]
                data["cpu_use_hz"] = use_cpu_hz[0]["value"][1]

            all_mem_count = c.query_vector_by_query('sum(node_memory_MemTotal_bytes{job = "node_exporter"})')
            use_mem_count = c.query_vector_by_query('sum(node_memory_MemTotal_bytes{job = "node_exporter"}) - sum(node_memory_MemAvailable_bytes{job = "node_exporter"})')
            if all_mem_count:
                data["mem_all_count"] = all_mem_count[0]["value"][1]
                data["mem_use_count"] = use_mem_count[0]["value"][1]

            all_disk_count = c.query_vector_by_query('sum(node_filesystem_size_bytes{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/"})')
            use_disk_count = c.query_vector_by_query('sum(node_filesystem_size_bytes{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/"})- sum(node_filesystem_free_bytes{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/"})')
            if all_disk_count:
                data["disk_all_count"] = all_disk_count[0]["value"][1]
                data["disk_use_count"] = use_disk_count[0]["value"][1]

        return {"msg": "查询概览成功", "code": 200, "data": data}

    @get(_path="/v5/pool/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_pool_overview(self, _id):
        role = self.get_cookie('role', "")
        data = {}

        with self.session_scope() as session:

            # Step 2: 查询所有 Clusters
            cluster_count = session.query(Cluster).filter(Cluster.pool_id == _id).count()
            data["cluster_count"] = cluster_count

            # Step 3: 查询所有Clusters下的所有 Hosts
            hosts = session.query(Host).filter(Host.pool_id == _id).all()
            data["host_count"] = str(len(hosts))
            host_up_count, host_down_count, host_error_count = 0, 0, 0
            for host in hosts:
                if host.is_connected == "1":
                    host_up_count += 1
                if host.is_connected == "0":
                    host_down_count += 1
                if host.is_connected == "2":
                    host_error_count += 1

            data["host_up_count"] = str(host_up_count)
            data["host_down_count"] = str(host_down_count)
            data["host_error_count"] = str(host_error_count)

            # Step 4: 查询所有 Host 下的所有虚拟机数量
            vms = session.query(Domain).filter(Domain.pool_id == _id).all()
            data["vm_count"] = str(len(vms))
            allocation_cpu_count = 0
            allocation_mem_count = 0
            allocation_disk_count = 0
            data["cpu_all_hz"], data["cpu_use_hz"], = "", ""
            vm_up_count, vm_down_count, vm_error_count = 0, 0, 0
            for vm in vms:
                allocation_cpu_count += int(vm.vcpu)
                allocation_mem_count += int(vm.memory)
                if vm.status == "running":
                    vm_up_count += 1
                if vm.status == "offline":
                    vm_down_count += 1
                if vm.status == "error":
                    vm_error_count += 1

            data["vm_up_count"] = str(vm_up_count)
            data["vm_down_count"] = str(vm_down_count)
            data["vm_error_count"] = str(vm_error_count)


            # Step 5: 查询所有 物理cpu，逻辑cpu，内存，磁盘
            data["cpu_allocation_count"] = str(allocation_cpu_count)
            data["mem_allocation_count"] = str(allocation_mem_count)
            data["disk_allocation_count"] = str(allocation_disk_count) # 存疑

            c = Pclient()
            ip_pattern_parts = [f"{d.ip}:9100"  for d in hosts]
            ip_regex = "|".join(ip_pattern_parts)
            # all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            if all_cpu_count:
                data["cpu_all_count"] = all_cpu_count[0]["value"][1]

            all_cpu_hz = c.query_vector_by_query(f'sum(node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}})')
            use_cpu_hz = c.query_vector_by_query(f'sum((node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}} / node_cpu_frequency_max_hertz{{job="node_exporter", instance=~"{ip_regex}"}}))')
            if all_cpu_hz:
                data["cpu_all_hz"] = all_cpu_hz[0]["value"][1]
                data["cpu_use_hz"] = use_cpu_hz[0]["value"][1]

            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            if all_mem_count:
                data["mem_all_count"] = all_mem_count[0]["value"][1]
                data["mem_use_count"] = use_mem_count[0]["value"][1]

            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            if all_disk_count:
                data["disk_all_count"] = all_disk_count[0]["value"][1]
                data["disk_use_count"] = use_disk_count[0]["value"][1]

        return {"msg": "查询池概览成功", "code": 200, "data": data}



    @post(_path="/v5/pool/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_list(self, form):
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')

        with self.session_scope() as session:
            # 首先创建基础查询
            query = session.query(Pool)

            # 添加搜索条件
            if search_str:
                query = query.filter(Host.name.ilike(f"%{search_str}%"))

            # 添加排序
            if hasattr(Host, order_by):
                order_column = getattr(Host, order_by)
                if order_type == 'asc':
                    query = query.order_by(asc(order_column))
                else:
                    query = query.order_by(desc(order_column))

            # 获取总记录数
            total_records = query.count()

            # 执行分页
            hosts = query.offset((page - 1) * per_page).limit(per_page).all()

            # 将结果转换为字典列表
            host_list = [host.to_dict() for host in hosts]

            return {
                "msg": "获取池列表成功",
                "code": 200,
                "total": total_records,
                "data": host_list
            }

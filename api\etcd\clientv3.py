'''
Created on Dec 15, 2020

@author: maojj
'''
import logging



import json
import etcd3
from collections import deque
import  settings
from api.model import instances

class ClientV3(object):
    
    nova_uri = ""
    token = ""
    def __init__(self):
        self.client = etcd3.client(settings.ETCD_HOST)


    def add_vm_to_group(self,  instance):
        path = "/theweb/groups/%s/%s" % (instance.group_name, instance.id)
        data = {}
        #info  = json.loads(instance.decode('utf-8').replace("'", '"'))
        res = self.client.put(path, json.dumps(data, ensure_ascii=False))
        if res:
            return data

    def delete_vm_from_group(self, instance):
        prefix_path = "/theweb/groups/%s/%s" % (instance.group_name, instance.id)
        data = {}
        #info  = json.loads(instance.decode('utf-8').replace("'", '"'))
        res = self.client.delete_prefix(prefix_path)
        if res:
            return data
    
    
    def get_vm_by_group(self, group_name):
        pre_path = "/theweb/groups/%s/" % group_name
        results = self.client.get_prefix(pre_path, sort_order="descend")
        res = list()
        for info in results:
            #line  = json.loads(info[0].decode('utf-8').replace("'", '"'))
            line = str(info[1].key.decode("utf-8")).split("/")[4]
            res.append(line)
        return res        
        
    def get_all_groups(self):
        pre_path = "/theweb/groups"
        results = self.client.get_prefix(pre_path, sort_order="descend")
        res = list()
        tmp = list()
        for info in results:
            name = str(info[1].key.decode("utf-8")).split("/")[3]
            if name not in tmp:
                tmp.append(name)
                res.append({"name":name})
        return res
        
        
    def add_group(self, name):
        pre_path = "/theweb/groups/%s" % name 
        data = {}
        self.client.put(pre_path, json.dumps(data, ensure_ascii=False))
        
    def rename_group(self, group):
        pre_path = "/theweb/groups/%s" % group.name
        new_path = "/theweb/groups/%s" % group.new_name
        data = {}
        self.client.put(pre_path, json.dumps(data, ensure_ascii=False))

    def delete_group(self, name):
        pre_path = "/theweb/groups/%s" % name 
        res = self.client.delete_prefix(pre_path)
        if res:
            return res
        


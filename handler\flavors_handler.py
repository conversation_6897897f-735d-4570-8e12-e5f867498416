# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import Custom<PERSON>og<PERSON>
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.flavors import FlavorCreateFrom,FlavorDetailFrom
from util.cov import todict
from api.openstack.client import Client

import logging
logger = logging.getLogger(__name__)

class NewFlavorsHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/flavor/{id}",_types=[str],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_flavor(self, id):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 查询全部云主机类型
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
        client = Client()
        res = client.FlavorClient.openstack_get_flavor_detail(client, id)
        return res

class FlavorsHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/flavors", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_flavors(self):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 查询全部云主机类型
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
        client = Client()
        res = client.FlavorClient.openstack_get_all_list(client)
        return res   
    
    @delete(_path="/v1/flavors/delete/{id}",_types=[str],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_flavor(self, id):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 删除云主机类型  参数 1.镜像id
        responses:
          '200':
            description: 删除成功
            content: {}
        """
        try:
            client = Client()
            res = client.FlavorClient.openstack_delete_flavor(client, id)  
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除云主机类型",
                             "object": id,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             }) 
        except Exception as e:
 
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "删除云主机类型",
                             "object": id,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             }) 
                return {"msg":"error"}    
        return res
    
    @post(_path="/v1/flavors/create",_types=[FlavorCreateFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_flavor(self, flavorcreateform):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 创建云主机类型  参数 1.名称
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayFlavorCreateModel'
        """
        try:
            flavorcreateform.ram = flavorcreateform.ram*1024
            client = Client()
            
            #license = client.HypervisorsClient.openstack_get_licence(client)
            #cpu_node = license.split(",")[0][-1]
            #cpu_node = 16
            
            flavor_list = client.FlavorClient.openstack_get_all_list(client)
            for flavor in flavor_list:
                if flavor["name"] == flavorcreateform.name:
                    res = client.FlavorClient.openstack_get_flavor_detail(client,flavor["id"])
                    return res
                
            res = client.FlavorClient.openstack_create_flavor(client,flavorcreateform)
            
            #if flavorcreateform.vcpus < int(cpu_node):
            #    cpu_node = str(flavorcreateform.vcpus)
            
            #re = client.FlavorClient.openstack_create_flavor_extra_specs(client,res["id"],cpu_node)
            
            
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云主机类型",
                             "object": flavorcreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             }) 
        except Exception as e:
 
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建云主机类型",
                             "object": flavorcreateform.name,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             }) 
                return {"msg":"error"} 
        return res
    
    @post(_path="/v1/flavors/detail",_types=[FlavorDetailFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_flavor_detail(self, form):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 创建云主机类型  参数 1.名称
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayFlavorCreateModel'
        """

        client = Client()
        
        res = client.FlavorClient.openstack_get_flavor_detail(client,form.id)
        res["ram"] = int(res["ram"]/1024)
        return res
    
    '''@put(_path="/v1/images/edit/{id}",_types=[str,ImageEditFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_edit_image(self, id,imageeditform):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 编辑镜像  
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageEditModel'
        """
        
        client = Client()
        res = client.ImageClient.openstack_edit_image(client,id,imageeditform)
        return res'''

    @post(_path="/v2/flavors/create",_types=[FlavorCreateFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_flavors(self, flavorcreateform):
        new_logger = CustomLogger()
        role = self.get_cookie('role', "")
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 创建云主机类型  参数 1.名称
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayFlavorCreateModel'
        """
        try:
            flavorcreateform.ram = flavorcreateform.ram*1024
            client = Client()

            #license = client.HypervisorsClient.openstack_get_licence(client)
            #cpu_node = license.split(",")[0][-1]
            #cpu_node = 16
            if hasattr(flavorcreateform, 'metadata_sys_version') and flavorcreateform.metadata_sys_version:
                flavorcreateform.name = "%s-%s" % (flavorcreateform.name, flavorcreateform.metadata_sys_version)

            flavor_list = client.FlavorClient.openstack_get_all_list(client)
            for flavor in flavor_list:
                if flavor["name"] == flavorcreateform.name:
                    res = client.FlavorClient.openstack_get_flavor_detail(client,flavor["id"])
                    return res

            res = client.FlavorClient.openstack_create_flavor(client, flavorcreateform)
            if hasattr(flavorcreateform, 'metadata_sys_version') and flavorcreateform.metadata_sys_version:
                cpuinfo = self.parse_cpuinfo()
                res1 = client.FlavorClient.openstack_create_flavor_extra_specs_key(client, res.get("id"), cpuinfo.get("model name"),flavorcreateform.metadata_sys_version)
                res.update(res1)
            #if flavorcreateform.vcpus < int(cpu_node):
            #    cpu_node = str(flavorcreateform.vcpus)

            #re = client.FlavorClient.openstack_create_flavor_extra_specs(client,res["id"],cpu_node)


            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": self.get_cookie('username', ""),
                         "op": "创建云主机类型",
                         "object": flavorcreateform.name,
                         "role": self.get_cookie('role', ""),
                         "result": "成功",
                         })
            new_logger.log(
                self.username, "虚机操作", "创建云主机类型", "成功", role, "创建云主机类型: {},成功".format(flavorcreateform.name)
            )
        except Exception as e:

            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                         {"username": self.get_cookie('username', ""),
                          "op": "创建云主机类型",
                          "object": flavorcreateform.name,
                          "role": self.get_cookie('role', ""),
                          "result": "失败",
                          })
            new_logger.log(
                self.username, "虚机操作", "创建云主机类型", "失败", role, "创建云主机类型: {},失败".format(flavorcreateform.name)
            )
            return {"msg":"error"}
        return res

    def parse_cpuinfo(self):
        cpuinfo = {}
        try:
            with open('/proc/cpuinfo') as f:
                for line in f:
                    # 对每一行用冒号分割键和值
                    parts = line.strip().split(':')
                    if len(parts) == 2:
                        key, value = parts
                        key = key.strip()
                        value = value.strip()
                        # 将信息添加到字典中，键为信息的名称，值为对应的详细信息
                        cpuinfo[key] = value
        except IOError as e:
            print("Error reading /proc/cpuinfo: ", e)

        return cpuinfo
from app.celery import app
from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from api.log.log import CustomLogger
from utils.db import get_dbi
from db.model.hci.network import Switch, SwitchPorts
from db.model.hci.compute import Domain, DomainDisk, DomainInterface,Host
from db.model.hci.storage import StoragePool, StorageVolume
import traceback
from celery import Task, group
import requests
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
from config.settings import QUEUE_NAME
from app.agents.vm_tasks import open_vm, close_vm, destroy_vm, pause_vm, recover_vm, reboot_vm, restart_vm

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class VmCreateTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )   

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 


class VmCreateCallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 


@app.task(name="create_vm_callback_abandon")
def create_vm_callback_abandon(results):
    # 废弃
    print(f"Task completed! The result is: {results}")

    # dbi = get_dbi()

    # # with dbi.session_scope() as session:
    # #     domain_list = session.query(Domain).all()
    # #     print(domain_list)

    # 通过results 来合并虚机的json信息
    form = {}
    interface = []
    disk = []
    domain_id = ""
    domain_name = ""

    # 测试等10秒
    import time
    time.sleep(20)

    for result in results:
        if result.get("status") == "failed":
            print("VM creation failed.")
            print(result.get("error"))
            return False

        if result.get("form", {}):
            form = result.get("form", {})
            if form:
                domain_id = form.get("domain_id", "")
                domain_name = form.get("name", "")

        if result.get("interface", []):
            interface = result.get("interface", [])

        if result.get("disk", []):
            disk = result.get("disk", [])

    data = form.copy()
    data["interface"] = interface
    data["disk"] = disk

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = data.get("host_ip", "")
    # client = LibvirtClient(host_ip)
    # res = client.create_dom_vm(client, data)

    print("VM creation successful.")

    dbi = get_dbi()
    with dbi.session_scope() as session:

        # 通过domain_id来修改domain表的状态
        domain = session.query(Domain).filter(Domain.id == domain_id).first()
        domain.status = "running"
        session.add(domain)

        # 根据disk_id 需要修改盘和虚机的关系
        disks = data.get("disk", [])
        for disk in disks:
            disk_id = disk.get("disk_id", "")
            pass

        # 根据port_id 需要修改网卡和虚机的关系
        interfaces = data.get("interface", [])
        for interface in interfaces:
            port_id = interface.get("port_id", "")
            pass

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdroms = data.get("cdrom", [])
        for cdrom in cdroms:
            cdrom_id = cdrom.get("cdrom_id", "")
            pass

    # TODO 用代理方式
    nameko_config = {
        'AMQP_URI': settings.MSG_AMQP_URI
    }
    body_data = {"id": domain_id, "name": domain_name}
    MESSAGE_EVENT = "refresh"
    event = MESSAGE_EVENT
    channel = "vm_list"
    with ClusterRpcProxy(nameko_config) as cluster_rpc:
        websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
        websocket_hub.broadcast(channel, event, body_data)

    # TODO 发送消息总线上 用rest api方式
    # channel_vm_list_path = "http://10.168.2.4:9000/send/vm_list"
    # data = {"id": "vm_table", "action": "refresh"}

    # #requests 发送 rest 
    # # Send the POST request
    # response = requests.post(channel_vm_list_path, json=data)        
    # # Check the response
    # if response.status_code == 200:
    #     print("Request was successful")
    #     print("Response data:", response.json())
    # else:
    #     print(f"Request failed with status code: {response.status_code}")
    #     print("Response content:", response.text)


@app.task(base=VmCreateCallbackTask, name="hello")
def hello(results):
    print("hello")


@app.task(base=VmCreateCallbackTask, name="create_vm_callback_new")
def create_vm_callback_new(info, result):
    print(f"Task completed! The result is: {result}")
    if not result:
        print("VM creation failed.")
        return False

    if result.get("status") == "failed":
        print("VM creation failed.")
        print(result.get("error"))
        return False

    domain_id = ""
    domain_name = ""

    # 测试等10秒
    import time
    time.sleep(5)

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = result.get("host_ip", "")
    client = LibvirtClient(host_ip)
    res = client.create_dom_vm(client, result)

    print("VM creation successful.")

    dbi = get_dbi()
    with dbi.session_scope() as session:

        if result.get("domain_id"):
            domain_id = result.get("domain_id", "")
            session.query(Domain).filter(Domain.id == domain_id).update({Domain.status : "running"})
            session.commit()

        # 根据disk_id 需要修改盘和虚机的关系
        if result.get("disk", []):
            disks = result.get("disk", [])
            for disk in disks:
                disk_id = disk.get("disk_id", "")
                pass

        # 根据port_id 需要修改网卡和虚机的关系
        if result.get("interface", []):
            interfaces = result.get("interface", [])
            for interface in interfaces:
                port_id = interface.get("port_id", "")
                pass

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdroms = result.get("cdrom", [])
        for cdrom in cdroms:
            cdrom_id = cdrom.get("cdrom_id", "")
            pass

    # #TODO 用代理方式
    # nameko_config = {
    #     'AMQP_URI': settings.MSG_AMQP_URI
    # }
    # body_data = {"id": domain_id, "name": domain_name }
    # MESSAGE_EVENT = "refresh"
    # event = MESSAGE_EVENT
    # channel = "vm_list"
    # with ClusterRpcProxy(nameko_config) as cluster_rpc:
    #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
    #     websocket_hub.broadcast(channel, event, body_data)


@app.task(name="create_vm_callback")
def create_vm_callback(result):
    print(f"create_vm_callback:{result}")
    domain_id = result.get("domain_id")
    host_id = result.get("host_id")
    host_ip = result.get("host_ip", "")
    dbi = get_dbi()
    with dbi.session_scope() as session:
        spice_port = result.get("spice_port", "")
        vnc_port = result.get("vnc_port", "")
        status = result.get("status", "")

        if result.get("domain_id"):
            domain_id = result.get("domain_id", "")
            session.query(Domain).filter(Domain.id == domain_id).update(
                {Domain.status : status,
                 Domain.spice_port: spice_port,
                 Domain.vnc_port: vnc_port
                 })
            session.commit()

        # 根据disk_id 需要修改盘和虚机的关系
        disks = result.get("disk", [])
        domain_disks = []
        for disk in disks:
            disk_id = disk.get("disk_id", "")
            pool_id = disk.get("pool_id", "")
            domain_disk = DomainDisk(
                domain_id=domain_id,
                storage_pool_id=pool_id,
                storage_vol_id=disk_id,
                type_code="file",  # 默认文件类型
                device="disk",     # 默认磁盘设备
                dev="vda",         # 默认设备名（可根据实际情况调整）
                bus="virtio",      # 默认总线类型
                qemu_type="qcow2", # 默认磁盘格式
                boot_order=0,      # 默认引导顺序
                host_id=host_id,  # 必须提供 host_id（外键）
            )
            domain_disks.append(domain_disk)
        if domain_disks:
            session.add_all(domain_disks)
        
        
        # 根据port_id 需要修改网卡和虚机的关系
        interfaces = result.get("interface", [])
        domain_interfaces = []
        for interface in interfaces:
            port_id = interface.get("switch_port_id", "")
            domain_interface = DomainInterface(
                domain_id=domain_id,
                switch_port_id=port_id,
                type_code="bridge",  # 默认桥接模式
                mac="52:54:00:00:00:01",  # 生成随机或默认 MAC
                model="virtio",     # 默认网卡型号
            )
            domain_interfaces.append(domain_interface)
        if domain_interfaces:
            session.add_all(domain_interfaces)

        # 根据cdrom_id 需要修改光驱和虚机的关系
        cdrom_disks = []
        cdroms = result.get("cdrom", [])
        for cdrom in cdroms:
            disk_id = cdrom.get("disk_id", "")
            if not disk_id:
                continue
            pool_id = cdrom.get("pool_id", "")
            cdrom_disk = DomainDisk(
                domain_id=domain_id,
                storage_pool_id=pool_id,
                storage_vol_id=disk_id,
                type_code="file",   # 光驱通常是文件
                device="cdrom",     # 设备类型为光驱
                dev="hda",          # 光驱通常用 ide 总线
                bus="ide",          # 光驱总线类型
                qemu_type="raw",    # 光驱格式通常为 raw
                boot_order=1,       # 光驱引导顺序
                host_id=host_id,  # 必须提供 host_id
            )
            cdrom_disks.append(cdrom_disk)
        if cdrom_disks:
            session.add_all(cdrom_disks)
            

    # #TODO 用代理方式
    # nameko_config = {
    #     'AMQP_URI': settings.MSG_AMQP_URI
    # }
    # body_data = {"id": domain_id, "name": domain_name }
    # MESSAGE_EVENT = "refresh"
    # event = MESSAGE_EVENT
    # channel = "vm_list"
    # with ClusterRpcProxy(nameko_config) as cluster_rpc:
    #     websocket_hub = cluster_rpc.websocket_service  # Access the Nameko service
    #     websocket_hub.broadcast(channel, event, body_data)

@app.task(name="clone_vm")
def clone_vm(form):
    """
    虚拟机克隆
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient(host)
    client.vm_clone(client, vm_name)
    print(f"克隆虚拟机 VM: {vm_name}")
    return f"VM {vm_name} clone successfully."


@app.task(name="delete_vm")
def delete_vm(form):
    """
    删除单个
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient(host)
    client.del_vm(client, vm_name)
    print(f"删除虚拟机 VM: {vm_name}")
    return f"VM {vm_name} deleted successfully."

@app.task(name="delete_vm_callback")
def delete_vm_callback(form):
    """
    删除单个
    :param form:
    :return:
    """
    print(f"delete_vm_callback:{form}")
    vm_name = form.get("vm_name", "")
    return f"VM {vm_name} deleted successfully."



@app.task(name="distach_op_vm_open")
def distach_op_vm_open(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = open_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | open_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


# @app.task(name="open_vm")
# def open_vm(form):
#     """
#     开机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)

#     client.start_vm(client, vm_name)
#     print(f"虚拟机开机 VM: {vm_name}")
#     return f"开机虚拟机成功"

@app.task(name="open_vm_callback")
def open_vm_callback(form):
    """
    开机
    :param form:
    :return:
    """
    print("open_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = []  # 存储批量更新的数据
    
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"开机虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()
    
    result = f"虚拟机开机任务执行完成 开机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


@app.task(name="distach_op_vm_close")
def distach_op_vm_close(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = close_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | close_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="close_vm_callback")
def close_vm_callback(form):
    """
    关机
    :param form:
    :return:
    """
    print("close_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"关机虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutoff",
                 Domain.spice_port: 0,  # 清空动态端口
                 Domain.vnc_port: 0      # 清空动态端口
                 },
                synchronize_session=False
            )
            session.commit()
    
    result = f"虚拟机关机任务执行完成 关机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="close_vm")
# def close_vm(form):
#     """
#     关机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.stop_vm(client, vm_name)
#     print(f"虚拟机关机 VM: {vm_name}")
#     return f"关机虚拟机成功"

@app.task(name="distach_op_vm_destroy")
def distach_op_vm_destroy(form):
    """
    分发操作
    :param form:
    :return:
    """
    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = destroy_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | destroy_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="destroy_vm_callback")
def destroy_vm_callback(form):
    """
    强制关机
    :param form:
    :return:
    """
    print("destroy_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"强制关机虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutoff",
                 Domain.spice_port: 0,  # 清空动态端口
                 Domain.vnc_port: 0      # 清空动态端口
                 },
                synchronize_session=False
            )
            session.commit()
    
    result = f"虚拟机强制关机任务执行完成 强制关机成功 {success_count} 失败 {failed_count}"
    print(result)
    return result



# @app.task(name="destroy_vm")
# def destroy_vm(form):
#     """
#     强制关机
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.force_stop_vm(client, vm_name)
#     print(f"虚拟机强制关机 VM: {vm_name}")
#     return f"强制关机虚拟机成功"


@app.task(name="distach_op_vm_pause")
def distach_op_vm_pause(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = pause_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | pause_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="pause_vm_callback")
def pause_vm_callback(form):
    """
    暂停
    :param form:
    :return:
    """
    print("pause_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"暂停虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "paused"},
                synchronize_session=False
            )
            session.commit()
    
    result = f"虚拟机暂停任务执行完成 暂停成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="pause_vm")
# def pause_vm(form):
#     """
#     暂停
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.suspend_vm(client, vm_name)
#     print(f"虚拟机暂停 VM: {vm_name}")
#     return f"批量暂停虚拟机成功"


@app.task(name="distach_op_vm_recover")
def distach_op_vm_recover(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = recover_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | recover_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()


@app.task(name="recover_vm_callback")
def recover_vm_callback(form):
    """
    恢复
    :param form:
    :return:
    """
    print("recover_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"恢复虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "running"},
                synchronize_session=False
            )
            session.commit()
    
    result = f"虚拟机恢复任务执行完成 恢复成功 {success_count} 失败 {failed_count}"
    print(result)
    return result

# @app.task(name="recover_vm")
# def recover_vm(form):
#     """
#     恢复
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.resume_vm(client, vm_name)
#     print(f"虚拟机恢复 VM: {vm_name}")
#     return f"批量恢复虚拟机成功"


@app.task(name="distach_op_vm_reboot")
def distach_op_vm_reboot(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = reboot_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | reboot_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()
    
@app.task(name="reboot_vm_callback")
def reboot_vm_callback(form):
    """
    重启
    :param form:
    :return:
    """
    print("reboot_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = [] # 存储批量更新的数据
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"重启虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()
    
    result = f"虚拟机重启任务执行完成 重启成功 {success_count} 失败 {failed_count}"
    print(result)
    return result


# @app.task(name="reboot_vm")
# def reboot_vm(form):
#     """
#     重启
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.reboot_vm(client, vm_name)
#     print(f"虚拟机重启 VM: {vm_name}")
#     return f"批量重启虚拟机成功"


@app.task(name="distach_op_vm_restart")
def distach_op_vm_restart(form):
    """
    分发操作
    :param form:
    :return:
    """

    jobs = []
    for target in form:
        ip = target['host']['ip']
        data = target

        queue_name = "queue_"+ ip

        # 分发子任务
        job = restart_vm.s(data).set(queue=queue_name)
        jobs.append(job)

    # 使用 group 来聚合所有子任务，并指定最终的回调函数
    job_group = group(jobs) | restart_vm_callback.s().set(queue=QUEUE_NAME)
    job_group.apply_async()
    
@app.task(name="restart_vm_callback")
def restart_vm_callback(form):
    """
    强制重启
    :param form:
    :return:
    """
    print("restart_vm_callback 参数:", form)
    dbi = get_dbi()
    success_count = 0
    failed_count = 0
    success_vms = []
    failed_vms = []
    ids = []
    updates = [] # 存储批量更新的数据
    # 先统计成功和失败的虚拟机
    for vm in form:
        if vm["task_status"] == "success":
            updates.append({
                "id": vm.get("id"),
                "status": "running",  # 统一更新状态
                "spice_port": vm.get("spice_port"),  # 动态端口
                "vnc_port": vm.get("vnc_port"),      # 动态端口
            })
            ids.append(vm.get("id"))
            success_vms.append(vm.get("name"))
            success_count += 1
        else:
            failed_vms.append(vm.get("name"))
            failed_count += 1
            print(f"强制重启虚拟机失败: {vm.get('name')}")
    
    # 批量更新成功的虚拟机状态
    if success_vms:
        with dbi.session_scope() as session:
            session.bulk_update_mappings(Domain, updates)
            # session.query(Domain).filter(Domain.id.in_(ids)).update(
            #     {Domain.status: "running"},
            #     synchronize_session=False
            # )
            # session.commit()
    
    result = f"虚拟机强制重启任务执行完成 强制重启成功 {success_count} 失败 {failed_count}"
    print(result)
    return result

# @app.task(name="restart_vm")
# def restart_vm(form):
#     """
#     强制重启
#     :param form:
#     :return:
#     """
#     host = form.get("host", "")
#     vm_name = form.get("vm_name")
#     client = LibvirtClient(host)
#     client.force_reboot_vm(client, vm_name)
#     print(f"虚拟机强制重启 VM: {vm_name}")
#     return f"批量强制重启虚拟机成功"


@app.task(name="batch_delete_vm")
def batch_delete_vm(form):
    """
    批量删除
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        # 删除虚拟机的逻辑
        client.del_vm(client, vm_name)
        print(f"删除虚拟机 VM: {vm_name}")
    return f"批量删除虚拟机成功"


@app.task(name="batch_open_vm")
def batch_open_vm(form):
    """
    批量开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.start_vm(client, vm_name)
        print(f"虚拟机开机 VM: {vm_name}")
    return f"批量开机虚拟机成功"


@app.task(name="batch_close_vm")
def batch_close_vm(form):
    """
    批量关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
    return f"批量关机虚拟机成功"


@app.task(name="batch_destroy_vm")
def batch_destroy_vm(form):
    """
    批量强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
    return f"批量强制关机虚拟机成功"


@app.task(name="batch_pause_vm")
def batch_pause_vm(form):
    """
    批量暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
    return f"批量暂停虚拟机成功"


@app.task(name="batch_recover_vm")
def batch_recover_vm(form):
    """
    批量恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
    return f"批量恢复虚拟机成功"


@app.task(name="batch_reboot_vm")
def batch_reboot_vm(form):
    """
    批量重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
    return f"批量重启虚拟机成功"


@app.task(name="batch_restart_vm")
def batch_restart_vm(form):
    """
    批量强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_reboot_vm(client, vm_name)
        print(f"虚拟机强制重启 VM: {vm_name}")
    return f"批量强制重启虚拟机成功"

'''
Created on Jan 20, 2022

@author: maojj
'''
import requests
import json
import settings
from api.model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
import time
from datetime import datetime, timedelta
import requests
from requests.auth import HTTPBasicAuth
    
class Client(object):
    
    token = ""
    def __init__(self):

        
        self.range_url = ""
        self.point_url = ""
        self.create_token()
        """
        package_perfix= "api.grafana.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    self.__dict__[attribute_name] = attribute
        """

    def create_token(self):
        toke_url = "%s/api/auth/keys" % settings.GRAFANA_AUTH_URI
        body = {"name":"apikeycurl", "role": "Admin"}
        headers = {
            "Content-Type":"application/json"
        }
        
        r = requests.post(toke_url, data=json.dumps(body), headers=headers, verify=False)
        print(r.text)
        
        
        #curl -u 'admin:admin' http://192.168.214.100:3000/api/datasources
        
        datasources_url = "%s/api/datasources" % settings.GRAFANA_AUTH_URI

        username = 'admin'
        password = 'admin'
        
        # 发送 GET 请求
        response = requests.get(datasources_url, auth=HTTPBasicAuth(username, password), verify=False)
        
        # 检查 HTTP 响应是否成功
        if response.status_code == 200:
            # 打印响应的 JSON 数据
            datasources = response.json()
            print(datasources)
            for datasource in datasources:
                if datasource["name"] == "Prometheus":
                    source_id = datasource["id"]
                    self.range_url = "%s/api/datasources/proxy/%s/api/v1/query_range" % (settings.GRAFANA_AUTH_URI, source_id)
                    self.point_url = "%s/api/datasources/proxy/%s/api/v1/query" % (settings.GRAFANA_AUTH_URI, source_id)
            
            
        else:
            print(f'Request failed with status code {response.status_code}')        
        
        
        
    def get_token(self):
        pass
        

    def query_pormQL_range(self, query):
        url = self.range_url

        end_datetime = datetime.now() 
        start_datetime = end_datetime - timedelta(hours = 1)
        print(url)
        print(query)
        print(int(time.mktime(start_datetime.timetuple())))
        print(int(time.mktime(end_datetime.timetuple())))
        params = {
            "query": query,
            "start": str(int(time.mktime(start_datetime.timetuple()))),
            "end": str(int(time.mktime(end_datetime.timetuple()))),
            "step": "30"
        }
        
        r = requests.get(url, params=params, verify=False)
        
        d = json.loads(r.text)
        

        return d
    
    def query_pormQL_range_step(self, query):
        url = self.range_url

        end_datetime = datetime.now() 
        start_datetime = end_datetime - timedelta(hours = 1)
        print(url)
        print(query)
        print(int(time.mktime(start_datetime.timetuple())))
        print(int(time.mktime(end_datetime.timetuple())))
        params = {
            "query": query,
            "start": str(int(time.mktime(start_datetime.timetuple()))),
            "end": str(int(time.mktime(end_datetime.timetuple()))),
            "step": "300"
        }
        
        r = requests.get(url, params=params, verify=False)
        
        d = json.loads(r.text)
        

        return d

    def query_pormQL_point(self, query):
        url = self.point_url

        end_datetime = datetime.now()
        start_datetime = end_datetime - timedelta(hours = 1)

        params = {
            "query": query,
            "start": str(int(time.mktime(start_datetime.timetuple()))),
            "end": str(int(time.mktime(end_datetime.timetuple()))),
            "step": "300"
        }
        
        
        r = requests.get(url, params=params, verify=False)
        
        d = json.loads(r.text)
        

        return d

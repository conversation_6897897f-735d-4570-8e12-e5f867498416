from functools import wraps

#检查创建虚机参数
def validate_data(expected_keys):
    def decorator(func):
        @wraps(func)
        def wrapper(self, data, *args, **kwargs):
            # 检查 data 是否是字典
            if not isinstance(data, dict):
                raise ValueError("data 参数必须是字典")

            # 检查顶层键是否存在
            for key in expected_keys:
                if key not in data:
                    raise ValueError(f"缺少必需的键: {key}")

            # 检查 'disk' 键是否是字典并且包含正确的键
            if not isinstance(data.get('disk'), list):
                raise ValueError("'disk' 必须是字典")
            
            # 检查 'interface' 键是否是字典并且包含正确的键
            if not isinstance(data.get('interface'), list):
                raise ValueError("'interface' 必须是字典")
            
            # disk_expected_keys = ["size", "format", "source"]
            # for key in disk_expected_keys:
            #     if key not in data['disk']:
            #         raise ValueError(f"'disk' 缺少必需的键: {key}")

            # 通过所有检查后调用原始函数
            return func(self, data, *args, **kwargs)
        return wrapper
    return decorator



def capacity_to_bytes(capacity):
    """
    将容量字符串转换为字节数
    :param capacity: 容量字符串，如 "10G"
    :return: 字节数
    """
    if not isinstance(capacity, str):
        raise ValueError("capacity 参数必须是字符串")

    if not capacity.endswith(('K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y')):
        raise ValueError("容量字符串格式错误")

    num = int(capacity[:-1])
    unit = capacity[-1].upper()

    if unit == 'K':
        return num * 1024
    elif unit == 'M':
        return num * 1024 * 1024
    elif unit == 'G':
        return num * 1024 * 1024 * 1024
    elif unit == 'T':
        return num * 1024 * 1024
    elif unit == 'P':
        return num * 1024 * 1024 * 1024 * 1024
    elif unit == 'E':
        return num * 1024 * 1024 * 1024 * 1024 * 1024
    elif unit == 'Z':
        return num * 1024 * 1024 * 1024 * 1024 * 1024 * 1024
    elif unit == 'Y':
        return num * 1024 * 1024 * 1024 * 1024 * 1024 * 1024 * 1024 * 1024
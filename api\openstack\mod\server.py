import requests
import json
import settings
import threading

from api.model.instances import Clusters, Instances, InstanceDetail, ServerRes, InterfaceDetail, InstanceDetail, \
    InstanceGetDetail, ComputeServiceFrom
from dataclasses import dataclass
from dacite import from_dict
from string import Template
from datetime import *
from dateutil import tz, zoneinfo
from api.openstack.client import Client


def openstack_update_volume_name(func):
    import time

    def update(ins_obj):
        client = Client()
        while True:
            time.sleep(3)
            ins = client.NovaClient.openstack_get_server_detail_v2(client, ins_obj["id"])
            if ins["volumeid"]:
                break

        client.VolumeClient.openstack_edit_volume(client, ins["volumeid"], ins["name"])

    def thread_(*args, **kargs):
        ins = func(*args, **kargs)
        thread = threading.Thread(target=update, args=(ins,))
        thread.start()

        return ins

    return thread_

class NovaClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_instance_list(self):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            ins = from_dict(data_class=Instances, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_get_all_instance_list_v2(self):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            res.append(ins_dict)
        return res

    def openstack_get_all_migrations_list(self):
        method = "/os-migrations"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        print(d)
        res = []
        # for ins_dict in d["servers"]:
        #     ins = from_dict(data_class=Instances, data=ins_dict)
        #     res.append(ins.__dict__)
        # return res
        for ins_dict in d.get("migrations", []):
            res.append(ins_dict)
        return res

    def openstack_get_all_instance_detail_dayu(self):
        method = "/servers/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            data = {}
            data["id"] = ins_dict["id"]
            data["name"] = ins_dict["name"]
            data["hostname"] = ins_dict["OS-EXT-SRV-ATTR:host"]
            data["domain"] = ins_dict["OS-EXT-SRV-ATTR:instance_name"]
            data["ip"] = ""
            for net in ins_dict["addresses"]:
                print(ins_dict["addresses"][net])
                for subnet in ins_dict["addresses"][net]:
                    data["ip"] = "%s%s," % (data["ip"], subnet["addr"])

            data["ip"] = data["ip"].rstrip(',')

            data["status"] = ins_dict["status"]

            if len(ins_dict["tags"]) > 0:
                if ins_dict["tags"][0] == "enable_auto_migrate":
                    data["migrate"] = "enable"
                elif ins_dict["tags"][0] == "disable_auto_migrate":
                    data["migrate"] = "disable"
            else:
                data["migrate"] = "enable"

            data["vcpus"] = ins_dict["flavor"]["vcpus"]
            data["ram"] = ins_dict["flavor"]["ram"]
            data["disk"] = ins_dict["flavor"]["disk"]

            data["task_state"] = ins_dict["OS-EXT-STS:task_state"]

            res.append(data)

        return res

    # 获取服务器的id、名称和链接
    def openstack_get_all_instance(self, params):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers, params=params)

        d = json.loads(r.text)
        return d

    def openstack_get_all_instance_detail(self):
        method = "/servers/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            data = {}
            data["id"] = ins_dict["id"]
            data["name"] = ins_dict["name"]
            data["hostname"] = ins_dict["OS-EXT-SRV-ATTR:host"]
            data["ip"] = ""
            for net in ins_dict["addresses"]:
                print(ins_dict["addresses"][net])
                for subnet in ins_dict["addresses"][net]:
                    data["ip"] = "%s%s," % (data["ip"], subnet["addr"])

            data["ip"] = data["ip"].rstrip(',')

            if len(ins_dict["tags"]) > 0:
                if ins_dict["tags"][0] == "enable_auto_migrate":
                    data["migrate"] = "enable"
                elif ins_dict["tags"][0] == "disable_auto_migrate":
                    data["migrate"] = "disable"
            else:
                data["migrate"] = "enable"

            data["status"] = ins_dict["status"]
            if ins_dict["image"]:
                data["imageid"] = ins_dict["image"]["id"]
            else:
                data["imageid"] = False
            # data["flavorid"] = ins_dict["flavor"]["id"]
            data["vcpus"] = ins_dict["flavor"]["vcpus"]
            data["ram"] = ins_dict["flavor"]["ram"]
            data["disk"] = ins_dict["flavor"]["disk"]
            data["volumes"] = ins_dict["os-extended-volumes:volumes_attached"]
            data["mountpoint"] = ins_dict["OS-EXT-SRV-ATTR:root_device_name"]
            data["description"] = ins_dict["description"]
            data["task_state"] = ins_dict["OS-EXT-STS:task_state"]
            if "os_type" in ins_dict["metadata"]:
                data["os_type"] = ins_dict["metadata"]["os_type"]
            else:
                data["os_type"] = ""

            res.append(data)

        return res

    def openstack_get_all_instance_detail_v2(self, params):
        method = "/servers/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers, params=params)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            data = {}
            data["id"] = ins_dict["id"]
            data["name"] = ins_dict["name"]
            data["hostname"] = ins_dict["OS-EXT-SRV-ATTR:host"]
            data["ip"] = ""
            for net in ins_dict["addresses"]:
                print(ins_dict["addresses"][net])
                for subnet in ins_dict["addresses"][net]:
                    data["ip"] = "%s%s," % (data["ip"], subnet["addr"])

            data["ip"] = data["ip"].rstrip(',')

            if len(ins_dict["tags"]) > 0:
                if ins_dict["tags"][0] == "enable_auto_migrate":
                    data["migrate"] = "enable"
                elif ins_dict["tags"][0] == "disable_auto_migrate":
                    data["migrate"] = "disable"
            else:
                data["migrate"] = "enable"

            data["status"] = ins_dict["status"]
            if ins_dict["image"]:
                data["imageid"] = ins_dict["image"]["id"]
            else:
                data["imageid"] = False
            # data["flavorid"] = ins_dict["flavor"]["id"]
            data["vcpus"] = ins_dict["flavor"]["vcpus"]
            data["ram"] = ins_dict["flavor"]["ram"]
            data["disk"] = ins_dict["flavor"]["disk"]
            data["volumes"] = ins_dict["os-extended-volumes:volumes_attached"]
            data["mountpoint"] = ins_dict["OS-EXT-SRV-ATTR:root_device_name"]
            data["description"] = ins_dict["description"]
            data["task_state"] = ins_dict["OS-EXT-STS:task_state"]
            data["vm_state"] = ins_dict["OS-EXT-STS:vm_state"]
            if "os_type" in ins_dict["metadata"]:
                data["os_type"] = ins_dict["metadata"]["os_type"]
            else:
                data["os_type"] = ""

            res.append(data)

        return res

    def openstack_get_all_instance_detail_v3(self, params):
        method = "/servers/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers, params=params)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            data = {}
            data["id"] = ins_dict["id"]
            data["name"] = ins_dict["name"]
            data["hostname"] = ins_dict["OS-EXT-SRV-ATTR:host"]
            data["addresses"] = []
            for net in ins_dict["addresses"]:
                for subnet in ins_dict["addresses"][net]:
                    data["addresses"].append(subnet)

            # if len(ins_dict["tags"]) > 0:
            #     if ins_dict["tags"][0] == "enable_auto_migrate":
            #         data["migrate"] = "enable"
            #     elif ins_dict["tags"][0] == "disable_auto_migrate":
            #         data["migrate"] = "disable"
            # else:
            #     data["migrate"] = "enable"

            data["status"] = ins_dict["status"]
            if ins_dict["image"]:
                data["imageid"] = ins_dict["image"]["id"]
            else:
                data["imageid"] = False
            # data["flavorid"] = ins_dict["flavor"]["id"]
            data["vcpus"] = ins_dict["flavor"]["vcpus"]
            data["ram"] = ins_dict["flavor"]["ram"]
            data["disk"] = ins_dict["flavor"]["disk"]
            data["volumes"] = ins_dict["os-extended-volumes:volumes_attached"]
            data["mountpoint"] = ins_dict["OS-EXT-SRV-ATTR:root_device_name"]
            data["description"] = ins_dict["description"]
            data["task_state"] = ins_dict["OS-EXT-STS:task_state"]
            data["vm_state"] = ins_dict["OS-EXT-STS:vm_state"]
            if "os_type" in ins_dict["metadata"]:
                data["os_type"] = ins_dict["metadata"]["os_type"]
            else:
                data["os_type"] = ""
            
            # # 如果是空虚机 状态一直是构建中 不能操作
            # if data["status"] not in ["ERROR", "UNKNOWN"]:
            #     if "empty" in ins_dict["tags"]:
            #         data["task_state"] = "构建中"
            #         data["vm_state"] = "BUILD"
            #         data["status"] = "BUILD"
            #         data["addresses"] = []
            #         data["hostname"] = ""
            res.append(data)

        return res

    def openstack_get_softdeleted_instance_detail(self, search):
        if search:
            method = "/servers/detail?deleted=True&status=soft_deleted&name=%s" % search
        else:
            method = "/servers/detail?deleted=True&status=soft_deleted"
        method += "&not-tags=empty"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []

        tz_sh = tz.gettz('Asia/Shanghai')
        nowtime = datetime.now(tz=tz_sh).replace(microsecond=0)

        for ins_dict in d["servers"]:

            if ins_dict["status"] == "SOFT_DELETED":

                data = {}
                data["id"] = ins_dict["id"]
                data["name"] = ins_dict["name"]
                data["vcpus"] = ins_dict["flavor"]["vcpus"]
                data["ram"] = ins_dict["flavor"]["ram"]
                data["disk"] = ins_dict["flavor"]["disk"]
                data["hostname"] = ins_dict["OS-EXT-SRV-ATTR:host"]
                data["ip"] = []

                updatetime = ins_dict["updated"]
                uptime = datetime.strptime(updatetime[:10], '%Y-%m-%d')
                time = (nowtime.date() - uptime.date()).days

                deadline = settings.RECYCLE_DAYS - 1 - time

                if deadline > 1:
                    data["deadline"] = deadline
                else:
                    data["deadline"] = 1

                addresses = ins_dict["addresses"]
                if len(addresses) > 0:
                    for subnet in addresses:
                        # data["ip"] = "%s%s," % (data["ip"], ins_dict["addresses"][subnet][0]["addr"])
                        data["ip"].append(ins_dict["addresses"][subnet][0])

                res.append(data)

        return res

    def openstack_get_server_detail(self, id):
        method = "/servers/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        print(d)
        data = {}
        if d.get("server", None):
            data["id"] = d["server"].get("id", "")
            data["name"] = d["server"].get("name", "")
            data["hostname"] = d["server"].get("OS-EXT-SRV-ATTR:host", "")
            data["status"] = d["server"].get("status", "")
            # data["imageid"] = d["server"].get("image", {"id":""}).get("id", "")
            data["flavorid"] = d["server"].get("flavor", {"id": ""}).get("id", "")
            # d["server"]["OS-EXT-STS:task_state"]
            data["task_state"] = d["server"].get("OS-EXT-STS:task_state", "")

            # data["volumeid"] = d["server"].get("os-extended-volumes:volumes_attached", [{"id":""}])[0]["id"]
            volumes = d["server"].get("os-extended-volumes:volumes_attached", {"id": []})
            if len(volumes) > 0:
                data["volumeid"] = volumes[0]["id"]
            else:
                data["volumeid"] = ""

            if len(volumes) > 1:
                data["isovolumeid"] = volumes[1]["id"]

            data["ip"] = ""
            addresses = d["server"]["addresses"]
            if len(addresses) > 0:
                for subnet in addresses:
                    data["ip"] = "%s%s," % (data["ip"], d["server"]["addresses"][subnet][0]["addr"])

            data["ip"] = data["ip"].rstrip(',')

        return data

    def openstack_get_server_detail_v2(self, id):
        method = "/servers/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        print(d)
        data = {}
        if d.get("server", None):
            data["id"] = d["server"].get("id", "")
            data["name"] = d["server"].get("name", "")
            data["hostname"] = d["server"].get("OS-EXT-SRV-ATTR:host", "")
            data["status"] = d["server"].get("status", "")
            # data["imageid"] = d["server"].get("image", {"id":""}).get("id", "")
            data["flavorid"] = d["server"].get("flavor", {"id": ""}).get("id", "")
            # d["server"]["OS-EXT-STS:task_state"]
            data["task_state"] = d["server"].get("OS-EXT-STS:task_state", "")

            # data["volumeid"] = d["server"].get("os-extended-volumes:volumes_attached", [{"id":""}])[0]["id"]
            volumes = d["server"].get("os-extended-volumes:volumes_attached", {"id": []})
            if len(volumes) > 0:
                data["volumeid"] = volumes[0]["id"]
            else:
                data["volumeid"] = ""

            if len(volumes) > 1:
                data["isovolumeid"] = volumes[1]["id"]

            data["ip"] = ""
            addresses = d["server"]["addresses"]
            if len(addresses) > 0:
                for subnet in addresses:
                    data["ip"] = "%s%s," % (data["ip"], d["server"]["addresses"][subnet][0]["addr"])

            data["ip"] = data["ip"].rstrip(',')

            data["addresses"] = addresses
        return data

    def openstack_get_server_detail_for_iso(self, id):
        method = "/servers/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        print(d)
        data = {}
        if d.get("server", None):
            data["id"] = d["server"].get("id", "")
            data["name"] = d["server"].get("name", "")
            data["hostname"] = d["server"].get("OS-EXT-SRV-ATTR:host", "")
            data["status"] = d["server"].get("status", "")
            # data["imageid"] = d["server"].get("image", {"id":""}).get("id", "")
            data["flavorid"] = d["server"].get("flavor", {"id": ""}).get("id", "")
            # d["server"]["OS-EXT-STS:task_state"]
            data["task_state"] = d["server"].get("OS-EXT-STS:task_state", "")

            # data["volumeid"] = d["server"].get("os-extended-volumes:volumes_attached", [{"id":""}])[0]["id"]
            volumes = d["server"].get("os-extended-volumes:volumes_attached", {"id": []})
            if len(volumes) > 0:
                data["volumeid"] = volumes[0]["id"]
            else:
                data["volumeid"] = ""

            if len(volumes) > 1:
                data["isovolumeids"] = [v["id"] for v in volumes][1:]
                data["isovolumeid"] = volumes[1]["id"]

            data["ip"] = ""
            addresses = d["server"]["addresses"]
            if len(addresses) > 0:
                for subnet in addresses:
                    data["ip"] = "%s%s," % (data["ip"], d["server"]["addresses"][subnet][0]["addr"])

            data["ip"] = data["ip"].rstrip(',')

        return data

    def openstack_create_server(self, provider):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        print(provider)

        if provider.__dict__["ipv4"]:
            temp = """{
                "server": {
                    "name": "$name",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "availability_zone": "$availability_zone",
                    "networks": [{
                        "port" : "$ipv4"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }] 
                }
            }"""
        else:
            temp = """{
                "server": {
                    "name": "$name",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "min_count": 1,
                    "max_count": 1,
                    "availability_zone": "$availability_zone",
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "uuid" : "$networkRef"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }] 
                }
            }"""

        t = Template(temp)
        body = t.substitute(provider.__dict__)
        dd = json.dumps(json.loads(body))

        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        print("hhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh")
        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_create_server_from_snapshot(self, form):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        if form.__dict__["ipv4"]:
            temp = """{
                "server": {
                    "name": "$name",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "min_count": 1,
                    "max_count": 1,
                    "availability_zone": "$availability_zone",
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "port" : "$ipv4"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "snapshot",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }] 
                }
            }"""
        else:
            temp = """{
                "server": {
                    "name": "$name",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "min_count": 1,
                    "max_count": 1,
                    "availability_zone": "$availability_zone",
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "uuid" : "$networkRef"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "snapshot",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }] 
                }
            }"""

        t = Template(temp)
        body = t.substitute(form.__dict__)

        dd = json.dumps(json.loads(body))

        r = requests.post(url, data=dd, headers=headers)

        print("从快照创建虚拟机返回结果：", r)
        d = json.loads(r.text)
        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    @openstack_update_volume_name
    def openstack_create_server_from_snapshot_v2(self, form):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "server": {
                    "name": "${name}",
                    "imageRef": "${imageRef}",
                    "flavorRef": "${flavorRef}",
                    "min_count": 1,
                    "max_count": 1,
                    "availability_zone": "${availability_zone}",
                    ${metadata},
                    ${networks},
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "${uuid}",
                        "source_type": "snapshot",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }] 
                }
            }"""

        if form.ipv4 != "":
            networks = '"networks" : [{ "port" : "%s" }]' % form.ipv4
        else:
            networks = '"networks" : [{ "uuid" : "%s" }]' % form.networkRef

        if form.os_type != "":
            metadata = '"metadata" : { "os_type" : "%s" }' % form.os_type
        else:
            metadata = ""

        t = Template(temp).substitute(name=form.name, imageRef=form.imageRef,
                                      flavorRef=form.flavorRef, availability_zone=form.availability_zone,
                                      metadata=metadata, networks=networks, uuid=form.uuid)
        # print(t)
        dd = json.dumps(json.loads(t))

        r = requests.post(url, data=dd, headers=headers)

        print("从快照创建虚拟机返回结果：", r)
        d = json.loads(r.text)
        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_delete_server(self, server_id):
        method = "/servers/%s" % server_id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)
        print("============软删除===============")
        print(r.status_code)
        print("===========================")
        if r.status_code == 204:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_server_snapshot(self, form):
        method = "/servers/%s/action" % form.id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "createImage": {
                    "name": "$name"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"name": form.name})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_server_action(self, instanceactionform):
        global detail
        method = "/servers/%s/action" % instanceactionform.id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        if instanceactionform.action == "start":
            temp = """{
                "os-start": null
            }"""
        elif instanceactionform.action == "stop":
            temp = """{
                "os-stop": null
            }"""
        elif instanceactionform.action == "suspend":
            temp = """{
                "suspend": null
            }"""
        elif instanceactionform.action == "resume":
            temp = """{
                "resume": null
            }"""
        elif instanceactionform.action == "restore":
            temp = """{
                "restore": null
            }"""
        elif instanceactionform.action == "forceDelete":
            temp = """{
                "forceDelete": null
            }"""
        elif instanceactionform.action == "reboot":
            temp = """{
                "reboot": {
                    "type": "HARD"
                }
            }"""
        elif instanceactionform.action == "resize":
            temp = """{
                "resize": {
                    "flavorRef": "$data",
                    "OS-DCF:diskConfig": "AUTO"
                }
            }"""
        elif instanceactionform.action == "live_migrate":
            temp = """{
                "os-migrateLive": {
                    "host": "$data",
                    "block_migration": false,
                    "disk_over_commit": false
                }
            }"""
        elif instanceactionform.action == "migrate":
            temp = """{
                "migrate": {
                    "host": "$data"
                }
            }"""
        elif instanceactionform.action == "confirm":
            temp = """{
                "confirmResize": null
            }"""
        elif instanceactionform.action == "revert":
            temp = """{
                "revertResize": null
            }"""

        elif instanceactionform.action == "getVNCConsole":
            temp = """{
                "os-getVNCConsole": {
                    "type": "novnc"
                }
            }"""

        elif instanceactionform.action == "getSPICEConsole":
            temp = """{
                "os-getSPICEConsole": {
                    "type": "spice-html5"
                }
            }"""
        elif instanceactionform.action == "shelve":
            temp = """{
                "shelve": null
            }"""
        elif instanceactionform.action == "unshelve":
            temp = """{
                "unshelve": {
                    "availability_zone": null
                }
            }"""
        msg = "ok"

        # if hasattr(instanceactionform, 'volume_status'):
        #     if not instanceactionform.volume_status:
        #         # found volume id and delete
        #         client = Client()
        #         detail = client.NovaClient.openstack_get_server_detail(client, instanceactionform.id)

        print(temp)
        t = Template(temp)
        if instanceactionform.data == "thxh":
            body = t.substitute()
        else:
            body = t.substitute({"data": instanceactionform.data})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        # if hasattr(instanceactionform, 'volume_status'):
        #     if not instanceactionform.volume_status:
        #         # found volume id and delete
        #          client = Client()
        #
        #          res = client.VolumeClient.openstack_delete_volume(client, detail["volumeid"])
        #          if res.get("msg") == "ok":
        #              msg = "虚拟机与云硬盘删除成功"

        if r.status_code == 202:
            return {"msg": msg}
        elif r.status_code == 204:
            return {"msg": msg}
        elif r.status_code == 200:
            d = json.loads(r.content)
            return {"msg": msg, "console": d["console"]["url"]}
        else:
            return {"msg": "error"}

    def openstack_server_action_v2(self, instanceactionform):
        id = instanceactionform.get("id", "")
        action = instanceactionform.get("action", "")
        data = instanceactionform.get("data", "")
        global detail
        method = "/servers/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        if action == "start":
            temp = """{
                "os-start": null
            }"""
        elif action == "stop":
            temp = """{
                "os-stop": null
            }"""
        elif action == "suspend":
            temp = """{
                "suspend": null
            }"""
        elif action == "resume":
            temp = """{
                "resume": null
            }"""
        elif action == "restore":
            temp = """{
                "restore": null
            }"""
        elif action == "forceDelete":
            temp = """{
                "forceDelete": null
            }"""
        elif action == "reboot":
            temp = """{
                "reboot": {
                    "type": "HARD"
                }
            }"""
        elif action == "resize":
            temp = """{
                "resize": {
                    "flavorRef": "$data",
                    "OS-DCF:diskConfig": "AUTO"
                }
            }"""
        elif action == "live_migrate":
            temp = """{
                "os-migrateLive": {
                    "host": "$data",
                    "block_migration": false,
                    "disk_over_commit": false
                }
            }"""
        elif action == "migrate":
            temp = """{
                "migrate": {
                    "host": "$data"
                }
            }"""
        elif action == "confirm":
            temp = """{
                "confirmResize": null
            }"""
        elif action == "revert":
            temp = """{
                "revertResize": null
            }"""

        elif action == "getVNCConsole":
            temp = """{
                "os-getVNCConsole": {
                    "type": "novnc"
                }
            }"""

        elif action == "getSPICEConsole":
            temp = """{
                "os-getSPICEConsole": {
                    "type": "spice-html5"
                }
            }"""
        msg = "ok"

        # if hasattr(instanceactionform, 'volume_status'):
        #     if not instanceactionform.volume_status:
        #         # found volume id and delete
        #         client = Client()
        #         detail = client.NovaClient.openstack_get_server_detail(client, instanceactionform.id)

        print(temp)
        t = Template(temp)
        if data == "thxh":
            body = t.substitute()
        else:
            body = t.substitute({"data": data})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        # if hasattr(instanceactionform, 'volume_status'):
        #     if not instanceactionform.volume_status:
        #         # found volume id and delete
        #          client = Client()
        #
        #          res = client.VolumeClient.openstack_delete_volume(client, detail["volumeid"])
        #          if res.get("msg") == "ok":
        #              msg = "虚拟机与云硬盘删除成功"
        print("=============硬删除==============")
        print(r.status_code)
        print("===========================")
        if r.status_code == 202:
            return {"msg": msg}
        elif r.status_code == 204:
            return {"msg": msg}
        elif r.status_code == 200:
            d = json.loads(r.content)
            return {"msg": msg, "console": d["console"]["url"]}
        else:
            return {"msg": "error"}

    def openstack_server_shelve(self, vm_id):
        method = "/servers/%s/action" % vm_id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "shelve": null
        }"""

        msg = "ok"

        print(temp)
        t = Template(temp)

        body = t.substitute()

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": msg}
        else:
            return {"msg": "error", "code": r.status_code}

    def openstack_server_unshelve(self, vm_id):
        method = "/servers/%s/action" % vm_id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.91"
        }
        temp = """{
                   "unshelve": {
                       "availability_zone": null
                   }
        }"""

        msg="ok"

        print(temp)
        t = Template(temp)
        body = t.substitute()

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": msg}
        else:
            return {"msg": "error", "code": r.status_code}


    def openstack_server_vm_set_status_error(self, vmid):

        method = "/servers/%s/action" % vmid
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "Content-Type": "application/json",
            "X-Auth-Token": self.token
        }


        body =  {
            "os-resetState": {
                "state": "error"
            }
        }


        msg = "ok"

        
        r = requests.post(url, json=body, headers=headers)
        print(r.text)


        if r.status_code == 202:
            return {"msg": msg}
        elif r.status_code == 204:
            return {"msg": msg}
        elif r.status_code == 200:
            d = json.loads(r.content)
            return {"msg": msg, "console": d["console"]["url"]}
        else:
            return {"msg": "error"}


    def openstack_add_server_tag(self, id, tag):
        method = "/servers/%s/tags/%s" % (id, tag)
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.put(url, headers=headers)

        # d = json.loads(r.text)

        if r.status_code == 201:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_delete_server_all_tag(self, vmid):
        method = "/servers/%s/tags" % vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.delete(url, headers=headers)

        if r.status_code == 204:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_edit_server(self, instanceeditform):
        method = "/servers/%s" % instanceeditform.id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "server": {
                    "name": "$name"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"name": instanceeditform.name})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=ServerRes, data=d["server"])
        return ins.__dict__

    def openstack_edit_server_v2(self, instanceeditform):
        id = instanceeditform.get("id", "")
        name = instanceeditform.get("name", "")

        method = "/servers/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "server": {
                    "name": "$name"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=ServerRes, data=d["server"])
        return ins.__dict__

    def openstack_edit_iso_server(self, vmid, description):
        method = "/servers/%s" % vmid
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        temp = """{
                "server": {
                    "description": "$desc"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"desc": description})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        print(r.status_code)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_update_server_name(self, vmid, vmname):
        method = "/servers/%s" % vmid
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }
        """
        headers = {
            "X-Auth-Token": self.token
        }
        """
        temp = """{
                 "server": {"name": "$vmname"}
                
            }"""

        t = Template(temp)
        body = t.substitute({"vmname": vmname})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        print(r.status_code)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_create_server_with_iso(self, form):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        if form.__dict__["ipv4"]:
            temp = """{
                "server": {
                    "name": "$name",
                    "description": "iso",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "availability_zone": "$availability_zone",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "port" : "$ipv4"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    
                    {
                        "boot_index": "1",
                        "uuid": "$uuid2",
                        "source_type": "volume",
                        "device_type": "cdrom",
                        "disk_bus": "ide",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }
                    
                    ] 
                }
            }"""
        else:
            temp = """{
                "server": {
                    "name": "$name",
                    "description": "iso",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "availability_zone": "$availability_zone",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "uuid" : "$networkRef"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    {
                        "boot_index": "1",
                        "uuid": "$uuid2",
                        "source_type": "volume",
                        "device_type": "cdrom",
                        "disk_bus": "ide",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }
                    ] 
                }
            }"""

        t = Template(temp)
        body = t.substitute(form.__dict__)

        dd = json.dumps(json.loads(body))
        print("创建虚拟机返回结果：")
        print(dd)

        r = requests.post(url, data=dd, headers=headers)

        print(r.status_code)

        d = json.loads(r.text)

        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_create_server_with_iso_arm(self, form):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        if form.__dict__["ipv4"]:
            temp = """{
                "server": {
                    "name": "$name",
                    "description": "iso",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "availability_zone": "$availability_zone",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "port" : "$ipv4"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "1",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    {
                        "boot_index": "0",
                        "uuid": "$uuid2",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }
                    
                    ] 
                }
            }"""
        else:
            temp = """{
                "server": {
                    "name": "$name",
                    "description": "iso",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "availability_zone": "$availability_zone",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "uuid" : "$networkRef"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "1",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    {
                        "boot_index": "0",
                        "uuid": "$uuid2",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }
                    ] 
                }
            }"""

        t = Template(temp)
        body = t.substitute(form.__dict__)

        dd = json.dumps(json.loads(body))
        print("创建虚拟机返回结果：")
        print(dd)

        r = requests.post(url, data=dd, headers=headers)

        print(r.status_code)

        d = json.loads(r.text)

        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_create_server_with_iso_driver(self, form):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        if form.__dict__["ipv4"]:
            temp = """{
                "server": {
                    "name": "$name",
                    "description": "iso",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "availability_zone": "$availability_zone",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "port" : "$ipv4"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    
                    {
                        "boot_index": "1",
                        "uuid": "$uuid2",
                        "source_type": "volume",
                        "device_type": "cdrom",
                        "disk_bus": "ide",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    {
                        "boot_index": "2",
                        "uuid": "$uuid_driver",
                        "source_type": "volume",
                        "device_type": "cdrom",
                        "disk_bus": "ide",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }
                    
                    ] 
                }
            }"""
        else:
            temp = """{
                "server": {
                    "name": "$name",
                    "description": "iso",
                    "imageRef": "$imageRef",
                    "flavorRef": "$flavorRef",
                    "availability_zone": "$availability_zone",
                    "min_count": 1,
                    "max_count": 1,
                    "metadata" : {
                        "os_type" : "$os_type"
                    },
                    "networks": [{
                        "uuid" : "$networkRef"
                    }],
                    "block_device_mapping_v2": [{
                        "boot_index": "0",
                        "uuid": "$uuid",
                        "source_type": "volume",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    
                    {
                        "boot_index": "1",
                        "uuid": "$uuid2",
                        "source_type": "volume",
                        "device_type": "cdrom",
                        "disk_bus": "ide",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    },
                    {
                        "boot_index": "2",
                        "uuid": "$uuid_driver",
                        "source_type": "volume",
                        "device_type": "cdrom",
                        "disk_bus": "ide",
                        "destination_type": "volume",
                        "delete_on_termination": false
                    }
                    
                    ] 
                }
            }"""

        t = Template(temp)
        body = t.substitute(form.__dict__)
        print("创建虚拟机创建BODY：")
        print(body)
        dd = json.dumps(json.loads(body))

        print(dd)

        r = requests.post(url, data=dd, headers=headers)

        print(r.status_code)

        d = json.loads(r.text)

        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_fast_create_server(self, params):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.52"
        }
        print(params)
        # 直接构造一个字典而不是使用模板字符串
        server_data = {
            "server": {
                "name": params['name'],
                # "imageRef": params['imageRef'],
                "flavorRef": params['flavorRef'],
                # "availability_zone": params['availability_zone'],
                "block_device_mapping_v2": [
                    {
                        "boot_index": "0",
                        "source_type": "blank",
                        "destination_type": "volume",
                        "volume_size": 1,  # 指定大小，单位为GB
                        "delete_on_termination": True,
                    }
                ],
                "tags": ["empty"]
            }
        }

        # 添加metadata和networks
        if params.get('os_type'):
            server_data['server']['metadata'] = {"os_type": params['os_type']}

        # if params.get('ipv4'):
        #     server_data['server']['networks'] = [{"port": params['ipv4']}]
        # else:
        server_data['server']['networks'] = [{"uuid": params.get('networkRef', '')}]
        # if params.get('iso'):
        #     server_data['server']["block_device_mapping_v2"] = [
        #                                                         # {
        #                                                         #     "boot_index": "0",
        #                                                         #     "source_type": "volume",
        #                                                         #     "destination_type": "volume",
        #                                                         #     "delete_on_termination": False
        #                                                         # },
        #
        #                                                         {
        #                                                             "boot_index": "0",
        #                                                             "source_type": "volume",
        #                                                             "device_type": "cdrom",
        #                                                             "disk_bus": "ide",
        #                                                             "destination_type": "volume",
        #                                                             "delete_on_termination": False,
        #                                                             "no_device": True,
        #                                                         },
        #                                                         {
        #                                                             "boot_index": "1",
        #                                                             "source_type": "volume",
        #                                                             "device_type": "cdrom",
        #                                                             "disk_bus": "ide",
        #                                                             "destination_type": "volume",
        #                                                             "delete_on_termination": False,
        #                                                             "no_device": True,
        #                                                         }
        #                                                         ]

        # 使用json.dumps生成JSON字符串
        dd = json.dumps(server_data)

        print("报文:", dd)
        r = requests.post(url, data=dd, headers=headers)

        print("fast创建虚拟机返回结果：", r)
        d = json.loads(r.text)
        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_fast_create_server_v2(self, params):
        method = "/servers"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.52"
        }
        print(params)
        # 直接构造一个字典而不是使用模板字符串
        server_data = {
            "server": {
                "name": params['name'],
                "imageRef": params['imageRef'],
                "flavorRef": params['flavorRef'],
                "availability_zone": params['availability_zone'],
                "block_device_mapping_v2": [
                    {
                        "boot_index": "0",
                        "source_type": "blank",
                        "destination_type": "volume",
                        "volume_size": 1,  # 指定大小，单位为GB
                        "delete_on_termination": True,
                    }
                ],
                "tags": ["empty"]
            }
        }

        # 添加metadata和networks
        if params.get('os_type'):
            server_data['server']['metadata'] = {"os_type": params['os_type']}

        # if params.get('ipv4'):
        #     server_data['server']['networks'] = [{"port": params['ipv4']}]
        # else:
        server_data['server']['networks'] = [{"uuid": params.get('networkRef', '')}]
        if params.get('iso'):
            server_data['server']["block_device_mapping_v2"] = [
                                                                {
                                                                    "boot_index": "0",
                                                                    "source_type": "blank",
                                                                    "destination_type": "volume",
                                                                    "volume_size": 1,
                                                                    "delete_on_termination": False,
                                                                },
                                                                {
                                                                    "boot_index": "1",
                                                                    "source_type": "blank",
                                                                    "device_type": "cdrom",
                                                                    "disk_bus": "ide",
                                                                    "destination_type": "volume",
                                                                    "volume_size": 1,
                                                                    "delete_on_termination": False,
                                                                },
                                                                {
                                                                    "boot_index": "2",
                                                                    "source_type": "blank",
                                                                    "device_type": "cdrom",
                                                                    "disk_bus": "ide",
                                                                    "destination_type": "volume",
                                                                    "volume_size": 1,
                                                                    "delete_on_termination": False,
                                                                }
                                                                ]

        # 使用json.dumps生成JSON字符串
        dd = json.dumps(server_data)

        print("报文:", dd)
        r = requests.post(url, data=dd, headers=headers)

        print("fast创建虚拟机返回结果：", r)
        d = json.loads(r.text)
        print(d)

        ins = from_dict(data_class=ServerRes, data=d["server"])

        return ins.__dict__

    def openstack_getpost_server_detail(self, id):
        method = "/servers/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        print(r.text)
        d = json.loads(r.text)
        print(d)
        if d.get("itemNotFound"):
            return {"name": ""}

        ins = from_dict(data_class=InstanceGetDetail, data=d["server"])

        created = datetime.strptime(ins.__dict__["created"], '%Y-%m-%dT%H:%M:%SZ') + timedelta(hours=8)
        updated = datetime.strptime(ins.__dict__["updated"], '%Y-%m-%dT%H:%M:%SZ') + timedelta(hours=8)
        ins.__dict__["created"] = created.strftime("%Y年%m月%d日 %H:%M:%S")
        ins.__dict__["updated"] = updated.strftime("%Y年%m月%d日 %H:%M:%S")

        ins.__dict__["task_state"] = d["server"]["OS-EXT-STS:task_state"]

        ins.__dict__["volume_id"] = []
        for volume in d["server"]["os-extended-volumes:volumes_attached"]:
            ins.__dict__["volume_id"].append(volume["id"])

        ins.__dict__["addr"] = ""
        ips = []
        for subnet in ins.__dict__["addresses"]:
            for sub in ins.__dict__["addresses"][subnet]:
                ips.append(sub["addr"])
            # ins.__dict__["addr"] = "%s%s," % (ins.__dict__["addr"],ins.__dict__["addresses"][subnet][0]["addr"])

        # ins.__dict__["addr"] = ins.__dict__["addr"].rstrip(',')
        ins.__dict__["addr"] = ",".join(ips)
        ins.__dict__["image"] = d["server"]["image"]
        ins.__dict__["metadata"] = d["server"]["metadata"]
        return ins.__dict__

    def openstack_detach_volume_from_server(self, vmid, volumeid):
        method = "/servers/%s/os-volume_attachments/%s" % (vmid, volumeid)

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_get_volume_attachment(self, vmid):
        method = "/servers/%s/os-volume_attachments" % (vmid)
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        return d

    def openstack_update_volume_attachment(self, vmid, volumeid, device):
        method = "/servers/%s/os-volume_attachments/%s" % (vmid, volumeid)

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"

        }

        temp = """{
                "volumeAttachment": {
                    "volumeId":"$volumeid",
                    "device": "$mountpoint"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"volumeid": volumeid, "mountpoint": device})

        dd = json.dumps(json.loads(body))

        r = requests.put(url, data=dd, headers=headers)

        if r.status_code == 204:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_attach_volume_to_server(self, form):
        method = "/servers/%s/os-volume_attachments" % form.vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "volumeAttachment": {
                    "volumeId": "$volumeid"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"volumeid": form.id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error", "error": d}

    def openstack_attach_volume_to_server(self, form):
        method = "/servers/%s/os-volume_attachments" % form.vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "volumeAttachment": {
                    "volumeId": "$volumeid"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"volumeid": form.id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error", "error": d}

    def openstack_attach_volume_to_server_v2(self, form):
        vmid = form.get("vmid", "")
        id = form.get("id", "")
        method = "/servers/%s/os-volume_attachments" % vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "volumeAttachment": {
                    "volumeId": "$volumeid"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"volumeid": id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_attach_volume_to_server_with_device(self, form):
        vmid = form.get("vmid", "")
        id = form.get("id", "")
        device = form.get("device", "")
        method = "/servers/%s/os-volume_attachments" % vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "volumeAttachment": {
                    "volumeId": "$volumeid",
                    "device": "$device"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"volumeid": id, "device": device})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error", "error": d}

    def openstack_attach_volume_to_server_with_device_v2(self, form):
        vmid = form.get("vmid", "")
        id = form.get("volumeid", "")
        device = form.get("device", "")
        method = "/servers/%s/os-volume_attachments" % vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                   "volumeAttachment": {
                       "volumeId": "$volumeid",
                       "device": "$device"
                   }
               }"""

        t = Template(temp)
        body = t.substitute({"volumeid": id, "device": device})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error", "error": d}

    def openstack_update_attach_volume_to_server_with_device(self, form):
        vmid = form.get("vmid", "")
        id = form.get("volumeid", "")
        new_id = form.get("new_volume_id", "")
        device = form.get("device", "")
        method = "/servers/%s/os-volume_attachments/%s" % (vmid, id)

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        temp = """{
                   "volumeAttachment": {
                       "volumeId": "$volumeid",
                       "delete_on_termination": true
                   }
               }"""

        t = Template(temp)
        body = t.substitute({"volumeid": new_id})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            return {"msg": "error", "error": d}

    def openstack_get_all_compute_services(self):
        method = "/os-services"
        url = "%s/%s%s" % (self.nova_uri, self.tenant_id, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["services"]:
            ins = from_dict(data_class=ComputeServiceFrom, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_get_cleanup_list(self, days):
        tz_sh = tz.gettz('Asia/Shanghai')
        nowtime = datetime.now(tz=tz_sh).replace(microsecond=0)
        method = "/servers/detail?status=SHUTOFF"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["servers"]:
            ins = from_dict(data_class=InstanceGetDetail, data=ins_dict)
            updatetime = ins_dict["updated"]

            ins.__dict__["ip"] = ""
            for net in ins_dict["addresses"]:
                print(ins_dict["addresses"][net])
                for subnet in ins_dict["addresses"][net]:
                    ins.__dict__["ip"] = "%s%s," % (ins.__dict__["ip"], subnet["addr"])

            ins.__dict__["ip"] = ins.__dict__["ip"].rstrip(',')
            ins.__dict__["hostname"] = ins_dict["OS-EXT-SRV-ATTR:host"]

            uptime = datetime.strptime(updatetime[:10], '%Y-%m-%d')

            time = (nowtime.date() - uptime.date()).days
            if time >= int(days):
                ins.__dict__["days"] = time
                res.append(ins.__dict__)

        return res

    def openstack_attach_volume_test(self, form):
        method = "/servers/%s/os-volume_attachments" % form.vmid

        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "volumeAttachment": {
                    "volumeId": "$volumeid",
                    "device": "$mountpoint"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"volumeid": form.volumeid, "mountpoint": form.mountpoint})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_server_update_to_image(self, vm_id, name):
        headers = {
            "X-Auth-Token": self.token
        }

        method = "/servers/%s/action" % vm_id
        temp = """{
            "createImage": {
                "name": "$name"
            }
        }"""

        url = "%s%s" % (self.nova_uri, method)
        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_get_server_ports(self, id):
        method = "/servers/%s/os-interface" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        res = []
        for interface in d["interfaceAttachments"]:
            interface_detail = from_dict(data_class=InterfaceDetail, data=interface)
            res.append(interface_detail.__dict__)

        return res

    def openstack_detach_interface(self, server_id, port_id):
        method = "/servers/%s/os-interface/%s" % (server_id, port_id)
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)

        if r.status_code == 202:
            return "ok"
        else:
            return "error"

    def openstack_create_interface(self, vm_id, port_id):
        headers = {
            "X-Auth-Token": self.token
        }

        method = "/servers/%s/os-interface" % vm_id
        temp = """{
            "interfaceAttachment": {
                "port_id": "$portid"
            }
        }"""

        url = "%s%s" % (self.nova_uri, method)
        t = Template(temp)
        body = t.substitute({"portid": port_id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 200:
            return {"msg": "200"}
        elif r.status_code == 409:
            return {"msg": "409"}
        else:
            return {"msg": "error"}

    def openstack_evacuate_vm(self, server_id):
        headers = {
            "X-Auth-Token": self.token
        }
        on_shared_storage = "True"
        method = "/servers/%s/action" % server_id
        temp = """{
            "evacuate": {
                "onSharedStorage": "True"
            }
        }"""

        url = "%s%s" % (self.nova_uri, method)
        t = Template(temp)
        body = t.substitute()

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 200:
            res = json.loads(r.text)
            res["msg"] = "ok"
            return res

        return {"msg": "error"}

    def openstack_force_down_host(self, host):
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.11"
        }
        # method = "/os-services/%s" % server_id
        method = "/os-services/force-down"
        temp = """{
            "host": "$host",
            "binary": "nova-compute",
            "forced_down": true
        }"""

        url = "%s%s" % (self.nova_uri, method)
        t = Template(temp)
        body = t.substitute({"host": host})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        print(r.text)
        if r.status_code == 200:
            res = json.loads(r.text)
            res["msg"] = "ok"
            return res

        return {"msg": "error"}

    def openstack_enable_host(self, host):
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.11"
        }
        # method = "/os-services/%s" % server_id
        method = "/os-services/force-down"
        temp = """{
            "host": "$host",
            "binary": "nova-compute",
            "forced_down": false
        }"""

        url = "%s%s" % (self.nova_uri, method)
        t = Template(temp)
        body = t.substitute({"host": host})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        print(r.text)
        if r.status_code == 200:
            res = json.loads(r.text)
            res["msg"] = "ok"
            return res

        return {"msg": "error"}

    def openstack_nova_services(self):
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.11"
        }
        method = "/os-services"
        url = "%s%s" % (self.nova_uri, method)
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            res = json.loads(r.text)
            res["msg"] = "ok"
            return res

        return {"msg": "error"}

    def openstack_put_metadata(self, form):
        method = "/servers/%s/metadata/%s" % (form["vm_id"], "os_type")
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88",
            'Content-Type': 'application/json'
        }

        data = {
            'meta': {
                "os_type": form["os_type"],
            }
        }

        r = requests.put(url, data=json.dumps(data), headers=headers)

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            print(r.json())
            return {"msg": "error"}

    def openstack_get_metadata(self, form):
        print(form)
        method = "/servers/%s/metadata/%s" % (form["vm_id"], "os_type")
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        if r.status_code == 200:
            # 解析 JSON 响应
            data = r.json()

            # 获取元数据项的值
            meta_value = data.get('meta', {}).get("os_type")
            print(meta_value)
            return {"msg": "ok"}
        else:
            print(r.json())
            return {"msg": "error"}

    # 获取虚拟机tag列表
    def openstack_get_vm_tags(self, vm_id):
        method = "/servers/%s/tags" % vm_id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            res = json.loads(r.text)
        else:

            res = {"msg": "error"}
        return res

    # 设置虚拟机单个tag
    def openstack_set_vm_single_tag(self, form):
        method = "/servers/%s/tags/%s" % (form['vm_id'], form['tag'])
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.put(url, headers=headers)
        if r.status_code == 201:
            return {"msg": "ok"}
        elif r.status_code == 204:
            return {"msg": "已指定该tag"}
        else:
            return {"msg": "error"}

    # 虚拟机替换指定tag
    def openstack_replace_vm_single_tag(self, form):
        method = "/servers/%s/tags" % (form['vm_id'])
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        param = {"tags": form["tags"]}
        r = requests.put(url, headers=headers, json=param)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    # 虚拟机删除指定tag
    def openstack_delete_vm_single_tag(self, form):
        method = "/servers/{}/tags/{}".format(form.get('vm_id', ''), form.get('tags', [''])[0])
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.delete(url, headers=headers)
        if r.status_code == 204:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

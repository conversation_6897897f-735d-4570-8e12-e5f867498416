import requests
import json
import settings
from api.model.volumes import Volume, VolumeDetail, VolumeSnapshotDetail, VolumeTypeDetail, VolumeDetailforlist, \
    VolumeAgent, VolumeType
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class VolumeClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_list(self):
        method = "/volumes/detail"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []

        for ins_dict in d["volumes"]:
            ins = from_dict(data_class=VolumeDetailforlist, data=ins_dict)
            if ins_dict["description"]:
                ins.__dict__["description"] = ins_dict["description"]
            res.append(ins.__dict__)
        return res

    def openstack_get_all_list_v2(self, params):
        method = "/volumes/detail"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers, params=params)

        d = json.loads(r.text)
        return d["volumes"]

    def openstack_get_all_detail_v2(self, params):
        method = "/volumes/detail"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers, params=params)

        d = json.loads(r.text)
        res = []

        for ins_dict in d["volumes"]:
            ins = from_dict(data_class=VolumeDetailforlist, data=ins_dict)
            if ins_dict["description"]:
                ins.__dict__["description"] = ins_dict["description"]
            res.append(ins.__dict__)
        return res

    def openstack_get_all_list_types(self):
        method = "/types"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["volume_types"]:
            ins = from_dict(data_class=VolumeTypeDetail, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_create_volume(self, volumecreateform):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$name",
                    "size": $size,
                    "imageRef": "$imageRef",
                    "description" : "$description"
                }
            }"""

        t = Template(temp)
        body = t.substitute(volumecreateform.__dict__)
        dd = json.dumps(json.loads(body))
        print("2222222222222")
        print(url)
        print(headers)
        print(dd)
        print("3333333333333")
        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        print("-----------------------------")
        print(d)
        ins = from_dict(data_class=VolumeDetailforlist, data=d["volume"])
        return ins.__dict__

    def openstack_create_volume_from_snap_id(self, volumecreateform):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$name",
                    "size": $size,
                    "snapshot_id": "$imageRef",
                    "description" : "$description"
                }
            }"""

        t = Template(temp)
        body = t.substitute(volumecreateform.__dict__)
        dd = json.dumps(json.loads(body))
        print("2222222222222")
        print(dd)
        print("3333333333333")
        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        print("-----------------------------")
        print(d)
        ins = from_dict(data_class=VolumeDetailforlist, data=d["volume"])
        return ins.__dict__

    def openstack_create_blank_volume(self, volumecreateform):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$name",
                    "size": $size,
                    "imageRef": null,
                    "description":"$description"
                }
            }"""

        t = Template(temp)
        body = t.substitute(volumecreateform.__dict__)

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=VolumeDetailforlist, data=d["volume"])
        return ins.__dict__

    def openstack_create_blank_volume_v2(self, volumecreateform):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$name",
                    "size": $size,
                    "imageRef": null,
                    "volume_type": "$volume_type",
                    "description":"$description"
                }
            }"""

        t = Template(temp)
        body = t.substitute(volumecreateform)

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=VolumeDetailforlist, data=d["volume"])
        return ins.__dict__

    def openstack_create_blank_volume_v3(self, volumecreateform):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$name",
                    "size": $size,
                    "imageRef": null,
                    "volume_type": "$volume_type",
                    "snapshot_id": "$snapshot_id",
                    "description":"$description"
                }
            }"""

        t = Template(temp)
        body = t.substitute(volumecreateform)

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=VolumeDetailforlist, data=d["volume"])
        return ins.__dict__

    def openstack_create_volume_by_type(self, type, size):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$type",
                    "size": $size,
                    "imageRef": null,
                    "volume_type": "$type",
                    "description":"thxh-passthrough"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"type": type, "size": size})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        if r.status_code == 202:
            return {"msg": "ok", "volumeid": d["volume"]["id"], "volume_status": d["volume"]["status"]}
        else:
            return {"msg": "error"}

    def openstack_create_volume_by_type_to_clone(self, volumecloneform):
        method = "/volumes"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                        "volume": {
                            "name": "$name",
                            "size": $size,
                            "source_volid": "$source_volid"
                        }
                    }"""

        t = Template(temp)
        body = t.substitute(volumecloneform.__dict__)
        print("json数据",json.loads(body))

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)
        print("创建返回结果：",d)
        ins = from_dict(data_class=VolumeDetailforlist, data=d["volume"])
        return ins.__dict__

    def openstack_update_volume_bootable(self, volumeid):
        method = "/volumes/%s/action" % volumeid
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "os-set_bootable": {
                    "bootable": "True"
                }
            }"""

        t = Template(temp)
        body = t.substitute()

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        return r.status_code

    def openstack_update_volume_bootable_status(self, volumeid, status):
        method = "/volumes/%s/action" % volumeid
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }
        if status:
            temp = """{
                   "os-set_bootable": {
                       "bootable": "True"
                   }
               }"""
        else:
            temp = """{
                   "os-set_bootable": {
                       "bootable": "False"
                   }
               }"""

        t = Template(temp)
        body = t.substitute()

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        return r.status_code

    def openstack_set_volume_image_metadata(self, volumeid):
        method = "/volumes/%s/action" % volumeid
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "os-set_image_metadata": {
                    "image_id": "Thxh1234",
                    "image_name": "vmware_migrate"
                }
            }"""

        t = Template(temp)
        body = t.substitute()

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        return r.status_code

    def openstack_delete_volume(self, id):
        method = "/volumes/%s" % id
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}
        elif r.status_code == 400:
            return {"msg": "snap"}
        else:
            return {"msg": "error"}

    def openstack_edit_volume(self, id, name):
        method = "/volumes/%s" % id
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume": {
                    "name": "$name"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=VolumeDetail, data=d["volume"])
        return ins.__dict__

    def openstack_volume_action(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient",
            "OpenStack-API-Version": "volume 3.62"
        }

        if form.action == "extend":
            method = "/volumes/%s/action" % form.id
            temp = """{
                "os-extend": {
                    "new_size": $data
                }
            }"""

        elif form.action == "retype":
            method = "/volumes/%s/action" % form.id
            temp = """{
                "os-retype": {
                    "new_type": "$data",
                    "migration_policy": "never"
                }
            }"""

        elif form.action == "upload":
            method = "/volumes/%s/action" % form.id
            temp = """{
                "os-volume_upload_image": {
                    "image_name": "$data",
                    "force": false,
                    "disk_format": "raw",
                    "container_format": "bare"
                }
            }"""

        elif form.action == "detach":
            data = form.data.split("@")
            method = "/volumes/%s/action" % form.id
            temp = """{
                "os-detach": {
                    "attachment_id": "$data"
                }
            }"""
            url = "%s%s" % (self.cinderv3_uri, method)
            t = Template(temp)
            body = t.substitute({"data": form.data[0]})
            dd = json.dumps(json.loads(body))
            r = requests.post(url, data=dd, headers=headers)

            if r.status_code == 202:
                return {"msg": "ok"}

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": form.data})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_action_v2(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient",
            "OpenStack-API-Version": "volume 3.62"
        }
        action = form.get("action", "")
        id = form.get("id", "")
        volume_data = form.get("data", "")

        if action == "extend":
            method = "/volumes/%s/action" % id
            temp = """{
                   "os-extend": {
                       "new_size": $data
                   }
               }"""

        elif action == "retype":
            method = "/volumes/%s/action" % id
            temp = """{
                   "os-retype": {
                       "new_type": "$data",
                       "migration_policy": "never"
                   }
               }"""

        elif action == "upload":
            method = "/volumes/%s/action" % id
            temp = """{
                   "os-volume_upload_image": {
                       "image_name": "$data",
                       "force": false,
                       "disk_format": "raw",
                       "container_format": "bare"
                   }
               }"""

        elif action == "detach":
            data = volume_data.split("@")
            method = "/volumes/%s/action" % id
            temp = """{
                   "os-detach": {
                       "attachment_id": "$data"
                   }
               }"""
            url = "%s%s" % (self.cinderv3_uri, method)
            t = Template(temp)
            body = t.substitute({"data": volume_data[0]})
            dd = json.dumps(json.loads(body))
            r = requests.post(url, data=dd, headers=headers)

            if r.status_code == 202:
                return {"msg": "ok"}

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": volume_data})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_snapshot(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        if form.action == "create":
            method = "/snapshots"
            temp = """{
                "snapshot": {
                    "name": "$data",
                    "volume_id": "$id",
                    "force": true
                }
            }"""

        elif form.action == "backup":
            method = "/backups"
            temp = """{
                "backup": {
                    "name": "$data",
                    "volume_id": "$id",
                    "container": null
                }
            }"""

        elif form.action == "edit":
            method = "/snapshots/%s" % form.id
            temp = """{
                "snapshot": {
                    "name": "$data"
                }
            }"""
            url = "%s%s" % (self.cinderv3_uri, method)
            t = Template(temp)
            body = t.substitute({"data": form.data})

            dd = json.dumps(json.loads(body))
            r = requests.put(url, data=dd, headers=headers)

            if r.status_code == 200:
                return {"msg": "ok"}

        elif form.action == "delete":
            method = "/snapshots/%s" % form.id
            url = "%s%s" % (self.cinderv3_uri, method)

            r = requests.delete(url, headers=headers)

            if r.status_code == 202:
                return {"msg": "ok"}


        elif form.action == "list":
            method = "/snapshots"
            url = "%s%s" % (self.cinderv3_uri, method)
            headers = {
                "X-Auth-Token": self.token
            }

            r = requests.get(url, headers=headers)

            d = json.loads(r.text)
            res = []
            for ins_dict in d["snapshots"]:
                ins = from_dict(data_class=VolumeSnapshotDetail, data=ins_dict)
                res.append(ins.__dict__)
            return res

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": form.data, "id": form.id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_snapshot_v2(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        id = form.get("id", "")
        action = form.get("action","")
        data = form.get("data", "")

        if action == "create":
            method = "/snapshots"
            temp = """{
                "snapshot": {
                    "name": "$data",
                    "volume_id": "$id",
                    "force": true
                }
            }"""

        elif action == "backup":
            method = "/backups"
            temp = """{
                "backup": {
                    "name": "$data",
                    "volume_id": "$id",
                    "container": null
                }
            }"""

        elif action == "edit":
            method = "/snapshots/%s" % id
            temp = """{
                "snapshot": {
                    "name": "$data"
                }
            }"""
            url = "%s%s" % (self.cinderv3_uri, method)
            t = Template(temp)
            body = t.substitute({"data": data})

            dd = json.dumps(json.loads(body))
            r = requests.put(url, data=dd, headers=headers)

            if r.status_code == 200:
                return {"msg": "ok"}

        elif action == "delete":
            method = "/snapshots/%s" % id
            url = "%s%s" % (self.cinderv3_uri, method)

            r = requests.delete(url, headers=headers)

            if r.status_code == 202:
                return {"msg": "ok"}


        elif action == "list":
            method = "/snapshots"
            url = "%s%s" % (self.cinderv3_uri, method)
            headers = {
                "X-Auth-Token": self.token
            }

            r = requests.get(url, headers=headers)

            d = json.loads(r.text)
            res = []
            for ins_dict in d["snapshots"]:
                ins = from_dict(data_class=VolumeSnapshotDetail, data=ins_dict)
                res.append(ins.__dict__)
            return res

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": data, "id": id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_snapshot_list(self):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        method = "/snapshots"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["snapshots"]:
            ins = from_dict(data_class=VolumeSnapshotDetail, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_volume_snapshot_delete(self, id):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        method = "/snapshots/%s" % id
        url = "%s%s" % (self.cinderv3_uri, method)

        r = requests.delete(url, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_snapshot_edit(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        method = "/snapshots/%s" % form["id"]
        temp = """{
            "snapshot": {
                "name": "$data"
            }
        }"""
        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": form["data"]})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        if r.status_code == 200:
            return {"msg": "ok"}

    def openstack_volume_snapshot_backup(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        method = "/backups"
        temp = """{
            "backup": {
                "name": "$data",
                "volume_id": "$id",
                "container": null
            }
        }"""

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": form.data, "id": form.id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_snapshot_create(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        method = "/snapshots"
        temp = """{
            "snapshot": {
                "name": "$data",
                "description":"$volumename",
                "volume_id": "$id",
                "force": true
            }
        }"""

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": form["snapname"], "id": form["id"], "volumename": form["volumename"]})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            d = json.loads(r.text)
            return d["snapshot"]["id"]

    def openstack_volume_backup(self, form):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        if form.action == "create":
            method = "/backups"
            temp = """{
                "backup": {
                    "name": "$data",
                    "volume_id": "$id",
                    "container": null
                }
            }"""

        elif form.action == "edit":
            method = "/snapshots/%s" % form.id
            temp = """{
                "snapshot": {
                    "name": "$data"
                }
            }"""
            url = "%s%s" % (self.cinderv3_uri, method)
            t = Template(temp)
            body = t.substitute({"data": form.data})

            dd = json.dumps(json.loads(body))
            r = requests.put(url, data=dd, headers=headers)

            if r.status_code == 200:
                return {"msg": "ok"}


        elif form.action == "list":
            method = "/snapshots"
            url = "%s%s" % (self.cinderv3_uri, method)
            headers = {
                "X-Auth-Token": self.token
            }

            r = requests.get(url, headers=headers)

            d = json.loads(r.text)
            res = []
            for ins_dict in d["snapshots"]:
                ins = from_dict(data_class=VolumeSnapshotDetail, data=ins_dict)
                res.append(ins.__dict__)
            return res

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": form.data, "id": form.id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_volume_backup_v2(self, form):
        id = form.get("id", "")
        action = form.get("action", "")
        data = form.get("data", "")
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        if action == "create":
            method = "/backups"
            temp = """{
                "backup": {
                    "name": "$data",
                    "volume_id": "$id",
                    "container": null
                }
            }"""

        elif action == "edit":
            method = "/snapshots/%s" % id
            temp = """{
                "snapshot": {
                    "name": "$data"
                }
            }"""
            url = "%s%s" % (self.cinderv3_uri, method)
            t = Template(temp)
            body = t.substitute({"data": data})

            dd = json.dumps(json.loads(body))
            r = requests.put(url, data=dd, headers=headers)

            if r.status_code == 200:
                return {"msg": "ok"}


        elif action == "list":
            method = "/snapshots"
            url = "%s%s" % (self.cinderv3_uri, method)
            headers = {
                "X-Auth-Token": self.token
            }

            r = requests.get(url, headers=headers)

            d = json.loads(r.text)
            res = []
            for ins_dict in d["snapshots"]:
                ins = from_dict(data_class=VolumeSnapshotDetail, data=ins_dict)
                res.append(ins.__dict__)
            return res

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"data": data, "id": id})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_attach_volume(self, form):
        method = "/volumes/%s/action" % form.id
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "os-attach": {
                    "instance_uuid": "$vmid",
                    "mountpoint": "$mountpoint"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"vmid": form.vmid, "mountpoint": form.mountpoint})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_get_volume_detail(self, form):
        method = "/volumes/%s" % form
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=VolumeDetail, data=d["volume"])

        ins.__dict__["volume_image_metadata"] = d["volume"].get("volume_image_metadata", {})

        ins.__dict__["vmid"] = ""
        ins.__dict__["volume_type"] = d["volume"]["volume_type"]
        ins.__dict__["mountpoint"] = ""
        ins.__dict__["attachmentid"] = ""
        for attach in d["volume"]["attachments"]:
            if attach["volume_id"] == form:
                ins.__dict__["vmid"] = attach["server_id"]
                ins.__dict__["mountpoint"] = attach["device"]
                ins.__dict__["attachmentid"] = attach["attachment_id"]

        return ins.__dict__

    def openstack_get_all_cinder_services(self):
        method = "/os-services"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["services"]:
            ins = from_dict(data_class=VolumeAgent, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_volume_update_to_image(self, volume_id, name):
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient",
            "OpenStack-API-Version": "volume 3.62"
        }

        method = "/volumes/%s/action" % volume_id
        temp = """{
            "os-volume_upload_image": {
                "image_name": "$name",
                "force": true,
                "disk_format" : "raw",
                "container_format" : "bare"
            }
        }"""

        url = "%s%s" % (self.cinderv3_uri, method)
        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}

    def openstack_get_all_cinder_volume_types(self):
        method = "/types"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for type in d["volume_types"]:
            ins = from_dict(data_class=VolumeType, data=type)
            res.append(ins.__dict__)
        return res

    def openstack_create_volume_type(self, volumetypeform):
        method = "/types"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "python-cinderclient"
        }

        temp = """{
                "volume_type": {
                    "name": "$name",
                    "description": "$description",
                    "os-volume-type-access:is_public": true,
                    "extra_specs" : {
                     "volume_backend_name" : "$extra_specs"
                    }
                }
            }"""

        t = Template(temp)
        body = t.substitute(volumetypeform)

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        # ins = from_dict(data_class=VolumeType, data=d["volume_type"])
        # return ins.__dict__

        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_delete_volume_type(self, id):
        method = "/types/%s" % id
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)

        if r.status_code == 202:
            return {"msg": "ok"}
        elif r.status_code == 400:
            return {"msg": "snap"}
        else:
            return {"msg": "error"}

    def openstack_get_all_passthrough(self):
        method = "/volumes/detail"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["volumes"]:
            ins = from_dict(data_class=VolumeDetailforlist, data=ins_dict)
            if ins_dict["description"] == "thxh-passthrough":
                ins.__dict__["description"] = ins_dict["description"]
                res.append(ins.__dict__)
        return res


    def openstack_volumes_services(self):
        headers = {
            "X-Auth-Token": self.token
        }
        method = "/os-services"
        url = "%s%s" % (self.cinderv3_uri, method)
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            res = json.loads(r.text)
            res["msg"] = "ok"
            return res
        
        return {"msg": "error"}
   
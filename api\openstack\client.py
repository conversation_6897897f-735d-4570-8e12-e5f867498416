'''
Created on Jan 20, 2022

@author: maojj
'''
import requests
import json
import settings
from api.model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os


    
class Client(object):
    
    nova_uri = ""
    neutron_uri = ""
    glance_uri = ""
    cinder_uri = ""
    cinderv3_uri = ""
    token = ""
    tenant_id = ""
    
    def __init__(self):
        self.openstatck_get_token()

        package_perfix= "api.openstack.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    self.__dict__[attribute_name] = attribute
        

    def openstatck_get_token(self):
        toke_url = "%s/auth/tokens" % settings.AUTH_URI
        body = """
        {
            "auth": {
                "identity": {
                    "methods": [
                        "password"
                    ],
                    "password": {
                        "user": {
                            "name": "admin",
                            "domain": {
                                "name": "Default"
                            },
                            "password": "%s"
                        }
                    }
                },
                "scope": {
                    "project": {
                        "name": "admin",
                        "domain": {
                            "name": "Default"
                        }
                    }
                }
            }
        }
        """ % settings.PASSWORD

        r = requests.post(toke_url, body)

        data = json.loads(r.text)

        self.tenant_id = data["token"]["project"]["id"]
        for catalog in data["token"]["catalog"]:
            if catalog["name"] == "nova":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.nova_uri = endpoint["url"]
            if catalog["name"] == "neutron":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.neutron_uri = endpoint["url"]   
            if catalog["name"] == "glance":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        if endpoint["url"][:-1] == "/":
                            self.glance_uri = endpoint["url"]
                        else:
                            self.glance_uri = "%s/" % endpoint["url"]
            if catalog["name"] == "cinder":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.cinder_uri = endpoint["url"]
            if catalog["name"] == "cinderv3":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.cinderv3_uri = endpoint["url"]                 
            
        self.token = r.headers.get("X-Subject-Token")

# client = Client()
# server_id = "fe060464-b756-41e2-be8c-f27d32179669"
# host = "controller3"
# test = client.VolumeClient.openstack_volumes_services(client)
# print(test)
#
# client = Client()
# server_id = "fe060464-b756-41e2-be8c-f27d32179669"
# host = "controller3"
# test = client.NovaClient.openstack_nova_services(client)
# print(test)


# client = Client()
# server_id = "62114a8c-1b4d-44dd-9645-f41c0f7974b2"
# test = client.NovaClient.openstack_get_server_detail(client, server_id)
# print(test.get("status", ""))


# client = Client()
# server_id = "fe060464-b756-41e2-be8c-f27d32179669"
# host = "controller3"
# test = client.NovaClient.openstack_force_down_host(client, host)
# print(test)

# client = Client()
# server_id = "fe060464-b756-41e2-be8c-f27d32179669"
# host = "controller3"
# test = client.NovaClient.openstack_enable_host(client, host)
# print(test)


# client = Client()
# vmid = "62114a8c-1b4d-44dd-9645-f41c0f7974b2"
# test = client.NovaClient.openstack_evacuate_vm(client, vmid)
# print(test)


# client = Client()
# vmid = "51f5dd69-451a-48cf-9f03-f12f786ff327"
# test = client.NovaClient.openstack_update_server_name(client, vmid, "maojjj111111")
# print(test)
#
#
# client = Client()
# test = client.HypervisorsClient.openstack_get_all_list(client)
# print(test)
#
#
# test = client.HypervisorsClient.openstack_get_hosts_all_vms(client, "controller3")
# print(test)
#
#
# test = client.HypervisorsClient.openstack_get_all_cluster_detail(client)
#
# for t in test:
#  print(t)
#
# client = Client()
# vm_id = "045cb4a2-57bb-4393-b971-fde1b1f36878"
# data = client.NovaClient.openstack_get_volume_attachment(client, vm_id)
# print(data)
#
# client = Client()
# data = client.NeutronClient.openstack_get_all_list(client)
# print(data)


# client = Client()
# data = client.NovaClient.openstack_getpost_server_detail(client,"9da34917-f3f7-4de1-8508-de15bf131f00")
# print(data)


# client = Client()

# data = client.HypervisorsClient.openstack_get_name_availabilityzone_by_zonename(client)

# print(data)

# client = Client()
# data = client.NovaClient.openstack_server_vm_set_status_error(client,"654952ef-de82-45f5-add2-7c02e3d11c56")
# print(data)
from client import Client

client = Client()

data = {
    'host_ip': '**************',
    'host_pid': '97b874c4-a470-428c-b666-b25f1dde1f79',
    'vm_name': '123',
    'virtualization': 'kvm',
    'memory_unit': 2,
    'memory_unit_type': 'GB',
    'vcpu_unit': 2,
    'display_protocol': 'spice',
    'input_devices': {'mouse': 'usb', 'keyboard': 'usb'},
    'disk': [
        {
            'pool': 'storage1',
            'disk_type': 'file',
            'size': '20',
            'disk_unit_type': 'MB',
            'path': '/var/lib/libvirt/storage1/cirros-0.6.3-x86_64-disk.qcow2',
            'disk_name': '527'
        }
    ],
    'interface': [
        {
            'interface_type': 'network',
            'mac': '00-E0-81-DC-53-1A',
            'network': 'tt',
            'net_id': '03cb8565-28d9-41a3-9905-62f330650d93',
            'model': 'virtio', 'port_name': 'tt-9ddbbad7',
            'switch': {
                'id': '8eb8ad2e-fcfe-4aee-9604-cb8eead1bd7f',
                'name': 'tt',
                'host_id': 'f70c2d20-09bd-4a72-8053-5a794586843b',
                'cluster_id': None, 'vswitch_type': 'local',
                'vlan_ids': '11', 'status': 'active',
                'created_at': '2025-05-26 10:13:24', 'updated_at': '2025-05-26 10:13:24',
                'extra_config': None, 'mtu': '1500'
            },
            'switch_group': {
                'id': '03cb8565-28d9-41a3-9905-62f330650d93',
                'name': 'tt', 'remark': None, 'vlan_id': '11',
                'switchs_id': '8eb8ad2e-fcfe-4aee-9604-cb8eead1bd7f'
            },
            'switch_id': '8eb8ad2e-fcfe-4aee-9604-cb8eead1bd7f',
            'switch_port_group_id': '03cb8565-28d9-41a3-9905-62f330650d93'}
    ],
    'domain_id': '0cb2bb11-6c93-4986-9c9e-8fc114a6be73',
    'interface_info': [
        {
            'interface_type': 'network', 'mac': '00-E0-81-DC-53-1A',
            'network': 'tt', 'net_id': '03cb8565-28d9-41a3-9905-62f330650d93',
            'model': 'virtio', 'port_name': 'tt-9ddbbad7',
            'switch': {
                'id': '8eb8ad2e-fcfe-4aee-9604-cb8eead1bd7f',
                'name': 'tt', 'host_id': 'f70c2d20-09bd-4a72-8053-5a794586843b',
                'cluster_id': None, 'vswitch_type': 'local', 'vlan_ids': '11',
                'status': 'active', 'created_at': '2025-05-26 10:13:24',
                'updated_at': '2025-05-26 10:13:24', 'extra_config': None, 'mtu': '1500'
            },
            'switch_group': {
                'id': '03cb8565-28d9-41a3-9905-62f330650d93', 'name': 'tt',
                'remark': None, 'vlan_id': '11', 'switchs_id': '8eb8ad2e-fcfe-4aee-9604-cb8eead1bd7f'
            },
            'switch_id': '8eb8ad2e-fcfe-4aee-9604-cb8eead1bd7f',
            'switch_port_group_id': '03cb8565-28d9-41a3-9905-62f330650d93',
            'operation_status': 'success'
        }
    ]
}

client.create_dom_vm(client, data)

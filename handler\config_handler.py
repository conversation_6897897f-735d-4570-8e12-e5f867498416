# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
import datetime
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from util.cov import todict, serialize, theevent
from api.openstack.client import Client
from db.model.user import User
from db.model.event import Event, Config
from model.event import EventForm
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

class ConfigHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/session/out/time",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_auto_exit_time(self):

        with self.session_scope() as session:
            query = session.query(Config).filter(Config.key=="auto_exit_time").first()
            
            if query:
                value = query.value
                
            else:
                value = "30"
            
            
                
        return {"auto_exit_time": int(value)}

    @put(_path="/v1/session/out/time/update",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_auto_exit_time(self, form):



        with self.session_scope() as session:
            query = session.query(Config).filter(Config.key=="auto_exit_time").first()
            
            if query:
                session.query(Config).filter(Config.key=="auto_exit_time").update({"value": form.get("auto_exit_time") or "30"})
                # query.value = form.get("auto_exit_time") or "30"
                
                # session.add(query)
                
            else:
                config = Config()
                config.key = "auto_exit_time"
                config.value = form.get("auto_exit_time") or "30"
                session.add(config)
            
            
                
        return {"msg": "ok"}
    
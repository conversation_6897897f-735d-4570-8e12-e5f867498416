'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import settings
from dataclasses import dataclass
from string import Template
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl

from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.open_vswitch import impl_idl


class Client:
    
    def create_ovs_local_switch(self, switch_name):
        ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)

        # 创建与 OVS 数据库的 TCP 连接
        conn = connection.Connection(
            idl=connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch"),
            timeout=10
        )
        ovs_api = impl_idl.OvsdbIdl(conn)
        # 添加端口到桥接
        with ovs_api.transaction(check_error=True) as txn:
            txn.add(ovs_api.add_br(switch_name))
            print(f"Bridge {switch_name} created")

        return True

    def create_ovn_switch(self, switch_name):
        nb_connection_str = "tcp:{}:{}".format(self.host_ip, self.nb_port)

        # 连接到 OVN Northbound 数据库
        conn_nb = connection.Connection(
            idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
            timeout=10
        )
        nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

        with nb_api.transaction(check_error=True) as txn:
            txn.add(nb_api.ls_add(switch_name))
            print(f"Logical switch {switch_name} created")


        return True

    def list_switches(self):
        nb_connection_str = "tcp:{}:{}".format(self.host_ip, self.nb_port)

        # 连接到 OVN Northbound 数据库
        conn_nb = connection.Connection(
            idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
            timeout=10
        )
        nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

        switches = nb_api.ls_list().execute(check_error=True)
        for switch in switches:
            print(f"Logical switch: {switch.name}")


        return True
    
    def delete_switch(self, switch_name):
        nb_connection_str = "tcp:{}:{}".format(self.host_ip, self.nb_port)

        # 连接到 OVN Northbound 数据库
        conn_nb = connection.Connection(
            idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
            timeout=10
        )
        nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

        with nb_api.transaction(check_error=True) as txn:
            txn.add(nb_api.ls_del(switch_name))
            print(f"Logical switch {switch_name} deleted")

        return True
    

    def add_port_to_bridge(self, bridge_name, port_name, iface_id):
        ovs_connection_str = "tcp:{}:{}".format(self.host_ip, self.ovs_port)

        # 创建与 OVS 数据库的 TCP 连接
        conn = connection.Connection(
            idl=connection.OvsdbIdl.from_server(ovs_connection_str, "Open_vSwitch"),
            timeout=10
        )
        ovs_api = impl_idl.OvsdbIdl(conn)
        # 添加端口到桥接
        with ovs_api.transaction(check_error=True) as txn:
            txn.add(ovs_api.add_port(bridge_name, port_name))
            print(f"Port {port_name} added to bridge {bridge_name}")

        with ovs_api.transaction(check_error=True) as txn:
            txn.add(ovs_api.db_set('Interface', port_name, ('external_ids', {'iface-id': iface_id})))
            print(f"Set external_ids for interface {port_name}: iface-id={iface_id}")

        
        return True 


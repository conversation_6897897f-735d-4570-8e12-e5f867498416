
from api.libvirt.utils.vmxml.network_xml import Network

class Net:
    def get_net_list(self):
        """
        获取网络列表
        """
        ret = []
        nets = self.conn.listNetworks()
        # print(net)
        # 循环获取所有net的详情
        for net_name in nets:
            network = self.conn.networkLookupByName(net_name)
            net_info = {
                "name": network.name(),
                "uuid": network.UUIDString(),
                "status": network.isActive(),
                "autostart": network.autostart(),
                "bridge": network.bridgeName(),
                "xml": network.XMLDesc()
            }
            ret.append(net_info)

        return ret

    def get_net_info_by_name(self, name):
        """
        获取网络详情
        """
        network = self.conn.networkLookupByName(name)
        net_info = {
            "name": network.name(),
            "uuid": network.UUID(),
            "status": network.isActive(),
            "autostart": network.autostart(),
            "bridge": network.bridgeName(),
            "xml_desc": network.XMLDesc()
        }

        return net_info

    def define_network_from_xml(self, xml_desc):
        """通过 XML 描述定义网络"""
        try:
            self.conn.networkDefineXML(xml_desc)
            print("Network defined successfully.")
        except Exception as e:
            print(f"Failed to define network: {e}")

    def start_network(self, network_name):
        """启动网络"""
        try:
            network = self.conn.networkLookupByName(network_name)
            network.create()
            print(f"Network {network_name} started successfully.")
        except Exception as e:
            print(f"Failed to start network: {e}")

    def stop_network(self, network_name):
        """停止网络"""
        try:
            network = self.conn.networkLookupByName(network_name)
            network.destroy()
            print(f"Network {network_name} stopped successfully.")
        except Exception as e:
            print(f"Failed to stop network: {e}")

    def undefine_network(self, network_name):
        """取消定义网络"""
        try:
            network = self.conn.networkLookupByName(network_name)
            network.undefine()
            print(f"Network {network_name} undefined successfully.")
        except Exception as e:
            print(f"Failed to undefine network: {e}")

    def create_libvirt_network(self, form):
        # 定义网络 XML
        # network_xml = '''
        # <network>
        #   <name>ovs-network</name>
        #   <forward mode='bridge'/>
        #   <bridge name='br0'/>
        #   <virtualport type='openvswitch'/>
        # </network>
        # '''
        ovs_net = Network("network")
        ovs_net.set_name(form.get("name"))
        ovs_net.set_forward()
        ovs_net.set_bridge(form.get("name") + "-net")
        ovs_net.set_virtual_port()
        network_xml = ovs_net.to_xml()
        # 定义网络
        net = self.conn.networkDefineXML(network_xml)
        print("Network created: %s" % net.name())

        # 启动网络
        net.create()
        print("Network started: %s" % net.name())

        net.setAutostart(1)  # 1 means autostart is enabled
        print("Network autostart enabled: %s" % net.name())
        return True

    def modify_network(self, form):
        pass

from .default import XMLElement


class Domain(XMLElement):
    """
    通用元素据类型
    name: 名称
    """

    def __init__(self, tag="domain", attributes=None, text=None):
        if attributes is None:
            attributes = {
                "type": "kvm"
            }
        super().__init__(tag, attributes, text)
        # self.set_tag(name)

    def set_name(self, name):
        self.set_tag("name", None, name)

    def set_uuid(self, uuid, attributes=None):
        self.set_tag("uuid", attributes, uuid)

    def set_memory(self, memory, attributes=None):
        if attributes is None:
            attributes = {
                "unit": "KiB"
            }
        self.set_tag("memory", attributes, memory)

    def set_current_memory(self, memory, attributes=None):
        if attributes is None:
            attributes = {
                "unit": "KiB"
            }
        self.set_tag("current_memory", attributes, memory)

    def set_vcpu(self, vcpu, attributes=None):
        self.set_tag("vcpu", attributes, vcpu)

    def set_cpu(self, attributes=None):
        cpu = Cpu("cpu")
        self.add_child(cpu)
        return cpu

    def set_os(self, attributes=None):
        os = OSBoot("os", attributes)
        self.add_child(os)
        return os

    def set_feature(self, attributes=None):
        feature = Feature("feature", attributes)
        self.add_child(feature)
        return feature

    def set_clock(self, attributes=None):
        clock = Clock("clock", attributes)
        self.add_child(clock)

        return clock

    def set_pm(self):
        pm = Power("pm")
        self.add_child(pm)
        return pm

    def set_on_poweroff(self, on_poweroff="destroy"):
        """
        描述：当客户操作系统请求关机时触发。
        :param on_poweroff:
            ■ destroy：完全终止虚拟机并释放所有资源。
            ■ restart：终止虚拟机并使用相同配置重新启动。
            ■ preserve：终止虚拟机但保留资源以便于分析。
            ■ rename-restart：终止虚拟机并以新名称重新启动（仅libxl hypervisor驱动支持）。
        :return:
        """
        self.set_tag_value("on_poweroff", on_poweroff)

    def set_on_reboot(self, on_reboot="reboot"):
        """
        当客户操作系统请求重启时触发。
        :param on_reboot:
            ■ destroy：完全终止虚拟机并释放所有资源。
            ■ restart：终止虚拟机并使用相同配置重新启动。
            ■ preserve：终止虚拟机但保留资源以便于分析。
            ■ rename-restart：终止虚拟机并以新名称重新启动（仅libxl hypervisor驱动支持）。
        :return:
        """
        self.set_tag_value("on_reboot", on_reboot)

    def set_on_crash(self, on_crash="destroy"):
        """
        描述：当客户操作系统崩溃时触发。
        :param on_crash:
            ■ destroy：完全终止虚拟机并释放所有资源。
            ■ restart：终止虚拟机并使用相同配置重新启动。
            ■ preserve：终止虚拟机但保留资源以便于分析。
            ■ coredump-destroy：转储崩溃虚拟机的核心，然后完全终止虚拟机并释放所有资源。
            ■ coredump-restart：转储崩溃虚拟机的核心，然后使用相同配置重新启动虚拟机。
        :return:
        """

        self.set_tag_value("on_crash", on_crash)

    def set_devices(self, ):
        devices = Devices("devices")
        self.add_child(devices)
        return devices


class Metadata(XMLElement):
    """
    元数据
    """

    def __init__(self, tag="metadata", attributes=None, text=None):
        super().__init__(tag)


class OSBoot(XMLElement):
    """
    os启动设置
    """

    def set_type(self, boot_type, attributes=None):
        self.set_tag("type", attributes, boot_type)

    def set_boot(self, boot, attributes=None):
        self.set_tag("boot", attributes, boot)

    def set_boot_menu(self, boot_menu, attributes=None):
        if attributes is None:
            attributes = {
                "enable": "no",
            }
        self.set_tag("boot_menu", attributes, boot_menu)


class Feature(XMLElement):
    """
    虚拟机监控程序功能，标签 <feature>
    """

    def set_acpi(self, attributes=None):
        """
        ● acpi
          ○ 描述：高级配置和电源接口，用于电源管理。对于KVM或HVF客户，ACPI是实现优雅关机所必需的。
          ○ 启用：<acpi/>
        :param attributes:
        :return:
        """
        self.set_tag("acpi", attributes)

    def set_apic(self, attributes=None):
        """
        ● apic
          ○ 描述：高级可编程中断控制器，允许使用可编程IRQ管理。
          ○ 启用：<apic/>
          ○ EOI设置：从0.10.2版本开始，QEMU支持eoi属性，可以设置为on或off，用于控制EOI（中断结束）的可用性。
        :param attributes:
        :return:
        """
        self.set_tag("apic", attributes)

    def set_vmport(self, attributes=None):
        """
        状态：可以是 on 或 off 来启用或禁用 VMware IO 端口仿真，对 vmmouse 等设备很有用。
        :param attributes:
        :return:
        """
        if attributes is None:
            attributes = {
                "state": "off"
            }
        self.set_tag("vmport", attributes)


class SMBIOS(XMLElement):
    """
    SMBIOS设置
    """
    pass


class Cpu(XMLElement):
    """
    cpu设置
    """

    def set_atr_model(self, model):
        self.set_attribute("model", model)

    def set_atr_migration(self, migration):
        self.set_attribute("migration", migration)

    def set_atr_check(self, check):
        self.set_attribute("check", check)


class Clock(XMLElement):
    """
    时钟
    """

    def set_timer(self, attributes=None):
        self.set_tag("timer", attributes)

    def set_default_timer(self):
        self.set_tag("timer", {"name": "rtc", "tickpolicy": "catchup"})
        self.set_tag("timer", {"name": "pit", "tickpolicy": "delay"})
        self.set_tag("timer", {"name": "hpet", "present": "no"})

    def set_atr_offset(self, offset="utc"):
        """
        ■ utc：虚拟机时钟始终与协调世界时（UTC）同步。
        ■ localtime：虚拟机时钟与宿主机配置的时区同步。
        ■ timezone：虚拟机时钟与指定的时区同步。
        ■ variable：虚拟机时钟有一个相对于UTC或本地时间的任意偏移量。
        ■ absolute：虚拟机时钟始终设置为启动域时的起始值（以epoch时间戳表示）。
        """
        self.set_attribute("offset", offset)


class Power(XMLElement):
    """
    电源管理
    """

    def set_suspend_to_disk(self, enabled="no"):
        """
        配置S4 ACPI睡眠状态，即挂起到硬盘
        :param enabled:
                    yes: “启用"
                    no:  ”禁用“
        :return:
        """
        self.set_tag("suspend_to_disk", attributes={"enabled": enabled})

    def set_suspend_to_mem(self, enabled="no"):
        """
        配置S3 ACPI睡眠状态，即挂起到内存。
        :param enabled:
                    yes: “启用"
                    no:  ”禁用“
        :return:
        """
        self.set_tag("suspend_to_mem", attributes={"enabled": enabled})


class CPUAllocation(XMLElement):
    """
    CPU设置
    """
    pass


class IOThreadAllocation(XMLElement):
    """
    IO线程设置
    """
    pass


class CPUTune(XMLElement):
    """
    CPU调优设置
    """
    pass


class MemoryAllocation(XMLElement):
    """
    内存设置
    """

    def __init__(self, tag="memory", attributes=None, text=None):
        if not attributes:
            attributes = {
                "unit": "KiB"
            }
        super().__init__(tag, attributes, text)

    def set_unit(self, unit="KiB"):
        self.set_attribute("unit", unit)

    def set_size(self, size):
        self.set_text(str(size))


class MemoryBacking(XMLElement):
    """
    内存后端设置
    """
    pass


class MemoryTune(XMLElement):
    """
    内存调优设置
    """
    pass


# ||============================================================================================================||
# ||================================================ 设备相关类 ==================================================||
# ||============================================================================================================||
class Devices(XMLElement):
    """
    虚拟机设备
    """

    def set_disk(self, ):
        """
        设置磁盘信息
        :return:
        """
        disk = Disk("disk")
        self.add_child(disk)
        return disk

    def set_console(self, type_atr="pty"):
        console = Console("console")
        self.add_child(console)
        console.set_atr_type(type_atr)

    def set_channel(self, channel: str):
        if channel == "":
            channel = "spicevmc"
        device_channel = Channel("channel")
        self.add_child(device_channel)
        device_channel.set_atr_type(channel)
        if channel == "spicevmc":
            device_channel.set_spice_channel_target()
        elif channel == "spiceport":
            device_channel.set_spice_port_channel_target()

    def set_input(self, type_atr, bus_atr=""):
        input_device = Input("input")
        self.add_child(input_device)
        input_device.set_atr_type(type_atr)
        if bus_atr != "":
            input_device.set_atr_bus(bus_atr)

    def set_default_input(self):
        mouse_device = Input("input")
        self.add_child(mouse_device)
        mouse_device.set_default_input_mouse()

        keyboard_device = Input("input")
        self.add_child(keyboard_device)
        keyboard_device.set_default_input_keyboard()

        tablet_device = Input("input")
        self.add_child(tablet_device)
        tablet_device.set_default_input_tablet()

    def set_default_graphics(self):
        graphics_device = Graphics("graphics")
        self.add_child(graphics_device)
        graphics_device.set_default_graphics()

    def set_audio_backend(self, type_str="spice"):
        if type_str == "spice":
            self.set_tag("audio", attributes={"type": type_str, "id": "1"})
        elif type_str == "fike":
            self.set_tag("audio", attributes={"type": type_str, "id": "1", 'path': "audio.wav"})

    def set_default_sound(self):
        sound = Sound("sound")
        self.add_child(sound)
        sound.set_atr_model()

    def set_default_redirect(self):
        redirect = Redirect("redirdev")
        redirect.set_default_atr()
        self.add_child(redirect)


class Disk(XMLElement):
    """
    磁盘
    """

    def set_atr_type(self, disk_type="file"):
        """
        type属性： 属性指定了磁盘的底层来源类型。这个属性的值决定了磁盘数据的存储和访问方式
        :param disk_type:
                "file"：磁盘数据存储在一个文件中，通常是磁盘映像文件，如 QCOW2、RAW 等格式。
                "block"：磁盘直接映射到宿主机上的一个块设备（如硬盘或分区）。
                "dir"：磁盘数据存储在一个目录中，这个目录通常包含磁盘的快照或其他磁盘数据。
                "network"：磁盘数据通过网络访问，通常用于网络附加存储（NAS）或存储区域网络（SAN）。
                "volume"：磁盘数据存储在存储池中的一个卷上，这个卷是使用 libvirt 管理的。
                "nvme"：磁盘模拟 NVMe 设备。
                "vhostuser"：使用 vhost-user 协议的磁盘。
                "vhostvdpa"：使用 vhost-vdpa 协议的磁盘。
        :return:
        """
        self.set_attribute("type", disk_type)

    def set_atr_device(self, device="disk"):
        """
        device属性：定义了磁盘如何在虚拟机的客户机操作系统中呈现。这个属性决定了虚拟机操作系统如何识别和使用这个磁盘
        :param device:
            ■ "floppy"：磁盘作为一个软盘驱动器呈现。
            ■ "disk"：磁盘作为一个硬盘驱动器呈现，这是默认值。
            ■ "cdrom"：磁盘作为一个 CD/DVD 驱动器呈现。
            ■ "lun"：磁盘作为一个逻辑单元号（LUN）呈现，这通常用于 SCSI 设备。从 0.9.10 版本开始，"lun" 只有在 type 是 "block" 或 "network" 且协议为 "iscsi" 时，或者 type 是 "volume" 且使用 iSCSI 源池模式为 "host" 或作为 NPIV 虚拟主机总线适配器（vHBA）使用 Fibre Channel 存储池时才有效。
            ■ 当使用 "lun" 时，磁盘的行为与 "disk" 类似，但客户机会接受并传递到物理设备的通用 SCSI 命令。注意，device='lun' 只对实际的原始设备有效，不适用于个别分区或 LVM 分区。在这些情况下，内核将拒绝通用 SCSI 命令，使其行为与 device='disk' 相同。
        :return:
        """
        self.set_attribute("device", device)

    def set_driver(self, driver_type="qcow2"):
        """
        driver：磁盘驱动信息
        ● name:
            ○ 指定主要后端驱动程序的名称。不同的虚拟化平台支持不同的驱动程序。
                例如，Xen 支持 "tap", "tap2", "phy", 或 "file" 等驱动，
                而 QEMU 只支持 "qemu" 驱动，但有多种类型，如 "raw", "bochs", "qcow2", 和 "qed"。
        ● type: 取决于name的值
            ○ 提供驱动程序的子类型。这取决于驱动程序的名称。对于 QEMU，类型包括 "raw", "bochs", "qcow2" 等。
        :param driver_type:
        :return:
        """
        attributes = {
            "name": "qemu",
            "type": driver_type
        }

        self.set_tag("driver", attributes)

    def set_source(self, attributes=None):
        """
        <disk> 元素的 <source> 子元素定义了虚拟机磁盘的来源。这个来源取决于磁盘的 type 属性，可以是文件、块设备或目录
        type="file"
            ● file 属性：
              ○ 当 type 属性为 "file" 时，file 属性指定了存储磁盘映像文件的完整路径。这个文件可以是 QCOW2、RAW、VMware 镜像或其他任何支持的磁盘映像格式。
              ○ 从 libvirt 9.0.0 版本开始，可以添加一个可选的 fdgroup 属性。这个属性指示 libvirt 通过与域对象关联的文件描述符来访问磁盘，而不是通过文件系统打开文件。这意味着文件不必是 libvirt 通过文件系统直接可访问的。通过 file 属性传递的文件名仍可用于在执行块操作时写入映像元数据的路径，但 libvirt 不会直接访问这些文件。
            type="block"
            ● dev 属性：
              ○ 当 type 属性为 "block" 时，dev 属性指定了宿主机上用作磁盘的设备文件的完整路径。这通常是一个物理硬盘或硬盘分区的路径。
            type="dir"
            ● dir 属性：
              ○ 当 type 属性为 "dir" 时，dir 属性指定了用作磁盘的目录的完整路径。大多数支持 "dir" 磁盘的虚拟化监视器通过公开一个带有模拟文件系统的模拟块设备来实现这一点，该文件系统用配置目录中的内容填充。
              ○ 需要注意的是，由于客户机操作系统可能会缓存文件系统元数据，因此在目录上的外部更改可能不会在客户机中显示，或者可能导致从虚拟机中观察到损坏的数据。
        :param attributes:
        :return:
        """
        self.set_tag("source", attributes)

    def set_target(self, attributes=None):
        """
        <target> 元素在虚拟机的 XML 配置中起着至关重要的作用。这个元素控制着磁盘如何被虚拟机的客户操作系统（guest OS）所识别和使用
        ● dev：
          ○ 表示“逻辑”设备名称。这个属性指定了虚拟机内部看到的设备名称。实际指定的设备名称不一定与虚拟机操作系统中的设备名称一一对应。应将其视为设备顺序的提示。
            virtio：vda, vdb
            ide: hda, hdb
            scsi/sata: sda, sdb
        ● bus（可选）：
          ○ 指定要模拟的磁盘设备类型。可能的值依赖于具体的虚拟化驱动程序，常见的值包括 "ide", "scsi", "virtio", "xen", "usb", "sata", 或 "sd"。
            如果在 1.1.2 版本之后，可以是 "sd"。如果省略此属性，总线类型将从设备名称的风格中推断出来（例如，名为 'sda' 的设备通常使用 SCSI 总线导出）。
        :param attributes:
        :return:
        """
        if attributes is None:
            attributes = {
                "bus": "virtio"
            }
        self.set_tag("target", attributes)


class Controller(XMLElement):
    """
    控制器
    """
    pass


class Console(XMLElement):
    """控制台"""

    def set_atr_type(self, type_atr):
        self.set_attribute("type", type_atr)


class Channel(XMLElement):
    """
    通道
    """

    def set_atr_type(self, type_atr="spicevmc"):
        self.set_attribute("type", type_atr)

    def set_spice_channel_target(self, attributes=None):
        if attributes is None:
            attributes = {
                "type": "virtio",
                "name": "com.redhat.spice.0"
            }
        self.set_tag("target", attributes)

    def set_spice_port_channel_target(self, attributes=None):
        if attributes is None:
            attributes = {
                "type": "virtio",
                "name": "org.spice-space.webdav.0"
            }

        self.set_tag("target", attributes)


class Input(XMLElement):
    """
    输入设备
    """

    def set_atr_type(self, type_atr: str):
        """
        <input> 元素有一个必需的属性 type，其值可以是以下几种：
            ● 'mouse'：表示鼠标设备，使用相对坐标移动光标。
            ● 'tablet'：表示平板设备，提供绝对坐标的光标移动（自1.2.2版本起支持）。
            ● 'keyboard'：表示键盘设备（自1.3.0版本起支持）。
            ● 'passthrough'：表示直接传递的输入设备（自7.4.0版本起支持）。
            ● 'evdev'：表示事件设备（自7.4.0版本起支持）。
        :param type_atr:
        :return:
        """
        self.set_attribute("type", type_atr)

    def set_atr_bus(self, bus_str: str):
        """
        可选属性
        bus 属性用于细化确切的设备类型，它可以取以下值：
            ● "xen"：Xen 的准虚拟化设备。
            ● "ps2"：PS/2 总线设备。
            ● "usb"：USB 设备。
            ● "virtio"：VirtIO 设备（自1.3.0版本起支持）。
                    :param bus_str:
        :return:
        """

        self.set_attribute("bus", bus_str)

    def set_default_input_mouse(self):
        """
        设置默认鼠标
        :return:
        """
        self.set_atr_type("mouse")
        self.set_atr_bus("ps2")

    def set_default_input_keyboard(self):
        """
        设置默认键盘
        :return:
        """
        self.set_atr_type("keyboard")
        self.set_atr_bus("ps2")

    def set_default_input_tablet(self):
        """
        设置默认平板设备
        :return:
        """
        self.set_atr_type("tablet")
        self.set_atr_bus("usb")


class Graphics(XMLElement):
    """
    图形帧缓冲区
    """

    def set_atr_type(self, type_atr: str):
        """
        必须属性
        ● <graphics> 元素有一个必需的 type 属性，用于指定图形显示的类型。支持的值包括：
              ○ sdl：使用 SDL（Simple DirectMedia Layer）库进行图形显示。适用于需要直接在宿主机上显示图形界面的场景。
              ○ vnc：使用 VNC 协议进行远程桌面访问。允许用户通过 VNC 客户端连接到虚拟机的图形界面。
              ○ spice：使用 SPICE 协议进行高效的远程桌面访问。SPICE 提供了更好的性能和功能，特别是在虚拟机的图形和音频方面。
              ○ rdp：使用 RDP（Remote Desktop Protocol）进行远程桌面访问。适用于与 Windows 客户端的连接。
              ○ desktop：在宿主机上创建一个桌面窗口，适用于直接在宿主机上显示虚拟机的图形界面。
              ○ egl-headless：用于无头（headless）图形应用程序，适合于不需要显示输出的场景，通常用于图形计算或渲染。
        :param type_atr:
        :return:
        """

        self.set_attribute("type", type_atr)

    def set_default_graphics(self):
        """
        设置默认<graphics>
        """
        form = {
            "type": "spice",
            "port": "-1",
            "tlsPort": "-1",
            "autoport": "yes",
            "listen": "0.0.0.0"
        }
        for k, v in form.items():
            self.set_attribute(k, v)

        self.set_tag("listen", attributes={
            "type": "address",
            "listen": "0.0.0.0",
        })


class Sound(XMLElement):
    """
    sound设备
    <sound> 元素
        ● 必需的 model 属性：指定要模拟的声卡型号。有效的值取决于底层虚拟化平台（hypervisor）的特性，常见的型号包括 sb16, es1370, pcspk, ac97, ich6, ich9, usb, ich7 和 virtio。
        <codec> 子元素
    """

    def set_atr_model(self, model: str = "ich9"):
        """
        设备model属性
        :param model:
        :return:
        """

        self.set_attribute("model", model)


class Video(XMLElement):
    """
    视频设备
    ● 如果域 XML 中没有设置 <video> 但有 <graphics>，则 libvirt 会根据客户机类型添加默认视频设备。
          ○ 对于类型为 "kvm" 的客户机，默认视频设备是：type 为 "cirrus"，vram 为 "16384"，heads 为 "1"。
          ○ 默认情况下，域 XML 中的第一个 <video> 设备是主设备，但可以使用可选的 primary 属性（自 1.0.2 起）标记多个视频设备中的主设备。
    """


class Redirect(XMLElement):
    """
    重定向设备
    redirdev 元素
        <redirdev> 元素是描述重定向设备的主容器。这个元素用于将宿主机上的设备（如USB设备）重定向到虚拟机，使得虚拟机可以访问这些设备。以下是 <redirdev> 元素的关键属性和子元素：
        ● bus：必须设置为 "usb"，表示这是一个USB设备。
        ● type：需要一个额外的 type 属性，匹配支持的串行设备类型之一（如 tcp 或 spicevmc）。type='spicevmc' 使用 SPICE 图形设备的 usbredir 通道。
        ● <address>：一个可选的子元素，可以将设备绑定到特定的控制器。
        ● <source>：根据给定的 type，可能需要额外的子元素，如 <source>。
        ● <target>：不需要 <target> 子元素，因为字符设备的消费者是 hypervisor 本身，而不是客户机中可见的设备。

    <redirfilter> 元素用于创建过滤规则，以过滤掉某些不需要重定向的设备。它使用 <usbdev> 子元素来定义每个过滤规则：
        ● class：USB 类代码，例如 0x08 表示大容量存储设备。
        ● vendor 和 product：使用供应商和产品 ID 来指定 USB 设备。
        ● version：设备的版本号，来自 bcdDevice 字段（不是 USB 协议的版本）。
        ● 这四个属性都是可选的，可以使用 -1 来允许任何值。
        ● allow：必需的属性，'yes' 表示允许，'no' 表示拒绝。

    示例：
        device = Redirect("redirdev")
        device = Redirect("redirfilter")
    """

    def set_default_atr(self):
        self.set_attribute("bus", "usb")
        self.set_attribute("type", "spicevmc")

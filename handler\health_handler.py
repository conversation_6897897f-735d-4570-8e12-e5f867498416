# -*- coding: utf-8 -*-
import json

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from model.hypervisors import Cluster, ClusterHostForm,ClusterEditForm,ClusterDeleteForm
from util.cov import todict
#from api.ceph.client import Client
#from api.prometheus.client import PClient
import settings
import importlib
from pkgutil import iter_modules
from inspect import isclass
import os

class HealthHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/health/allname", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_health_check_names(self):
        
        result = []
        import api
        
        path = os.path.abspath(api.__file__)
        print(path)
        
        package_perfix= "api.health"
        
        package_dir = os.path.abspath(os.path.join(api.__file__, '../health'))
        for module_name in iter_modules([package_dir]):
            tmp = {}
            module_str = '%s.%s' % (package_perfix, module_name[1])
            print(module_str)
            module = importlib.import_module(module_str)
            tmp["key"] =  module_name[1]
            for attribute_name in dir(module):
                if attribute_name in ["name", "weigth"]:
                    attribute = getattr(module, attribute_name)
                    tmp[attribute_name] = attribute
            result.append(tmp)
   
        
        return result

        
    @get(_path="/v1/health/check", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_health_check(self):
        import api
        key = self.get_argument('key')
        
        package_perfix= "api.health"
        
        module_str = '%s.%s' % (package_perfix, key)
        module = importlib.import_module(module_str)

        for attribute_name in dir(module):
            if attribute_name == "check":
                s = getattr(module, attribute_name)()
                if key == "alert":
                    for entry in s['data']:
                        if entry['summary'].find("节点失联") != -1:
                             entry['state'] = "提示"
                if key == "cpu_status" or key == "ram_status" or key == "alert" or key == "storage_status":
                    for entry in s['data']:
                        entry['instance'] = entry['instance'].split(':')[0]
                if key == "cpu_status" or key == "ram_status" or key == "storage_status":
                    for entry in s['data']:
                        entry['value'] = f"{entry['value']}%"
                #data = json.loads(s['data'])
                # 按时间排序返回值
                if key != "alert":
                    return s
                else :
                    sorted_data = sorted(s['data'], key=lambda x: x["date"], reverse=True)
                    s['data'] = sorted_data
                    return s

        return {"result":"error"}        

# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from dataclasses import dataclass
import json
from sqlalchemy.engine import strategies
from sqlalchemy.orm import strategy_options
from typing import List



@dataclass
class Instances(object):
    id : str
    name : str
    """
    def __repr__(self):
        return json.dumps(self.__dict__)
    def toJson(self):
        return json.dumps(self.__dict__)
    """
    
class InstancesAddtoGroup(object):
    id : str
    group_name : str
    
@dataclass
class InstanceDetail(object):
    id : str
    name : str
    status : str
    created : str
    updated : str
    hostId : str
    addresses : dict
    image : dict
    flavor : dict
    
    
@dataclass
class InstanceGetDetail(object):
    id : str
    name : str
    status : str
    created : str
    updated : str
    hostId : str
    addresses : dict
    flavor : dict



@dataclass
class ServerRes:
    id : str

    
    
class Groups(object):
    instances : dict
    name : str

class Group(object):
    name : str
    page: int
    pagecount: int
    search: str

class GroupRename(object):
    name : str
    new_name : str

class Clusters(object):
    nodes : dict
    name : str
    
class InstancesDetailFrom(object):
    page: int
    pagecount: int
    search: str
    
    
class InstanceCreateFrom(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid: str
    availability_zone: str


class InstanceCreateFromV2(object):
    name: str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid: str
    os_type: str
    availability_zone: str


class InstanceDeleteFrom(object):
    ids : list
    
class InstanceActionFrom(object):
    id : str
    action : str
    data : str



class InstanceActionFromV2(object):
    ids : []
    action : str
    data : str


class InstanceEditFrom(object):
    id : str
    name : str
    
class InstanceISOEditFrom(object):
    id : str
    description : str
    
class InstanceAttachForm(object):
    id : str
    vmid : str


class InstanceDetachFrom(object):
    vmid: str
    volumeid: str


class ServerWithISOFrom(object):
    name: str
    imageRef: str
    flavorRef: str
    networks: str
    count: int


class InstanceISOCreateFrom(object):
    name: str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid: str
    uuid2: str
    availability_zone: str



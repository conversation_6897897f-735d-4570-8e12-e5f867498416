#!/usr/bin/env python3
"""
Celery启动脚本
支持三种方式:
1. 服务端启动: python agent_runner.py server
2. 带队列的agent启动: python agent_runner.py agent --queue queue_192.168.213.31
3. 自定义任务包启动: python agent_runner.py custom --include app.tasks.vm_tasks,agents.tasks
"""

import os
import sys
import argparse
import subprocess
import importlib.util
import tempfile

DEFAULT_CONCURRENCY = 2
DEFAULT_MAX_TASKS = 2
DEFAULT_LOGLEVEL = "info"
PYTHON_PATH = "/opt/venv/bin/python3.9"
CELERY_PATH = "/opt/venv/bin/celery"

def parse_args():
    parser = argparse.ArgumentParser(description='Celery Worker启动工具')
    subparsers = parser.add_subparsers(dest='command', help='启动命令')
    
    # 通用参数
    common_args = argparse.ArgumentParser(add_help=False)
    common_args.add_argument('--concurrency', '-c', type=int, default=DEFAULT_CONCURRENCY,
                    help=f'并发worker数(默认: {DEFAULT_CONCURRENCY})')
    common_args.add_argument('--loglevel', default=DEFAULT_LOGLEVEL,
                    help=f'日志级别(默认: {DEFAULT_LOGLEVEL})')
    common_args.add_argument('--max-tasks', type=int, default=DEFAULT_MAX_TASKS,
                    help=f'每个子进程最大处理任务数(默认: {DEFAULT_MAX_TASKS})')
    
    # 服务端模式
    server_parser = subparsers.add_parser('server', parents=[common_args], help='服务端启动模式')
    
    # Agent模式
    agent_parser = subparsers.add_parser('agent', parents=[common_args], help='Agent启动模式')
    agent_parser.add_argument('--queue', '-Q', required=True,
                    help='指定队列名称, 如: queue_192.168.213.31')
    
    # 自定义模式
    custom_parser = subparsers.add_parser('custom', parents=[common_args], help='自定义任务包启动模式')
    custom_parser.add_argument('--include', required=True,
                    help='指定要加载的任务包，逗号分隔，如: app.tasks.vm_tasks,agents.tasks')
    custom_parser.add_argument('--queue', '-Q',
                    help='可选，指定队列名称')
    
    return parser.parse_args()

def generate_bootstrap_script(mode, include_tasks=None, queue=None):
    """生成临时的启动脚本"""
    # 创建一个临时目录用于存放Python模块
    temp_dir = tempfile.mkdtemp()
    
    # 创建临时Python包目录结构
    os.makedirs(os.path.join(temp_dir, "celery_runner"), exist_ok=True)
    
    # 创建__init__.py使其成为有效的Python包
    with open(os.path.join(temp_dir, "celery_runner", "__init__.py"), "w") as f:
        f.write("# 临时包")
    
    # 创建bootstrap.py模块
    bootstrap_content = f"""
# 临时生成的Celery启动脚本
import os
import sys
from celery import Celery
from celery.signals import worker_init

# 添加项目根目录到PYTHONPATH
project_path = "/home/<USER>/project/hci_asyn"
if project_path not in sys.path:
    sys.path.insert(0, project_path)

# 导入必要的模块
from app.celery import create_celery_app, SERVER_TASKS, AGENT_TASKS
from utils.db import init_db

# 根据模式重新创建Celery应用
if "{mode}" == "server":
    app = create_celery_app(SERVER_TASKS)
elif "{mode}" == "agent":
    app = create_celery_app(AGENT_TASKS)
elif "{mode}" == "custom":
    custom_tasks = {include_tasks}
    app = create_celery_app(custom_tasks)

# 设置队列（如果有）
if "{queue}":
    app.conf.task_routes = {{"*": {{"queue": "{queue}"}}}}

# 保持worker_init信号
@worker_init.connect
def init_worker(**kwargs):
    init_db()

if __name__ == "__main__":
    argv = [
        "worker",
        "--loglevel={os.environ.get('CELERY_LOGLEVEL', 'INFO')}",
        "-c", os.environ.get('CELERY_CONCURRENCY', '2'),
        "--max-tasks-per-child={os.environ.get('CELERY_MAX_TASKS', '2')}"
    ]
    
    if "{queue}":
        argv.extend(["-Q", "{queue}"])
    
    app.worker_main(argv)
"""
    
    # 写入bootstrap.py
    bootstrap_path = os.path.join(temp_dir, "celery_runner", "bootstrap.py")
    with open(bootstrap_path, "w") as f:
        f.write(bootstrap_content)
    
    return temp_dir

def main():
    args = parse_args()
    
    if not args.command:
        print("错误: 必须指定命令 (server, agent 或 custom)")
        sys.exit(1)
    
    # 设置环境变量
    env = os.environ.copy()
    env["CELERY_LOGLEVEL"] = args.loglevel
    env["CELERY_CONCURRENCY"] = str(args.concurrency)
    env["CELERY_MAX_TASKS"] = str(args.max_tasks)
    
    # 设置 Celery 模式
    if args.command == "agent":
        env["CELERY_MODE"] = "agent"
    else:
        env["CELERY_MODE"] = "server"
    
    # 根据不同模式处理
    if args.command == "server":
        temp_dir = generate_bootstrap_script("server")
        queue = None
    elif args.command == "agent":
        temp_dir = generate_bootstrap_script("agent", queue=args.queue)
        queue = args.queue
    elif args.command == "custom":
        include_tasks = [task.strip() for task in args.include.split(",")]
        temp_dir = generate_bootstrap_script("custom", include_tasks, args.queue)
        queue = args.queue
    
    # 直接使用Python运行临时模块，而不是使用Celery命令
    cmd = [PYTHON_PATH, "-m", "celery_runner.bootstrap"]
    
    print(f"启动命令: {' '.join(cmd)}")
    print(f"临时模块路径: {temp_dir}")
    print(f"队列: {queue or '默认'}")
    
    # 添加临时目录到PYTHONPATH
    env["PYTHONPATH"] = f"{temp_dir}:{env.get('PYTHONPATH', '')}"
    
    try:
        # 执行命令
        subprocess.run(cmd, env=env)
    finally:
        # 清理临时文件
        # 注意: 如果需要保留日志或调试，可以注释掉下面的代码
        # import shutil
        # shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
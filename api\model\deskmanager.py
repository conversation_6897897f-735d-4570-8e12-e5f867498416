# -*- coding: utf-8 -*-


from dataclasses import dataclass
from sqlalchemy.engine import strategies
from bson import int64
from pygments.lexers import int_fiction


class DeskAddForm:
    vmid: str
    deskgroupid: str

class DeskSearchForm:
    name: str

class DeskGroupForm:
    name: str
    desksource: str
    glance_id: str
    count: int
    deskclass: str
    increase: str

class DeskGroupDeleteForm:
    ids: [int]

class DeskDeleteForm:
    id: int

class AddDeskForm:
    deskgroup_id: int
    count: int

class DeskListForm:
    deskgroup_id: int
    page: int
    pagecount: int
    searchstr: str


class DeskUpdateForm:
    id: int
    glanceid: str
    scheduled: str



# -*- coding: utf-8 -*-


from dataclasses import dataclass
from sqlalchemy.engine import strategies
from bson import int64
from sqlalchemy.orm import strategy_options
from pyrestful.types import str


    

class LDAPUserSearchForm:
    granttype: str
    domain: str
    searchstr: str
    pagecount: int
    page: int

class GrantedUserSearchForm:
    deskgroupid: int
    domain: str
    searchstr: str
    pagecount: int
    page: int

class DeskUserAllocUsersFrom:
    name: str
    domain: str
    username: str
    dnpath: str
    objectguid: str
    

class DeskUserAllocForm:
    deskgroupid: str
    users: [DeskUserAllocUsersFrom]

class DeskUserQueryUserOrGroupForm:
    granttype: str
    domain: str
    username: str
    details: str
    

class DeskUserAllocDeleteForm:
    id: int






import requests
import json
import settings
from api.grafana.client import Client
import time
import numpy as np
import requests
from requests.auth import HTTPBasicAuth

class CephClient(Client):

    token = ""
    def __init__(self):
        super().__init__()


    def grafana_get_ceph_usage(self):

        
        #query = """sum without (instance, pool_id, name) ((ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail)) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~"vms,images,volumes"}))"""
        #query = """sum without (instance, pool_id, name) ((ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail)) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~"^.*$"}))"""
        query = """sum without (instance) (ceph_cluster_total_used_bytes)"""
        
        res = self.query_pormQL_range_step(query)
        #data = json.loads(res)
        data = res["data"]["result"][0]["values"]
        v = [int(d[1]) for d in data]
        
        ceph_pool_stored = sum(v)/ len(v)  / 1024 /1024 / 1024 
        
        
        #query = """sum without (instance, pool_id, name) ((ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail)) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~"^.*$"}))"""
        #query = """sum without (instance, pool_id, name) ((ceph_pool_stored + ceph_pool_max_avail ) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~"^.*$"}))"""
        query = """sum without (instance) (ceph_cluster_total_bytes)"""
        #query = """sum without (instance, pool_id, name) ((ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail) ) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~"images|vms|volumes"}))"""
        
        res = self.query_pormQL_range_step(query)
        #data = json.loads(res)
        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]
        
        ceph_pool_max = sum(v)/ len(v) / 1024 / 1024 / 1024 
        
        ceph_pool_max = np.around(ceph_pool_max, 4)
        ceph_pool_stored = np.around(ceph_pool_stored, 4)
        
        return {"ceph_pool_max":ceph_pool_max, "ceph_pool_stored":ceph_pool_stored}
    





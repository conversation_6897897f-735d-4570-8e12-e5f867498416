import unittest
from unittest.mock import patch
import settings

# 必须用vip访问 不会自动redirct
#ceph_auth_uri = "http://***************:8443"
ceph_auth_uri = "http://************:8443"

# Patch the settings module for testing
class TestClient(unittest.TestCase):
    @patch('settings.CEPH_AUTH_URI', ceph_auth_uri)
    @patch('settings.CEPH_USER', "admin")
    @patch('settings.CEPH_PWD', "p@ssw0rd")
    def test_ceph_get_ceph_host(self):
        from client import Client  # Importing here to apply patches

        client = Client()
        res = client.FsClient.ceph_get_ceph_host(client)
        #print(res)
        
        #assert res 不为空
        self.assertTrue(res)
        # self.assertEqual(client.get_alert_url(), "http://test-alert-uri")
        # self.assertEqual(client.get_query_url(), "http://test-query-uri")


    @patch('settings.CEPH_AUTH_URI', ceph_auth_uri)
    @patch('settings.CEPH_USER', "admin")
    @patch('settings.CEPH_PWD', "p@ssw0rd")
    def test_ceph_check_ceph_balancing(self):
        from client import Client  # Importing here to apply patches

        client = Client()
        res = client.OsdClient.ceph_check_ceph_balancing(client)
        #print(res)
        #self.assertTrue(res)
        self.assertFalse(res)

    @patch('settings.CEPH_AUTH_URI', ceph_auth_uri)
    @patch('settings.CEPH_USER', "admin")
    @patch('settings.CEPH_PWD', "p@ssw0rd")
    def test_get_ceph_health(self):
        from client import Client  # Importing here to apply patches

        client = Client()
        res = client.OsdClient.fetch_ceph_health_minimal(client)
        #print(res)
        self.assertTrue(res)


if __name__ == '__main__':
    unittest.main()
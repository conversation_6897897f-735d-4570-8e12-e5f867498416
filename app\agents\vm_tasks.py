from app.celery import app
from api.libvirt.client import Client as LibvirtClient
from api.log.log import CustomLogger
from celery import Task
from nameko.standalone.rpc import ClusterRpcProxy
# from nameko.web.websocket import WebSocketHubProvider
from config import settings
import xml.etree.ElementTree as ET


import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class VmCreateTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # )   

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        vm_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"VM Name: {vm_name}")
        print(f"Form: {form}")
        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 


class VmCreateCallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "成功", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        task_name = self.name
        form = args[0]

        # new_logger.log(
        #     self.username, "虚拟机", "删除虚拟机", "失败", vm_name,
        #     "删除虚拟机: {},成功".format(vm_name)
        # ) 


@app.task(name="create_vm")
def create_vm(result, form):
    print(f"create_vm-> result: {result}")
    print(f"create_vm-> form: {form}")
    if not form:
        print("VM creation failed.")
        return False

    if form.get("status") == "failed":
        print("VM creation failed.")
        print(result.get("error"))
        return False

    domain_id = ""
    domain_name = ""
    
    form["interface_info"] = result[1]

    # 测试等10秒
    import time
    time.sleep(5)

    # data是创建虚机的json信息 调用libvirt创建虚机
    host_ip = form.get("host_ip", "")
    client = LibvirtClient()
    res = client.create_dom_vm(client, form)
    form["spice_port"] = res.get("spice_port", 0)
    form["vnc_port"] = res.get("vnc_port", 0)
    form["task_status"] = "success"
    form["status"] = res.get("status", "nostate")
    print("VM creation successful.")
    return form



@app.task(name="clone_vm")
def clone_vm(form):
    """
    虚拟机克隆
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient(host)
    client.vm_clone(client, vm_name)
    print(f"克隆虚拟机 VM: {vm_name}")
    return f"VM {vm_name} clone successfully."


@app.task(name="delete_vm")
def delete_vm(form):
    """
    删除单个
    :param form:
    :return:
    """
    vm_name = form.get("vm_name", "")
    host = form.get("host", "")
    # 删除虚拟机的逻辑
    client = LibvirtClient()
    client.del_vm(client, vm_name)
    print(f"删除虚拟机 VM: {vm_name}")
    return form




@app.task(name="open_vm")
def open_vm(form):
    """
    开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()

    try:
        res = client.start_vm(client, vm_name)
        if res: 
            print(f"虚拟机开机 VM: {vm_name}")
            form["task_status"] = "success"
            # 获取虚拟机XML并解析spice/vnc端口
            dom = client.get_vm_info_by_name(client, vm_name)
            vnc_port = dom.get("vnc_port", 0)
            spice_port = dom.get("spice_port", 0)
            
        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
    except Exception as e:
        print(f"虚拟机开机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["vnc_port"] = 0
        form["spice_port"] = 0

    return form


@app.task(name="close_vm")
def close_vm(form):
    """
    关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()

    try:
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机关机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"


    return form



@app.task(name="destroy_vm")
def destroy_vm(form):
    """
    强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机强制关机失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="pause_vm")
def pause_vm(form):
    """
    暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机暂停失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="recover_vm")
def recover_vm(form):
    """
    恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机恢复失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="reboot_vm")
def reboot_vm(form):
    """
    重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
        form["task_status"] = "success"
        dom = client.get_vm_info_by_name(client, vm_name)
       
        vnc_port = dom.get("vnc_port", 0)
        spice_port = dom.get("spice_port", 0)
           
        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
    except Exception as e:
        print(f"虚拟机重启失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
        form["vnc_port"] = 0
        form["spice_port"] = 0
    return form


@app.task(name="restart_vm")
def restart_vm(form):
    """
    强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vm_name = form.get("name")
    client = LibvirtClient()
    try:
        client.force_reboot_vm(client, vm_name)
        dom = client.get_vm_info_by_name(client, vm_name)
        vnc_port = dom.get("vnc_port", 0)
        spice_port = dom.get("spice_port", 0)
        
        form["vnc_port"] = vnc_port
        form["spice_port"] = spice_port
        print(f"虚拟机强制重启 VM: {vm_name}")
        form["task_status"] = "success"
    except Exception as e:
        print(f"虚拟机强制重启失败 VM: {vm_name}")
        print(f"错误信息: {e}")
        form["task_status"] = "failed"
    return form


@app.task(name="batch_delete_vm")
def batch_delete_vm(form):
    """
    批量删除
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        # 删除虚拟机的逻辑
        client.del_vm(client, vm_name)
        print(f"删除虚拟机 VM: {vm_name}")
    return f"批量删除虚拟机成功"


@app.task(name="batch_open_vm")
def batch_open_vm(form):
    """
    批量开机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.start_vm(client, vm_name)
        print(f"虚拟机开机 VM: {vm_name}")
    return f"批量开机虚拟机成功"


@app.task(name="batch_close_vm")
def batch_close_vm(form):
    """
    批量关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.stop_vm(client, vm_name)
        print(f"虚拟机关机 VM: {vm_name}")
    return f"批量关机虚拟机成功"


@app.task(name="batch_destroy_vm")
def batch_destroy_vm(form):
    """
    批量强制关机
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_stop_vm(client, vm_name)
        print(f"虚拟机强制关机 VM: {vm_name}")
    return f"批量强制关机虚拟机成功"


@app.task(name="batch_pause_vm")
def batch_pause_vm(form):
    """
    批量暂停
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.suspend_vm(client, vm_name)
        print(f"虚拟机暂停 VM: {vm_name}")
    return f"批量暂停虚拟机成功"


@app.task(name="batch_recover_vm")
def batch_recover_vm(form):
    """
    批量恢复
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.resume_vm(client, vm_name)
        print(f"虚拟机恢复 VM: {vm_name}")
    return f"批量恢复虚拟机成功"


@app.task(name="batch_reboot_vm")
def batch_reboot_vm(form):
    """
    批量重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.reboot_vm(client, vm_name)
        print(f"虚拟机重启 VM: {vm_name}")
    return f"批量重启虚拟机成功"


@app.task(name="batch_restart_vm")
def batch_restart_vm(form):
    """
    批量强制重启
    :param form:
    :return:
    """
    host = form.get("host", "")
    vms = form.get("vms", [])
    client = LibvirtClient(host)

    for vm in vms:
        vm_name = vm.get("vm_name")
        client.force_reboot_vm(client, vm_name)
        print(f"虚拟机强制重启 VM: {vm_name}")
    return f"批量强制重启虚拟机成功"

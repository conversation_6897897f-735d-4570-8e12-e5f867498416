import importlib
import os

import libvirt
import xml.etree.ElementTree as ET
from api.libvirt.utils.utils import capacity_to_bytes
from api.libvirt.utils.vmxml.storage_xml import StoragePool, StorageVolume, Target, BackingStore
from api.libvirt.utils.vmxml.storage_xml import default_local_pool_xml


class Storage:
    storage_state = {
        libvirt.VIR_STORAGE_POOL_INACTIVE: "inactive",
        libvirt.VIR_STORAGE_POOL_BUILDING: "building",
        libvirt.VIR_STORAGE_POOL_RUNNING: "running",
        libvirt.VIR_STORAGE_POOL_DEGRADED: "degraded",
        libvirt.VIR_STORAGE_POOL_INACCESSIBLE: "inaccessible",
    }

    def get_storage_pools_type(self):
        pool_types = []
        for pool in self.conn.listAllStoragePools():
            print(pool.info(), pool.name(), pool.UUID())
            pool_types.append(pool.info()[0])
        return pool_types

    '''
    获取存储池名称列表
    '''

    def get_storage_pool_names(self):
        pool_names = []
        for pool in self.conn.listAllStoragePools():
            pool_names.append(pool.name())
        return pool_names

    '''
    获取存储池信息
    '''

    def get_storage_pool_info(self, pool_name):
        pool = self.conn.storagePoolLookupByName(pool_name)
        pool_info = pool.info()
        pool_state = pool_info[0]
        pool_capacity = pool_info[1]
        pool_available = pool_info[2]
        pool_in_use = pool_info[3]

        pool_uuid = pool.UUIDString()

        state = Storage.storage_state.get(pool_state, "unknown")
        # 获取存储池的 XML 配置
        pool_xml = pool.XMLDesc()

        # 解析 XML
        root = ET.fromstring(pool_xml)
        target = root.find('target')
        if target is not None:
            # 获取存储路径
            path = target.find('path')
            if path is not None:
                path = path.text
        else:
            path = ""

        # 获取存储池中的所有卷
        volumes = pool.listVolumes()
        volume_details = []
        for volume_name in volumes:
            volume = pool.storageVolLookupByName(volume_name)
            volume_info = volume.info()
            volume_details.append({
                "name": volume_name,
                "type": volume_info[0],
                "capacity": volume_info[1],
                "allocation": volume_info[2]
            })

        info = {
            "name": pool_name,
            "uuid": pool_uuid,
            "storage_local_dir": path,  # 存储路径
            "status": pool_state,  # 存储池状态
            "state": state,  # 存储池状态
            "capacity": pool_capacity,  # 存储池容量
            "available": pool_available,  # 存储池可用空间
            "allocation": pool_in_use,  # 存储池已用空间
            "xml": pool_xml,  # 存储池 XML 配置
            "volumes": volume_details  # 存储池中的所有卷
        }

        return info

    '''
    创建存储池
    '''

    def create_storage_pool(self, pool_name, pool_type, target_path, **kwargs):

        pool = self.conn.storagePoolDefineXML(
            f"""
            <pool type='{pool_type}'>
                <name>{pool_name}</name>
                <target>
                    <path>{target_path}</path>
                </target>
                {kwargs}
            </pool>
            """,
            0
        )

        pool.create(libvirt.VIR_STORAGE_POOL_CREATE_WITH_BUILD)
        pool.setAutostart(True)

        return pool

    def test_create_storage_pool(self, form):
        """
        创建存储池测试
        :param form:
        :return:
        """
        s = StorageXml('storagexml')
        pool_xml = s.call_plugin_methods("local", 'create_pool_xml', form)
        print("存储池：", form["name"], pool_xml)

        return pool_xml

    '''
    重命名存储池
    '''

    def rename_storage_pool(self, old_name, new_name):
        pool = self.conn.storagePoolLookupByName(old_name)

        # 获取存储池的XML描述
        xml_desc = pool.XMLDesc(0)

        # 解析XML描述
        root = ET.fromstring(xml_desc)

        # 修改存储池名称
        name_elem = root.find('name')
        if name_elem is not None:
            name_elem.text = new_name

        # 由于使用ElementTree，不需要转换为字符串，直接使用树结构
        new_xml_desc = ET.tostring(root, encoding='unicode')

        # 停止存储池
        if pool.isActive():
            pool.destroy()

        # 取消定义旧的存储池
        pool.undefine()

        # 重新定义存储池
        self.conn.storagePoolDefineXML(new_xml_desc, 0)

        # 启动存储池
        pool = self.conn.storagePoolLookupByName(new_name)
        pool.create()
        pool.setAutostart(True)

        return pool.name()


    def delete_storage_pool(self, pool_name):
        '''
        删除存储池
        '''
        try:
            pool = self.conn.storagePoolLookupByName(pool_name)
            if pool.isActive():
                pool.destroy()
            try:
                pool.delete()
            except libvirt.libvirtError as e:
                print(f"删除存储池 {pool_name} 时发生错误: {str(e)}")
                return False
                # if "Directory not empty" in str(e):
                #     print(f"警告: 存储池 {pool_name} 的目录非空，但将继续删除存储池定义")
                # else:
                #     raise
            pool.undefine()
            return True
        except libvirt.libvirtError as e:
            print(f"删除存储池 {pool_name} 时发生错误: {str(e)}")
            return False

    '''
    暂停存储池
    '''

    def pause_storage_pool(self, pool_name):
        pool = self.conn.storagePoolLookupByName(pool_name)
        if pool.isActive():
            pool.destroy()

        pool_info = pool.info()
        pool_state = pool_info[0]
        return pool_state

    '''
    启动存储池
    '''

    def unpause_storage_pool(self, pool_name):
        pool = self.conn.storagePoolLookupByName(pool_name)
        if not pool.isActive():
            pool.create(libvirt.VIR_STORAGE_POOL_CREATE_WITH_BUILD)
            # 设置自动启动
            pool.setAutostart(True)

        pool_info = pool.info()
        pool_state = pool_info[0]
        return pool_state

    '''
    创建存储池中的卷 TODO 完善参数
    '''

    def create_storage_pool_volume_test(self, form):
        """
        创建存储池中的卷
        """
        pool = self.conn.storagePoolLookupByName(form.get('pool_name'))
        if pool is None:
            print(f"找不到存储池 {form.get('pool_name')} ")
            return False

        name = form.get('name')
        capacity = str(capacity_to_bytes(form.get('capacity')))
        vol_format = form.get('vol_format', "")
        path = form.get('path', "")
        back = form.get('back', {})  # backingStore需要指定 存储卷和存储池
        if path == "":
            # 获取存储池的 XML 配置
            pool_xml = pool.XMLDesc()

            # 解析 XML
            root = ET.fromstring(pool_xml)
            target = root.find('target')
            if target is not None:
                # 获取存储路径
                path = target.find('path').text

        vol = path + "/" + name
        root = StorageVolume()
        root.set_name(name)  # 卷名称
        cap = root.set_capacity(capacity)  # 卷容量
        allow = root.set_allocation()  # 卷已分配容量， 默认0

        target = Target()
        root.add_child(target)
        target.set_format(vol_format)
        target.set_path(path)

        # backingStore设置
        if back and back["pool_name"] and back["vol_name"]:
            back_pool_name = back["pool_name"]
            back_vol_name = back["vol_name"]
            back_pool = self.conn.storagePoolLookupByName(back_pool_name)
            if back_pool is None:
                print(f"找不到backStore存储池 {back_pool_name} ")
                return False
            vol = self.get_storage_pool_volume_info(self, back_pool_name, back_vol_name)

            back_store = BackingStore()
            root.add_child(back_store)
            back_store.set_path(vol["path"])
            back_store.set_format(vol["format"])

        vol_xml = root.format_xml()

        print(vol_xml)
        vol = pool.createXML(vol_xml, 0)

        # 刷新存储池
        pool.refresh()
        return True

    def create_storage_pool_volume(self, form):
        """
        创建存储池中的卷
        """
        print(form)
        pool = self.conn.storagePoolLookupByName(form.get('storage_pool_name'))
        if pool is None:
            print(f"找不到存储池 {form.get('storage_pool_name')} ")
            return False

        s = StorageXml('storagexml')
        volume_xml = s.call_plugin_methods(form['storage_type_code'], 'create_xml', form)
        print("存储卷：", form["name"], volume_xml)

        vol = pool.createXML(volume_xml, 0)

        # 刷新存储池
        pool.refresh()
        return True

    def ensure_remote_disk_exists(self, form):
        try:
            # 获取存储池对象
            pool = self.conn.storagePoolLookupByName(form.get('storage_pool_name'))
            pool.refresh()  # 刷新池以获取最新状态

            # 检查是否存在该存储卷
            try:
                volume = pool.storageVolLookupByName(form.get('name'))
                print(f"Volume {form.get('name')} already exists.")
                return True
            except libvirt.libvirtError as e:
                print(f"Volume {form.get('name')} not found. Creating new volume.")

                # 定义新存储卷
                volume_xml = f"""
            <volume>
                <name>{form.get('name')}</name>
                <capacity unit="B">{form.get('capacity')}</capacity>
                <allocation unit="B">0</allocation>
                <target>
                    <format type="{form.get('storage_type_code')}"/>
                    <permissions>
                        <mode>0644</mode>
                    </permissions>
                </target>
            </volume>
            """
                volume = pool.createXML(volume_xml, 0)
                pool.refresh()
                print(f"Volume {form.get('name')} created successfully.")
                return True

        except libvirt.libvirtError as e:
            print(f"Failed to ensure remote disk: {e}")
            return False

    def clone_storage_pool_volume(self, form):
        """
        克隆存储池中的卷
        """
        print(form)
        source_vol_name = form.get("volume_name")
        clone_vol_name = form.get("new_volume_name")
        pool = self.conn.storagePoolLookupByName(form.get('storage_pool_name'))
        if pool is None:
            print(f"找不到存储池 {form.get('storage_pool_name')} ")
            return False
        source_vol = pool.storageVolLookupByName(source_vol_name)
        source_xml = source_vol.XMLDesc(0)
        r = ET.fromstring(source_xml)
        print(source_vol_name, source_xml)
        name = r.find('name')
        if name is not None:
            name.text = clone_vol_name
        else:
            print("找不到卷名")
            return False
        clone_xml = ET.tostring(r, encoding='unicode')

        clone_vol = pool.createXMLFrom(clone_xml, source_vol, 0)

        # 刷新存储池
        pool.refresh()
        return True

    def get_storage_pool_volumes(self, pool_name):
        """
        获取存储池中的所有卷
        """
        pool = self.conn.storagePoolLookupByName(pool_name)
        volumes = []
        for vol in pool.listAllVolumes():
            volumes.append(vol.name())
        return volumes

    def get_storage_pool_volume_info(self, pool_name, volume_name):
        '''
        获取存储池中的卷信息
        '''
        pool = self.conn.storagePoolLookupByName(pool_name)
        if pool is None:
            print(f"找不到存储池 {pool_name} ")
            return None
        vol = pool.storageVolLookupByName(volume_name)
        if vol is None:
            print(f"找不到卷 {volume_name} ")
            return None

        vol_xml = vol.XMLDesc()
        root = ET.fromstring(vol_xml)
        # 查找 <format> 元素并获取其 type 属性
        format_element = root.find('.//target/format')
        volume_format = ""
        if format_element is not None:
            volume_format = format_element.get('type')

        info = {
            "name": vol.name(),
            "type": vol.info()[0],
            "capacity": vol.info()[1],
            "allocation": vol.info()[2],
            "xml": vol.XMLDesc(),
            "format": volume_format,
            "path": vol.path(),
            "key": vol.key(),
        }
        return info

    def delete_storage_pool_volume(self, pool_name, volume_name):
        '''
        删除存储池中的卷
        '''
        pool = self.conn.storagePoolLookupByName(pool_name)
        if pool is None:
            print(f"找不到存储池 {pool_name} ")
            return None
        try:
            vol = pool.storageVolLookupByName(volume_name)
            if vol is None:
                print(f"找不到卷 {volume_name} ")
                return False
            vol.delete(0)
            # 刷新存储池
            pool.refresh()
            return True
        except libvirt.libvirtError as e:
            if "Storage volume not found" in str(e):
                print(f"存储卷 {volume_name} 不存在")
                return True  # 返回 True 因为卷已经不存在，可以认为删除成功
            raise  # 重新抛出其他类型的错误

    def rename_storage_pool_volume(self, pool_name, current_volume_name, new_volume_name):
        '''
        重命名存储池中的卷
        '''
        try:
            # 查找存储池
            pool = self.conn.storagePoolLookupByName(pool_name)
            if pool is None:
                print(f"找不到存储池 {pool_name}")
                return False

            # 查找当前卷
            current_vol = pool.storageVolLookupByName(current_volume_name)
            if current_vol is None:
                print(f"找不到卷 {current_volume_name}")
                return False

            # 获取当前卷的 XML 描述
            vol_xml = current_vol.XMLDesc()

            # 修改 XML 中的卷名
            import xml.etree.ElementTree as ET
            root = ET.fromstring(vol_xml)
            name_elem = root.find('name')
            if name_elem is not None:
                name_elem.text = new_volume_name

            # 将修改后的 XML 转换回字符串
            modified_xml = ET.tostring(root, encoding='unicode')

            # 使用修改后的 XML 克隆卷
            new_vol = pool.createXML(modified_xml, 0)

            # 删除旧卷
            current_vol.delete(0)

            # 刷新存储池
            pool.refresh()

            print(f"成功将卷 {current_volume_name} 重命名为 {new_volume_name}")
            return True

        except Exception as e:
            print(f"重命名卷时发生错误: {e}")
            return False


class StorageXml:

    def __init__(self, plugin_dir):
        self.plugin_dir = plugin_dir
        self.package_perfix = "api.libvirt.mod.storagexml"
        self.plugins = self.load_plugins()
        self.plugin = ""

    def load_plugins(self):
        plugins = {}

        # 获取当前文件的绝对路径和所在目录路径

        current_file = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_file)

        # 构建插件目录的绝对路径
        plugin_abs_dir = os.path.join(current_dir, self.plugin_dir)

        print(f"Plugin directory path: {plugin_abs_dir}")
        if not os.path.exists(plugin_abs_dir):
            print("路径不存在")

        for filename in os.listdir(plugin_abs_dir):
            if filename.endswith('.py') and filename != '__init__py':
                module_name = filename[:-3]
                module_path = f'{self.package_perfix.replace("/", ".")}.{module_name}'
                module = importlib.import_module(module_path)

                for name in dir(module):
                    obj = getattr(module, name)
                    if isinstance(obj, type):
                        plugins[name] = obj

        return plugins

    def get_plugin_methods(self, plugin_name):
        plugin = self.plugins.get(plugin_name)
        if plugin:
            return [method for method in dir(plugin) if callable(getattr(plugin, method))]

        return []

    def call_plugin_methods(self, plugin_name, method_name, *arg, **kwargs):

        plugin_class = self.plugins.get(plugin_name)
        if plugin_class:
            # 实例化插件类
            plugin_instance = plugin_class()

            method = getattr(plugin_instance, method_name, None)
            if callable(method):
                return method(*arg, **kwargs)

        return None

    def get_all_class(self):
        pass

    # 创建存储对应的xml文件
    def create_xml(self, form):
        """
        创建对应的xml文件
        :param form:
        :return:
        """
        r = self.call_plugin_methods(form['type'], "create_xml", form)

        return r

'''
Created on Sep 6, 2022

@author: ct
'''
from api.openstack.client import Client
from dataclasses import dataclass
from dacite import from_dict

name = "虚拟机运行状态"
weigth = 10

@dataclass
class Instances(object):
    id : str
    name : str
    status : str
    
def check():
    print("check method")
    client = Client()
    vms = client.NovaClient.openstack_get_all_instance_detail(client)
    data = []
    # [active , shutoff , error]
    count = [0,0,0]
    for vm  in vms:
        ins = from_dict(data_class=Instances, data=vm)
        if ins.__dict__["status"] == "ACTIVE":
            count[0] = count[0] + 1
        elif ins.__dict__["status"] == "SHUTOFF":
            count[1] = count[1] + 1
        elif ins.__dict__["status"] == "ERROR":
            count[2] = count[2] + 1

    if count[2] == 0:
        res = "ok"
    elif count[2] < 3 and count[2] > 0 :
        res = "warning"
    elif  count[2] >3  :
        res = "error"
            
    result = {
        "key": "vms_status",
        "name": name,
        "weigth": weigth,
        "result": res,    # ok (V), warning (!) ,error (X)
        "columns": [
            {
                "title":"虚机总数",
                "key":"total",
                "tooltip":True
            },
            {
                "title":"运行中",
                "key":"active",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"关机",
                "key":"shutoff",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"错误状态",
                "key":"error",
                "tooltip":True,
                "align":"center"
            }
        ],
        "data": [
            {
                "total":count[0]+count[1]+count[2],
                "active":count[0],
                "shutoff":count[1],
                "error":count[2]
            }
        ]
    }
    return result





# -*- coding: utf-8 -*-


from dataclasses import dataclass

@dataclass
class Flavor(object):
    id : str
    name : str
    
class FlavorCreateFrom:
    name : str
    ram : int
    vcpus : int
    disk : int
    extdisk : int
    
@dataclass
class FlavorDetail(object):
    id : str
    name : str
    disk : int
    ram : int
    #"OS-FLV-EXT-DATA:ephemeral" : str
    vcpus : int
    rxtx_factor : float
    links: list
    

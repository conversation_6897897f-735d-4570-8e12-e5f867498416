# -*- coding: utf-8 -*-
import uuid
from calendar import c
from unittest import result
from venv import create
import tornado.ioloop
import pyrestful.rest
from db.model.hci.template import Template
from api.prometheus.client import Client as Pclient

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from util.tools import convert_to_bytes
from db.model.hci.compute import Domain, Host, Pool, Cluster, DomainDisk, DomainInterface
from db.model.hci.network import Switch, Router, RouterPort, RouterTable, SwitchPortGroups, SwitchPorts
from db.model.hci.storage import StoragePool, StorageVolume
from app.tasks.vm_tasks import (
    create_vm_callback, 
    create_vm_callback_new, 
    delete_vm_callback,
    distach_op_vm_open,
    distach_op_vm_close,
    distach_op_vm_destroy,
    distach_op_vm_pause,
    distach_op_vm_recover,
    distach_op_vm_reboot,
    distach_op_vm_restart,
    )
from app.agents.vm_tasks import (
    create_vm, 
    clone_vm,
    delete_vm
    )
from app.tasks.disk_tasks import (
    create_sub_disk_callback, 
    create_sub_disk_with_template,
    
    )
from app.agents.disk_tasks import (
    create_sub_disk, 
    
    )
from app.tasks.network_tasks import (
    create_bridge_port_callback
    )
from app.agents.network_tasks import (
    domain_create_bridge_port
    )
from app.tasks.cdrom_tasks import create_cdrom
from math import ceil
from sqlalchemy import asc, desc
from api.log.log import CustomLogger
from celery import Celery, chord, group
from api.libvirt.client import Client as LClient
from settings import QUEUE_NAME
from util.tools import generate_unique_port_name

import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class VmHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v5/vm/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_host_overview(self, _id):
        role = self.get_cookie('role', "")
        data = {}

        with self.session_scope() as session:

            vm = session.query(Domain).filter(Domain.id == _id).first()
            # data["vm_count"] = str(len(vms))
            logic_cpu_count = 0
            logic_mem_count = 0
            logic_disk_count = 0
            # for vm in vms:
            logic_cpu_count += int(vm.vcpu)
            logic_mem_count += int(vm.memory)
            data = vm.to_dict_merge()

            # Step 5: 查询所有 物理cpu，逻辑cpu，内存，磁盘
            # data["logic_cpu_count"] = str(logic_cpu_count)
            # data["logic_cpu_rate"] = str(0)
            # data["logic_mem_count"] = str(logic_mem_count)
            # data["logic_disk_count"] = str(logic_disk_count) # 存疑

            c = Pclient()
            hosts = []
            ip_pattern_parts = [f"{d['ip']}:9100" for d in hosts]
            ip_regex = "|".join(ip_pattern_parts)
            # all_cpu_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            all_cpu_hz = c.query_vector_by_query(f'sum(node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}})')
            use_cpu_hz = c.query_vector_by_query(f'sum((node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}} / node_cpu_frequency_max_hertz{{job="node_exporter", instance=~"{ip_regex}"}}))')
            if all_cpu_hz:
                data["cpu_all_hz"] = all_cpu_hz[0]["value"][1]
                data["cpu_use_hz"] = use_cpu_hz[0]["value"][1]
            else:
                data["cpu_all_hz"] = 0
                data["cpu_use_hz"] = 0

            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            if all_mem_count:
                data["mem_all_count"] = all_mem_count[0]["value"][1]
                data["mem_use_count"] = use_mem_count[0]["value"][1]
            else:
                data["mem_all_count"] = 0
                data["mem_use_count"] = 0

            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            if all_disk_count:
                data["disk_all_count"] = all_disk_count[0]["value"][1]
                data["disk_use_count"] = use_disk_count[0]["value"][1]
            else:
                data["disk_all_count"] = 0
                data["disk_use_count"] = 0

        return {"msg": "ok", "code": 200, "data": data}

    @get(_path="/v5/vm/detail/{domain_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_vm_detail(self, domain_id):

        with self.session_scope() as session:
            # 查询虚机
            domain = session.query(Domain).join(Host, Domain.host_id == Host.id).filter(Domain.id == domain_id).first()
            if domain:
                data = Domain.to_dict_merge(domain)
                return data

        return {"msg": "没有找到虚机", "code": "404"}

    @post(_path="/v5/vm/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_list(self, form):

        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        recycle = form.get('recycle', False)

        with self.session_scope() as session:
            if recycle:
                query = session.query(Domain).filter(Domain.domain_recycle_id == "1")
            if not recycle:
                query = session.query(Domain).filter(Domain.domain_recycle_id == "0")

            # 搜索功能，根据指定字段进行搜索（这里假设搜索的字段为 name）
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%"))

            # 排序功能
            if order_by:
                if order_type == 'asc':
                    query = query.order_by(asc(getattr(Domain, order_by)))
                else:
                    query = query.order_by(desc(getattr(Domain, order_by)))

            # 计算总记录数
            total_records = query.count()

            # 分页
            domains = query.limit(per_page).offset((page - 1) * per_page).all()

            # 将结果转换为字典列表
            domain_list = [Domain.to_dict(domain) for domain in domains]

            # 计算总页数
            total_pages = ceil(int(total_records) / per_page)
            # 返回分页信息和数据
        return {
            "msg": "获取虚机列表成功",
            "code": 200,
            "total": total_records,
            "data": domain_list
        }

        # return {"msg": "获取虚机列表失败", "code": 500}

    @post(_path="/v5/vm/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_vm_list(self, form):

        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        recycle = form.get('recycle', False)
        type = form.get('type', 'recource')
        _id = form.get('_id', '')

        with self.session_scope() as session:
            # 首先创建基础查询
            query = session.query(Domain)

            # 根据 recycle 状态过滤
            recycle_value = "1" if recycle else "0"
            query = query.filter(Domain.domain_recycle_id == recycle_value)

            # 根据不同类型进行过滤
            if type == 'recource':
                # 获取所有相关的域
                query = query.join(Host).join(Cluster).join(Pool)

            elif type == 'pool':
                query = query.join(Host).join(Cluster).join(Pool).filter(Pool.id == _id)

            elif type == 'cluster':
                query = query.join(Host).join(Cluster).filter(Cluster.id == _id)

            elif type == 'host':
                query = query.join(Host).filter(Host.id == _id)

            # 添加搜索条件
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%"))

            # 添加排序
            if hasattr(Domain, order_by):
                order_column = getattr(Domain, order_by)
                if order_type == 'asc':
                    query = query.order_by(asc(order_column))
                else:
                    query = query.order_by(desc(order_column))

            # 获取总记录数
            total_records = query.count()

            # 执行分页
            domains = query.offset((page - 1) * per_page).limit(per_page).all()

            # 将结果转换为字典列表
            domain_list = [domain.to_dict() for domain in domains]

            # # 计算总页数
            # total_pages = ceil(int(total_records) / per_page)
            new_logger.log(
                self.username, "虚机管理", "查询虚机", "成功", "role", "{}:{},成功".format("查询虚拟机", "")
            )
            return {
                "msg": "获取虚机列表成功",
                "code": 200,
                "total": total_records,
                "data": domain_list
            }

    @post(_path="/v5/vm/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_create(self, form):
        role = self.get_cookie('username', "")

        vm_name = form.get("vm_name", "")
        host_ip = form.get("host_ip", "")
        form["domain_id"] = str(uuid.uuid4())

        # 查出host信息 创建虚机空壳 但是不启动 由celery任务来启动
        with self.session_scope() as session:
            # 根据host_ip查出host信息
            host = session.query(Host).filter(Host.ip == host_ip).first()
            if not host:
                return {"status": "failed", "message": "主机不存在"}

            form["host_id"] = str(host.id)
            domain = Domain()
            domain.id = form.get("domain_id", "")
            domain.name = vm_name
            domain.os_type = form.get("os_type", 0)
            domain.host_id = str(host.id)
            domain.cluster_id = host.cluster_id
            domain.pool_id = host.pool_id
            domain.status = "creating"
            domain.vcpu = form.get("vcpu_unit", 1)
            domain.memory = form.get("memory_unit", 5)

            domain.os_version = "ubuntu"
            domain.remark = "remark"
            domain.is_ha = 0
            domain.auto_migrate = 0
            domain.is_losing_contact = 0
            domain.domainname = "instance"
            domain.uuid = "34343"
            domain.hmemory = form.get("memory_unit", 5)
            domain.cpu_arch = "x86"
            domain.vnc_port = "0000"
            domain.spice_port = "0000"
            domain.bind_ip_list = "dhcp"
            domain.is_persistence = 1
            domain.secret_id = "0"
            domain.secret_alg = "0"
            domain.domain_recycle_id = "0"
            domain.safe_status = 0
            domain.is_hide = 0
            domain.defense_status = 0
            domain.is_ignore = 0
            session.add(domain)
            session.commit()
            
            
            disks_form = []
            for disk in form.get("disk", []):
                # 检查卷是否存在
                disk_id = disk.get("disk_id", "")
                pool_id = disk.get("pool_id", "")
                path = disk.get("path", "")
                disk_type = disk.get("disk_type", "")
                # if not disk_type or disk_type == "file":
                #     disk["disk_type"] = 'qcow2'
                #     disk_type = 'qcow2'
                disk_name = disk.get("disk_name", "")
                size = disk.get("size", 0)
                disk_unit_type = disk.get("disk_unit_type", "GB")
                capacity = convert_to_bytes(size, disk_unit_type)
                storage_pool = session.query(StoragePool).filter_by(
                        id=pool_id).first()
                if storage_pool is None:
                        return {"code": 200, "msg": "存储池不存在"}
                disk["storage_type_code"] = disk.get("storage_type_code", "qcow2")   
                disk["capacity"] = capacity
                disk["type_code"] = storage_pool.type_code
                disk["pool_name"] = storage_pool.name
                # 将数据存入数据表中
                if path == "":
                    disk["path"] = storage_pool.storage_local_dir
                    path = storage_pool.storage_local_dir
                if not disk_id:
                    # 存储卷不存在，进行创建
                    disk["is_exist"] = False
                    data = {
                        "name": disk_name,
                        "storage_pool_id": pool_id,
                        # "type_code": form["volume_format"],
                        "join_type": 3,
                        "path": path,
                        "encrypt": 0,
                        "status": 4,
                        "capacity": capacity,
                        "allocation": capacity,
                        "preallocation": 1,
                        "protocol_type": "",
                        "volume_type": "qcow2",

                    }
                    volume = StorageVolume.from_dict(data)
                    session.add(volume)
                    session.commit()
                    disk["disk_id"] = volume.id
                else:
                    volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                    disk["storage_type_code"] = volume.volume_type
                    disk["is_exist"] = True
                
                disks_form.append(disk)

            # cdrom = form.get("cdrom", "")
            # for i in cdrom:
            #     volume = session.query(StorageVolume).filter_by(id=disk_id).first()
                
            
            # form["disk"] = disks_form    
               
            interface = form.get("interface", "")
            nets_form = []
            for i in interface:
                net_form = i
                switch_port_id = i.get("switch_port_id", "")
               
                switch_port_group_id = i.get("net_id", "")
                # 通过 switch_port_group_id 获取端口组及其关联的交换机信息
                result = session.query(SwitchPortGroups, Switch) \
                    .join(Switch, SwitchPortGroups.switchs_id == Switch.id) \
                    .filter(SwitchPortGroups.id == switch_port_group_id) \
                    .first()

                if not result:
                    raise ValueError(f"未找到端口组 {switch_port_group_id} 或其关联的交换机")
                port_group, switch = result  # 解构结果
                port_name = generate_unique_port_name()
                
                if not switch_port_id:
                    # 新建端口数据库信息
                    switch_port = SwitchPorts()
                    switch_port.name = port_name
                    switch_port.switchs_id = str(switch.id)
                    switch_port.switch_port_group_id = switch_port_group_id
                    session.add(switch_port)
                    session.commit()
                    switch_port_id = switch_port.id
                net_form["switch_port_id"] = switch_port_id
                net_form['port_name'] = port_name
                net_form['switch'] = switch.to_dict()
                net_form['switch_group'] = port_group.to_dict()
                net_form['switch_id'] = str(switch.id)
                net_form['switch_port_group_id'] = switch_port_group_id 
                net_form["network"] = switch.ovs_name
                nets_form.append(net_form)
        form["interface"] = nets_form

        # 异步调用多任务集合点来完成
        # TODO
        queue = "queue_" + host_ip
        
                
        # 子任务链：创建磁盘 + 更新数据库（使用默认队列）
        disk_chain = (
            create_sub_disk.s(vm_name, disks_form).set(queue=queue) |
            create_sub_disk_callback.s().set(queue=QUEUE_NAME)
        )
        
        # 子任务链：创建桥接端口 + 更新数据库（使用默认队列）
        bridge_chain = (
            domain_create_bridge_port.s(nets_form).set(queue=queue) |
            create_bridge_port_callback.s().set(queue=QUEUE_NAME)
        )

        # 构建子任务组
        tasks = group([
            disk_chain,
            bridge_chain,
        ])

        # 创建最终回调链：先执行 create_vm_callback，再执行 update_database_callback（使用默认队列）
        final_chain = (
            create_vm.s(form=form).set(queue=queue) |
            create_vm_callback.s().set(queue=QUEUE_NAME)
        )

        # 启动 chord 任务
        result = chord(tasks)(final_chain)
        # tasks = [create_sub_disk.s(vm_name, form), create_bridge_port.s(vm_name, form)]
        # result = chord(tasks)(create_vm_callback_new.s(form))
        # new_logger.log(
        #     self.username, "虚机管理", "创建虚机", "成功", "role", "{}:{},失败".format("创建虚拟机", vm_name)
        # )
        return {"msg": "已开始创建虚机", "code": 200}

    @post(_path="/v5/vm/create/use/template", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_create_with_template(self, form):
        role = self.get_cookie('username', "")

        vm_name = form.get("vm_name", "")
        host_ip = form.get("host_ip", "")
        template_id = form.get("template_id", "")

        # 查出host信息 创建虚机空壳 但是不启动 由celery任务来启动
        with self.session_scope() as session:
            # 根据host_ip查出host信息
            host = session.query(Host).filter(Host.ip == host_ip).first()
            if not host:
                return {"status": "failed", "message": "主机不存在"}

            template = session.query(Template).filter(Template.id == template_id).first()
            if not template:
                return {"status": "failed", "message": "模板不存在"}

            domain = Domain()
            domain.name = vm_name
            domain.os_type = form.get("os_type", 0)
            domain.host_id = str(host.id)
            domain.cluster_id = host.cluster_id
            domain.pool_id = host.pool_id
            domain.status = "creating"
            domain.vcpu = template.vcpu
            domain.memory = template.memory

            domain.os_version = "ubuntu"
            domain.remark = "remark"
            domain.is_ha = 0
            domain.auto_migrate = 0
            domain.is_losing_contact = 0
            domain.domainname = "instance"
            domain.uuid = "34343"
            domain.hmemory = form.get("memory_unit", 5)
            domain.cpu_arch = template.cpu_arch
            domain.vnc_port = "0000"
            domain.spice_port = "0000"
            domain.bind_ip_list = "dhcp"
            domain.is_persistence = 1
            domain.secret_id = "0"
            domain.secret_alg = "0"
            domain.domain_recycle_id = "0"
            domain.safe_status = 0
            domain.is_hide = 0
            domain.defense_status = 0
            domain.is_ignore = 0
            session.add(domain)
            session.commit()

        # 异步调用多任务集合点来完成
        tasks = [create_sub_disk_with_template.s(vm_name, template)]
        result = chord(tasks)(create_vm_callback_new.s(form))
        return {"msg": "已开始创建虚机", "code": 200}

    @post(_path="/v5/vm/op/full/clone/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_full_clone_vm(self, vm_id, form):
        """
        克隆虚拟机
        """
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        result = (clone_vm.apply_async(form))

        # try:
        #     libvirtClient = LibvirtClient(host=host)
        #
        #     r = libvirtClient.vm_clone(libvirtClient, form)
        #     new_logger.log(
        #         self.username, "虚拟机", "克隆", "成功", role,
        #         "虚拟机克隆: {},成功".format(vm_name)
        #     )
        # except  Exception as e:
        #     new_logger.log(
        #         self.username, "虚拟机", "克隆", "失败", role,
        #         "虚拟机克隆: {},失败".format(vm_name)
        #     )
        #     traceback.print_exc()
        #     return {"msg": "虚拟机克隆操作失败", "code": 500}

        return {"msg": "虚拟机克隆操作完毕", "code": 200}

    @post(_path="/v5/vm/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_delete(self, form):
        role = self.get_cookie('role', "")
        host = form.get("host_ip", "")
        ids = form.get("ids", [])
        names = form.get("names", [])

        failed_vms = []
        success_count = 0

        try:
            queue = "queue_" + host
            # libvirtClient = LClient(host=host)

            for _id, vm_name in zip(ids, names):
                try:
                    # 删除虚拟机及
                    try:
                        delete_form = {
                            "vm_name": vm_name,
                            "host": host
                        }
                        result = (delete_vm.apply_async((delete_form,),
                                                         queue=queue,
                                                         link=delete_vm_callback.s().set(queue=QUEUE_NAME)))
                        # libvirtClient.del_vm(libvirtClient, vm_name)
                    except Exception as e:
                        if "Domain not found" in str(e):
                            print(f"虚拟机 {vm_name} 不存在，继续删除数据库记录")
                        else:
                            raise e

                    # 删除数据库记录
                    with self.session_scope() as session:
                        # 删除虚拟机和磁盘的绑定关系，磁盘本身不做删除
                        session.query(DomainDisk).filter(DomainDisk.domain_id == _id).delete()
                        
                        # 查询和虚拟机绑定的网络端口id
                        switch_port_ids_info = session.query(DomainInterface.switch_port_id)\
                                            .filter(DomainInterface.domain_id == _id)\
                                            .all()  # 返回的是元组列表，如 [(1,), (2,)]
                        # 提取纯 ID 列表
                        switch_port_ids = [id[0] for id in switch_port_ids_info if id[0] is not None]
                        
                        # 批量删除 switch_port 表中的记录
                        # 删除相关的网络端口的数据，
                        # 网络端口只有在虚拟机运行的时候才会实际创建，所以虚拟机删除的时候将网络端口数据删除
                        if switch_port_ids:
                            deleted_count = session.query(SwitchPorts)\
                                                .filter(SwitchPorts.id.in_(switch_port_ids))\
                                                .delete(synchronize_session=False)
                        # 删除虚拟机和网络端口的绑定关系                      
                        session.query(DomainInterface).filter(DomainInterface.domain_id == _id).delete()
                        
                        
                        
                        # 删除对应的虚拟机数据
                        session.query(Domain).filter(Domain.id == _id).delete()
                        session.commit()

                    success_count += 1
                    new_logger.log(
                        self.username, "虚拟机", "删除虚拟机", "成功", role,
                        f"删除虚拟机: {vm_name} 成功"
                    )

                except Exception as e:
                    failed_vms.append(vm_name)
                    print(f"删除虚拟机 {vm_name} 失败: {str(e)}")
                    new_logger.log(
                        self.username, "虚拟机", "删除虚拟机", "失败", role,
                        f"删除虚拟机: {vm_name} 失败, 错误: {str(e)}"
                    )
                    continue

            if failed_vms:
                return {
                    "msg": f"部分虚拟机删除失败: {', '.join(failed_vms)}",
                    "code": 206,
                    "success_count": success_count,
                    "failed_vms": failed_vms
                }

            return {"msg": "ok", "code": 200}

        except Exception as e:
            print(f"批量删除虚拟机失败: {str(e)}")
            traceback.print_exc()
            return {"msg": f"批量删除虚拟机失败: {str(e)}", "code": 500}

    @post(_path="/v5/vm/op/open", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_open(self, form):
        """
        开机批量
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])
        
        with self.session_scope() as session:
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "starting"},
                synchronize_session=False
            )
            session.commit()
            
            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            # 异步调用主任务，传入需要分发的目标节点信息
            distach_op_vm_open.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机开机操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200,"data":"虚拟机开机操作完毕"}

    @post(_path="/v5/vm/op/close", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_close(self, form):
        """
        关机批量
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])
        
        with self.session_scope() as session:
            # 批量更新虚拟机状态为 shutdown
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutdown"},
                synchronize_session=False
            )
            session.commit()
            
            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
                distach_op_vm_close.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机关机操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机关机操作完毕"}

    @post(_path="/v5/vm/op/destroy", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_destroy(self, form):
        """
        批量强制关机
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])

        with self.session_scope() as session:
            # 批量更新虚拟机状态为 shutdown
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "shutdown"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            distach_op_vm_destroy.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机强制关机操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机强制关机操作完毕"}



    @post(_path="/v5/vm/op/pause", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_pause(self, form):
        """
        批量暂停
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "pausing"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            distach_op_vm_pause.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机暂停操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机暂停操作完毕"}

    @post(_path="/v5/vm/op/recover", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_recover(self, form):
        """
        批量恢复
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "recover"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            distach_op_vm_recover.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机恢复操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机恢复操作完毕"}

    @post(_path="/v5/vm/op/reboot", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_reboot(self, form):
        """
        批量重启
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "reboot"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            distach_op_vm_reboot.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机重启操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机重启操作完毕"}

    @post(_path="/v5/vm/op/force/restart", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_force_restart(self, form):
        """
        批量强制重启
        """
        role = self.get_cookie('role', "")

        ids = form.get("ids", [])   

        with self.session_scope() as session:
            # 批量更新虚拟机状态为 stopping
            session.query(Domain).filter(Domain.id.in_(ids)).update(
                {Domain.status: "restart"},
                synchronize_session=False
            )
            session.commit()

            vms = session.query(Domain).filter(Domain.id.in_(ids)).all()
        
        vms = [vm.to_dict_deep() for vm in vms]

        try:
            distach_op_vm_restart.apply_async(args=[vms], queue=QUEUE_NAME)
            
        except Exception as e:
            traceback.print_exc()
            return {"msg": f"虚拟机强制重启操作失败: {e}", "code": 200}

        return {"msg": "ok", "code": 200, "data": "虚拟机强制重启操作完毕"}

    @post(_path="/v5/vm/delete/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_delete_vm(self, vm_id, form):
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        client = LibvirtClient(host)
        libvirtClient = LClient(host=host)
        try:
            r = libvirtClient.del_vm(libvirtClient, vm_name)

            new_logger.log(
                self.username, "虚拟机", "删除虚拟机", "成功", role,
                "删除虚拟机: {},成功".format(vm_name)
            )
        except Exception as e:
            print(f"删除虚拟机：{vm_name} 报错：", e)
            new_logger.log(
                self.username, "虚拟机", "删除虚拟机", "失败", role,
                "删除虚拟机: {},成功".format(vm_name)
            )
            print("删除报错：", e)
            traceback.print_exc()
            return {"msg": "删除虚拟机失败", "code": 500}

        return {"msg": "删除虚拟机成功", "code": 200}

    @post(_path="/v5/vm/op/open/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_open_vm(self, vm_id, form):
        """
        开机
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.start_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "开机", "成功", role,
                "虚拟机开机: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "开机", "失败", role,
                "虚拟机开机: {},失败".format(vm_name)
            )
            print("开机", e)
            traceback.print_exc()
            return {"msg": "虚拟机开机操作失败", "code": 500}

        return {"msg": "虚拟机开机操作成功", "code": 200}

    @post(_path="/v5/vm/op/close/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_close_vm(self, vm_id, form):
        """
        关机
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.stop_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "关机", "成功", role,
                "虚拟机关机: {},成功".format(vm_name)
            )
        except Exception as e:
            new_logger.log(
                self.username, "虚拟机", "关机", "失败", role,
                "虚拟机关机: {},失败".format(vm_name)
            )
            print("关机", e)
            traceback.print_exc()
            return {"msg": "虚拟机关机操作失败", "code": 500}

        return {"msg": "虚拟机关机操作成功", "code": 200}

    @post(_path="/v5/vm/op/destroy/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_destroy_vm(self, vm_id, form):
        """
        强制关机
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.force_stop_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "强制关机", "成功", role,
                "虚拟机强制关机: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "强制关机", "失败", role,
                "虚拟机强制关机: {},失败".format(vm_name)
            )
            print("强制关机", e)
            traceback.print_exc()
            return {"msg": "虚拟机强制关机操作失败", "code": 500}

        return {"msg": "虚拟机强制关机操作成功", "code": 200}

    @post(_path="/v5/vm/op/pause/{vm_id}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_pause_vm(self, vm_id, form):
        """
        暂停
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.suspend_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "暂停", "成功", role,
                "虚拟机暂停: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "暂停", "失败", role,
                "虚拟机暂停: {},失败".format(vm_name)
            )
            print("暂停", e)
            traceback.print_exc()
            return {"msg": "虚拟机暂停操作失败", "code": 500}

        return {"msg": "虚拟机暂停操作成功", "code": 200}

    @post(_path="/v5/vm/op/recover/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_recover_vm(self, vm_id, form):
        """
        恢复
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.resume_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "恢复", "成功", role,
                "虚拟机恢复: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "恢复", "失败", role,
                "虚拟机恢复: {},失败".format(vm_name)
            )
            print("恢复", e)
            traceback.print_exc()
            return {"msg": "虚拟机恢复操作失败", "code": 500}

        return {"msg": "虚拟机恢复操作成功", "code": 200}

    @post(_path="/v5/vm/op/reboot/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_reboot_vm(self, vm_id, form):
        """
        重启
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.reboot_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "重启", "成功", role,
                "虚拟机重启: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "重启", "失败", role,
                "虚拟机重启: {},失败".format(vm_name)
            )
            print("重启", e)
            traceback.print_exc()
            return {"msg": "虚拟机重启操作失败", "code": 500}

        return {"msg": "虚拟机重启操作成功", "code": 200}

    @post(_path="/v5/vm/op/force/restart/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_op_force_restart_vm(self, vm_id, form):
        """
        强制重启
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.force_reboot_vm(libvirtClient, vm_name)
            new_logger.log(
                self.username, "虚拟机", "强制重启", "成功", role,
                "虚拟机强制重启: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "强制重启", "失败", role,
                "虚拟机强制重启: {},失败".format(vm_name)
            )
            print("强制重启", e)
            traceback.print_exc()
            return {"msg": "虚拟机强制重启操作失败", "code": 500}

        return {"msg": "虚拟机强制重启操作成功", "code": 200}

    @post(_path="/v5/vm/snapshot/create/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_create(self, vm_id, form):
        """
        创建快照
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.create_snapshot(libvirtClient, form)
            new_logger.log(
                self.username, "虚拟机", "创建快照", "成功", role,
                "虚拟机创建快照: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "创建快照", "失败", role,
                "虚拟机创建快照: {},失败".format(vm_name)
            )
            traceback.print_exc()
            return {"msg": "虚拟机创建快照操作失败", "code": 500}

        return {"msg": "虚拟机创建快照操作完毕", "code": 200}

    @post(_path="/v5/vm/snapshot/delete/{snapshot_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_delete(self, snapshot_id, form):
        """
        删除快照
        """
        print(snapshot_id, form)
        role = self.get_cookie('role', "")

        snapshot_id = snapshot_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.delete_snapshot(libvirtClient, form)
            new_logger.log(
                self.username, "虚拟机", "删除快照", "成功", role,
                "虚拟机删除快照: {},成功".format(vm_name)
            )
        except  Exception as e:
            new_logger.log(
                self.username, "虚拟机", "删除快照", "失败", role,
                "虚拟机删除快照: {},失败".format(vm_name)
            )
            traceback.print_exc()
            return {"msg": "虚拟机删除快照操作失败", "code": 500}

        return {"msg": "虚拟机删除快照操作完毕", "code": 200}

    @post(_path="/v5/vm/snapshot/restore", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_restore(self, form):
        """
        从快照还原虚拟机
        """
        print(form)
        role = self.get_cookie('role', "")

        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            r = libvirtClient.restore_snapshot(libvirtClient, form)
            new_logger.log(
                self.username, "虚拟机", "从快照还原", "成功", role,
                "虚拟机从快照还原: {},成功".format(vm_name)
            )
        except Exception as e:
            new_logger.log(
                self.username, "虚拟机", "从快照还原", "失败", role,
                "虚拟机从快照还原: {},失败".format(vm_name)
            )
            traceback.print_exc()
            return {"msg": "虚拟机从快照还原操作失败", "code": 500}

        return {"msg": "虚拟机从快照还原操作完毕", "code": 200}

    @post(_path="/v5/vm/snapshot/list/{vm_id}", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_snapshot_list(self, vm_id, form):
        """
        快照列表
        """
        print(vm_id, form)
        role = self.get_cookie('role', "")

        vm_id = vm_id
        host = form.get("host", "")
        vm_name = form.get("vm_name", [])

        try:
            libvirtClient = LibvirtClient(host=host)

            list = libvirtClient.snapshot_list(libvirtClient, form)

        except  Exception as e:

            traceback.print_exc()
            return {"msg": e, "code": 500, "data": list}

        return {"msg": "数据获取成功", "code": 200, "data": list}

    @post(_path="/v5/vm/recycle/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_recycle_list(self, form):

        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')

        with self.session_scope() as session:
            query = session.query(Domain).filter(Domain.domain_recycle_id == "1") # 回收站 0为不在回收站中，1为在回收站中

            # 搜索功能，根据指定字段进行搜索（这里假设搜索的字段为 name）
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%")).filter(Domain.domain_recycle_id == "1")

            # 排序功能
            if order_by:
                if order_type == 'asc':
                    query = query.order_by(asc(getattr(Domain, order_by)))
                else:
                    query = query.order_by(desc(getattr(Domain, order_by)))

            # 计算总记录数
            total_records = query.count()

            # 分页
            domains = query.limit(per_page).offset((page - 1) * per_page).all()

            # 将结果转换为字典列表
            domain_list = [Domain.to_dict(domain) for domain in domains]

            # 计算总页数
            total_pages = ceil(total_records / per_page)

            # 返回分页信息和数据
        return {
            "msg": "获取虚机列表成功",
            "code": 200,
            "total": total_records,
            "data": domain_list
        }

        # return {"msg": "获取虚机列表失败", "code": 500}

    @post(_path="/v5/vm/recycle/movein", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_recycle_movein(self, form):
        role = self.get_cookie('username', "")

        names = form.get("names", [])
        ids = form.get("ids", [])
        for _id, _name in zip(ids, names):

            with self.session_scope() as session:
                session.query(Domain).filter(Domain.id == _id).update({Domain.domain_recycle_id : "1"})
                session.commit()

        return {"msg": "ok", "code": 200}

    @post(_path="/v5/vm/recycle/moveout", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_recycle_moveout(self, form):
        role = self.get_cookie('username', "")
        names = form.get("names", [])
        ids = form.get("ids", [])
        for _id, _name in zip(ids, names):
            with self.session_scope() as session:
                session.query(Domain).filter(Domain.id == _id).update({Domain.domain_recycle_id : "0"})
                session.commit()

        return {"msg": "ok", "code": 200}

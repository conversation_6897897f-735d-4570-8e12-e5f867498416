# -*- coding: utf-8 -*-


from dataclasses import dataclass
from sqlalchemy.engine import strategies

@dataclass
class SecurityGroup(object):
    id : str
    name : str
    description : str
    
    
class SecurityGroupRules:
    name : str
    ram : int
    vcpus : int
    disk : int
    extdisk : int
    
@dataclass   
class SecurityGroupDetail(object):
    id : str
    name : str
    description : str
    security_group_rules : list
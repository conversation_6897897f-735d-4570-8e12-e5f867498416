sudo docker build  -t tianwen1:5000/theweb:v1 .

sudo docker run --network=host  --name test1 -it --rm tianwen1:5000/theweb:v1


#theweb 生产环境绑定127.0.0.1
sudo docker run -it -p 127.0.0.1:8080:8080  tianwen1:5000/theweb:v1

#nginx
sudo docker run -it nginx:1.15-alpine -p 8000:80 -v ./data/nginx:/etc/nginx/conf.d -v ./data/www:/usr/share/nginx/html

sudo docker run -it -p 8008:80 -v /maojj/project/thenew/data/nginx:/etc/nginx/conf.d -v /maojj/project/thenew/data/www:/usr/share/nginx/html nginx


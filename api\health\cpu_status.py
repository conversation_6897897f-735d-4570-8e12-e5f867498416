from api.prometheus.client import Client
import time

name = "CPU硬件状态"
weigth = 20

def check():
    
    client = Client()
    res = client.query_vector_by_query("node_load5{job='openstack_hypervisors'}")
    
    
    for info in res:
        print(info)
    result_status = "ok"
    
    columns = [
            {
                "title":"主机名",
                "key":"hostname",
                "tooltip":True
            },
            {
                "title":"主机IP",
                "key":"instance",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"5分钟平均负载",
                "key":"value",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"时间",
                "key":"date",
                "tooltip":True,
                "align":"center"
            }
        ]
    data = []
    
    
    for info in res:
        print(info)
        tmp = {}
        tmp["hostname"] = info["metric"]["hypervisor_hostname"]
        tmp["instance"] = info["metric"]["instance"]
        tmp["value"] = info["value"][1]
        if (float(tmp["value"]) > 5.0):
            result_status = "warning"
        tmp["date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info["value"][0]))
        data.append(tmp)
    
    result = {
        "key": "cpu_status",
        "name": "CPU硬件状态",
        "weigth": 20,
        "columns": columns,
        "result":result_status,    # ok (V), warning (!) ,error (X)
        "data": data
    }
    return result
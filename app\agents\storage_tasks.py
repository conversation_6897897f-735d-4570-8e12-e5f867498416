import time
from nameko.standalone.rpc import ClusterRpcProxy

from app.celery import app
from celery import Task
from api.libvirt.client import Client
from utils.db import get_dbi
from config.settings import MSG_AMQP_URI
from api.iscsi.iscsi_client import ISCSIManager
from utils.tools import get_system_info

import logging


class StorageCreateTask(Task):
    """
    自定义存储卷钩子类
    """

    def on_success(self, retval, task_id, args, kwargs):
        storage_name = args[0]
        form = args[1]

        print(f"Task {task_id} succeeded with result: {retval}")
        print(f"存储名称: {storage_name}")
        print(f"Form: {form}")

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"Task {task_id} failed with error: {exc}")
        # 从 args 获取 vm_name 和 form 参数
        storage_name = args[0]
        form = args[1]

        print(f"Task {task_id} failed with error: {exc}")
        print(f"存储名称: {storage_name}")
        print(f"Form: {form}")


class StorageCreateCallbackTask(Task):
    """
    自定义回调构造函数
    """

    def on_success(self, retval, task_id, args, kwargs):
        print("回调成功函数")
        print(args)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print("回调失败函数")
        print(args)


@app.task(name="allocate_storage")
def allocate_storage(name, form):
    print("创建存储卷")

    storage_type_code = form.get("storage_type_code", "local")
    if storage_type_code == "IP-SAN":
        # 如果是IP-SAN存储卷，直接返回表单数据
        print("IP-SAN存储卷，直接返回表单数据")
        form["size"] = form.get("capacity", 0)
        form["name"] = form["name"] + "." + form["volume_format"]
        res = ISCSIManager.create_disk_volume(form)
        if res:
            print("创建IP-SAN存储卷成功")
            data = {
                "id": form["id"],
                "status": 4,
                "allocation": form["capacity"],
            }
            return data
        else:
            print("创建IP-SAN存储卷失败")
            
        return form
    elif storage_type_code == "local":
        libvirtClient = Client()
        r = libvirtClient.create_storage_pool_volume(libvirtClient, form)

        if r:
            # 获取创建的卷的详情
            volume = libvirtClient.get_storage_pool_volume_info(libvirtClient, form["storage_pool_name"], form['name']+ "." + form['volume_format'])
            print("将存储卷信息转换成数据库信息", volume)
            data = {
                "id": form["id"],
                # "name": form["name"],
                # "storage_pool_id": form["storage_pool_id"],
                # "type": form["volume_format"],
                # "join_type": form["join_type"],
                # "path": form["path"],
                # "encrypt": form["encrypt"],
                "status": volume["type"],
                # "capacity": form["capacity"],
                "allocation": volume["allocation"],
                # "preallocation": form["preallocation"],
                # "remark": form["remark"]
            }
            return data
        else:
            print("存储卷：", form["name"], " 创建失败")
        # return {"status": "success", "data": form}
    return form



@app.task(name="update_storage")
def update_storage(storage_name, form):
    print("修改卷函数")
    name = form["name"]
    new_name = form["new_name"]
    pool_name = form["pool_name"]
    storage_type_code = form.get("storage_type_code", "local")
    path = form["path"]
    volume_type = form["volume_type"]
    if storage_type_code == "IP-SAN":
        print("IP-SAN存储卷修改，暂时不做任何操作")
        res = ISCSIManager.rename_disk_volume(path, name + "." + volume_type, new_name + "." + volume_type)
        if res:
            print(f"存储卷 {name} 重命名为 {new_name} 成功")
        else:
            print(f"存储卷 {name} 重命名为 {new_name} 失败")
        return [form]
    elif storage_type_code == "local":
        # 如果是本地存储卷，直接调用libvirtClient的重命名方法
        print("本地存储卷，直接调用libvirtClient的重命名方法")
        libvirtClient = Client()
        r = libvirtClient.rename_storage_pool_volume(libvirtClient, pool_name, name, new_name)
        if r:
            print(f"存储卷 {name} 重命名为 {new_name} 成功")
        else:
            print(f"存储卷 {name} 重命名为 {new_name} 失败")
        # 返回修改后的表单数据
    return [form]


@app.task(name="delete_storage")
def delete_storage(storage, form):
    # 删除存储的逻辑
    volume_name = form["volume_name"]
    pool_name = form["pool_name"]
    path = form["path"]
    print(form)
    print(f"删除存储卷: {storage}")
    storage_type_code = form.get("storage_type_code", "local")
    if storage_type_code == "IP-SAN":
        # 如果是IP-SAN存储卷，直接返回表单数据
        print("IP-SAN存储卷删除，直接返回表单数据")
        res = ISCSIManager.delete_disk_volume(path, volume_name)
        if res:
            print("删除IP-SAN存储卷成功")
        else:
            print("删除IP-SAN存储卷失败")
        return form
    elif storage_type_code == "local":
        libvirtClient = Client()
        try:
            r = libvirtClient.delete_storage_pool_volume(libvirtClient, pool_name, volume_name)
            if r is None:
                print(f"找不到存储池 {pool_name}")
            return form
        except Exception as e:
            print("删除存储卷失败", e)
            return form



@app.task(name="discover_iscsi")
def discover_iscsi(form):
    """
    发现指定iscsi
    """
    
    iscsi_host = form.get("iscsi_host")
    iscsi_port = form.get("iscsi_port", 3260)
    iscsi = ISCSIManager(iscsi_host, iscsi_port)
    targets = iscsi.discover_target_list()

    form["targets"] = targets
    return form


@app.task(name="iscsi_get_iqn")
def iscsi_get_iqn(form):
    iscsi_host = form.get("iscsi_host")
    iscsi_port = form.get("iscsi_port", 3260)
    target_name = form.get("target_name")
    iscsi = ISCSIManager(iscsi_host, iscsi_port)
    # 判断是否登录iscsi
    is_login = iscsi.is_target_logged_in(target_name)
    if is_login:
        login_ret = True
    else:
        # 登录
        login_ret = iscsi.login(target_name)

    # form["login_ret"] = login_ret

    if login_ret:
        # 获取lun设备信息
        time.sleep(1)
        target_lun = iscsi.get_device_info_by_iqn(target_name)
        form["target_lun"] = target_lun
    return form

@app.task(name="iscsi_logout")
def iscsi_logout(form):
    iscsi_host = form.get("iscsi_host")
    iscsi_port = form.get("iscsi_port", 3260)
    target_name = form.get("target_name")
    iscsi = ISCSIManager(iscsi_host, iscsi_port)
    # 登出
    is_logout = iscsi.logout(target_name)
    if is_logout:
        print(f"{iscsi_host.name} 登出 iSCSI 设备成功")
        form["is_logout"] = True
    else:
        # 如果需要保存成功登录的信息，可以在此记录
        print(f"{iscsi_host.name} 登出 iSCSI 设备失败")
        form["is_logout"] = True
        
    return form

@app.task(name="iscsi_create_pool")
def iscsi_create_pool(form):
    print("iscsi存储池创建参数：", form)
    iscsi_host = form.get("iscsi_host")
    iscsi_port = form.get("iscsi_port")
    target_name = form.get("target_name")
    device_path = form.get("device_path")
    storage_type = form.get("storage_type")
    filesystem = form.get("filesystem", "ocfs2")
    pool_name = form.get("pool_name")
    mount_point = "/hci_data/iscsi/" + device_path
    
    
    # 创建iscsi 连接
    iscsi = ISCSIManager(iscsi_host, iscsi_port)
    
    # 判断是否登录指定的target
    is_login = iscsi.is_target_logged_in(target_name)
    if not is_login:
        # 登录
        login_ret = iscsi.login(target_name)
    else:
        login_ret = True
        
    # 获取指定lun设备信息 device_node 示例：/dev/sda
    device_node = ""
    device_size = ""
    devices = iscsi.get_device_info_by_iqn(target_name)
    for device in devices:
        if device["device_path"] == device_path:
            device_node = device["device_node"]
            device_size = device["size_bytes"]
            print("lun设备: ", device_node)
        
    
    # 判断改设备是否被ocfs2格式化
    is_format = iscsi.is_device_formatted_as_ocfs2(device_node)
    if not is_format:
        print("{device_node} 设备没有格式化, 执行格式化操作")
        if storage_type == "share":
            print("存储类型为 共享存储， 执行ocfs2格式化")
            filesystem = "ocfs2"
            format_result = iscsi.format_disk_with_ocfs2(device_node)            
            if not format_result:
                print("ocfs2格式化磁盘失败")
            
    #挂载点处理，如果挂载点不存在就创建
    mount_point_handle = iscsi.create_catalog(mount_point)
    if not mount_point_handle:
        print("挂载点创建失败")
    
    # 判断该设备是否被挂载
    is_mount = iscsi.is_device_mounted(device_node)
    if not is_mount:
        print("设备{device_node}未被挂载")
        mount_form = {
            "device_node": device_node,
            "filesystem": filesystem,
            "mount_point": mount_point,
            
        }
        print("挂载设备{device_node}")
        mount_result = iscsi.mount_lun_device(mount_form)
        if not mount_result:
            print("挂载设备失败")
            form["storage_result"] = False
            return form
            
    # 在挂载点下面创建存储池目录
    storage_pool_catalog = mount_point + "/" + pool_name
    print("storage_pool_catalog", storage_pool_catalog)
    is_pool = iscsi.create_catalog(storage_pool_catalog)
    if not is_pool:
        print("存储池目录：{storage_pool_catalog}创建失败")
        form["storage_result"] = False
    else:
        form["storage_result"] = True
        
        
    form["storage_local_dir"] = storage_pool_catalog
    form["device_size"] = device_size
    return form
    
            
        
@app.task(name="iscsi_del_pool")
def iscsi_del_pool(form):
    print("删除iscsi存储池")
    print("参数：", form)
    storage_local_dir = form.get("storage_local_dir")
    pool_name = form.get("pool_name")
    
    # 直接调用静态方法
    res = ISCSIManager.remove_catalog(storage_local_dir)
    
    if not res:
        print(f"删除存储池 {pool_name} 失败")
        form["del_pool_result"] = False
    else:
        print(f"删除存储池 {pool_name} 成功")
        form["del_pool_result"] = True
    
    return form
    
    
    
@app.task(name="create_local_pool")
def create_local_pool(form):
    print("创建本地存储池")
    print("参数：", form)
    device_id = form.get("device_id")
    host_ip = form.get("host_ip")
    pool_name = form.get("pool_name")
    storage_local_dir = form.get("storage_local_dir")
    type_info = form.get("type_info", {})
    
    libvirtClient = Client()
    r = libvirtClient.create_storage_pool(libvirtClient, pool_name, 'dir', storage_local_dir)
    pool = libvirtClient.get_storage_pool_info(libvirtClient, pool_name)
    pool["type_code"] = type_info["code"]
    pool["type_code_display"] = type_info["name"]
    pool["storage_device_id"] = device_id
    form["pool"] = pool
    system_info = get_system_info()
    form["system_info"] = system_info
    
    print("创建本地存储池成功，返回的参数信息：", form)
    return form


@app.task(name="put_local_pool")
def put_local_pool(form):
    print("修改本地存储池")
    print("参数：", form)
    
    host_ip = form.get("host_ip")
    name = form.get("name")
    new_name = form.get("new_name")
    
    libvirtClient = Client()
    try:
        r = libvirtClient.rename_storage_pool(libvirtClient, name, new_name)
        if not r:
            print(f"重命名存储池从 {name} 到 {new_name} 失败")
            return {"code": 500, "msg": "重命名存储池失败"}
        print(f"重命名存储池从 {name} 到 {new_name} 成功")
        return form
    except Exception as e:
        print(f"重命名存储池异常: {str(e)}")
        return {"code": 500, "msg": f"重命名存储池异常: {str(e)}"}
    
    
@app.task(name="delete_local_pool")
def delete_local_pool(form):
    print("删除本地存储池")
    print("参数：", form)
    host_ip = form.get("host_ip")
    pool_name = form.get("pool_name")
    libvirtClient = Client()
    r = libvirtClient.delete_storage_pool(libvirtClient, pool_name)
    
    if r:
        print(f"删除本地存储池 {pool_name} 成功")
    else:
        print(f"删除本地存储池 {pool_name} 失败")
    
    return form

@app.task(name="iscsi_create_pool_volume")
def iscsi_create_pool_volume(form):
    """在创建iscsi共享存储池中创建存储卷"""
    print("创建iscsi存储池卷")
    print("参数：", form)
    
    res = ISCSIManager.create_disk_volume(form)
    if res:
        print("创建iscsi存储池卷成功")
        return form
    else:
        print("创建iscsi存储池卷失败")
        return form
    
@app.task(name="iscsi_delete_pool_volume")
def iscsi_delete_pool_volume(form):
    """删除iscsi共享存储池中的存储卷"""
    print("删除iscsi存储池卷")
    print("参数：", form)
    
    res = ISCSIManager.delete_disk_volume(form)
    if res:
        print("删除iscsi存储池卷成功")
        return form
    else:
        print("删除iscsi存储池卷失败")
        return form 
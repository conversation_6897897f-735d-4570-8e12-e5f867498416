# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import Custom<PERSON>ogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.model.instances import *
from db.model.user import User
from db.model.vm import Volumes
from util.cov import todict, serialize
from api.openstack.client import Client
from sqlalchemy_pagination import paginate
from sqlalchemy import and_
from db.model.vm import VmGroup , Vms
from model.util import Role

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()

class GrantHandler(pyrestful.rest.RestHandler):
    
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    def secadm_query_vms(self, group):
        vms = []
        group_id = group.get("id")
        with self.session_scope() as session:
            group = session.query(VmGroup).filter(VmGroup.id == group_id).first()
        
        client = Client()
        vmsdata = client.NovaClient.openstack_get_all_instance_list(client)
        for ins in vmsdata:
            vms.append(ins.get("id"))

        return vms
    
    @post(_path="/v1/grant/instances",  _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_find_vm_garnt(self, group):
        """
        ---
        tags:
          - 虚拟机分配相关接口
        summary: 获取所有虚拟机
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupPageForm'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayInstanceDetailModel'
        """
        role = self.get_cookie("role")
        page = group.get("page")
        group_id = group.get("id")
        pagecount = group.get("pagecount")
        order_by = group.get("order_by", "")
        order_type = group.get("order_type", "desc")
        search_str = group.get("search_str", "")
        
        data = []
        vms = []
        client = Client()
        grantvms = {}
        
        if role == Role.secadm:
            vms = self.secadm_query_vms(group)
            
            with self.session_scope() as session:
                sql_vms = session.query(Vms).all()
            
                for vm in sql_vms:
                    if vm.user_id:
                        with self.session_scope() as session:
                            user = session.query(User).filter(User.id==vm.user_id).first()
                            grantvms[vm.vmid] = user.username
            
             
        ins_details = client.NovaClient.openstack_get_all_instance_detail(client)
        
        for ins_detail in ins_details:
            vmid = ins_detail.get("id")
            ins_detail["grant_username"] = grantvms.get(vmid, "")
                
            if search_str:
                if search_str in ins_detail["name"]:
                    data.append(ins_detail)
            else:
                data.append(ins_detail)


        count = len(data)
        pages = count // pagecount + 1
        if order_type == "desc" and order_by:
            data = sorted(data, key = lambda x:x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by], reverse=False)


        start = (page - 1) * pagecount
        #end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
        end =  start + pagecount
        
        data = data[start:end]
        
        r = {
            "total": count,
            "pages": pages,
            "data": data
            }
 
        return r    



    @post(_path="/v1/grant/instances/to/user",  _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_vm_garnt_to_user(self, group):
        role = self.get_cookie("role")
        vmids = group.get("vmids")
        vmnames = group.get("vmnames")
        userid = group.get("userid", None)
        username = group.get("username", None)
        client = Client()
        try:
            
            with self.session_scope() as session:
                for vmid in vmids:    
                    vm = session.query(Vms).filter(Vms.vmid==vmid).first()
                 
                    if not vm:
                        vm = Vms()
                        vm.vmid = vmid
                        vm.user_id = userid
                        vm_volume = client.NovaClient.openstack_getpost_server_detail(client, vmid)
                        
                        for volume_id  in vm_volume["volume_id"]:
                            v = session.query(Volumes).filter(Volumes.vm_id==volume_id).first()
                            if v:
                                session.query(Volumes).filter(Volumes.vm_id==volume_id).update({"user_id":userid, "vm_id": vmid})
                            else:
                                my_volume = Volumes()
                                my_volume.vm_id = vmid
                                my_volume.user_id = userid
                                my_volume.volume_id = volume_id
                                session.add(my_volume)
                        
                        session.add(vm)
                    else:
                        #TODO 是否删除全部分组关系。操作员和管理员的分组都被删除
                        vm.vmgroup.clear()
                        session.query(Vms).filter(Vms.vmid==vmid).update({"user_id":userid})
                        session.query(Volumes).filter(Volumes.vm_id==vmid).update({"user_id":userid})
                
                
                
                
                session.commit()
        
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "分配虚机",
                             "object": "%s %s" % (userid, vmids),
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "虚机管理", "分配虚拟机", "失败", role, "{}: 用户:{},虚拟机:{},失败".format("分配虚拟机",username, vmnames)
                )
                return {"msg":"error"}

        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": self.get_cookie('username', ""),
                     "op": "分配虚机",
                     "object": "%s %s" % (userid, vmids),
                     "role": self.get_cookie('role', ""),
                     "result": "成功",
                     })
        new_logger.log(
            self.username, "虚机管理", "分配虚拟机", "成功", role, "{}: 用户:{},虚拟机:{},成功".format("分配虚拟机", username, vmnames)
        )
        
        return {"msg":"ok"}  
            
    @post(_path="/v1/grant/instances/delete/user",  _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_vm_garnt_delete_user(self, group):
        role = self.get_cookie("role")
        vmids = group.get("vmids")
        vmnames = group.get("vmnames")
        userid = group.get("userid", None)
        client = Client()
        try:
            
            with self.session_scope() as session:
                for vmid in vmids:
                    vm = session.query(Vms).filter(Vms.vmid==vmid).first()
                 
                    if vm:
                        vm = Vms()
                        vm.vmid = vmid
                        vm.user_id = userid
                        vm_volume = client.NovaClient.openstack_getpost_server_detail(client, vmid)
                        
                        for volume_id  in vm_volume["volume_id"]:
                            session.query(Volumes).filter(Volumes.vm_id==volume_id).delete()
                        
                        session.query(Vms).filter(Vms.vmid==vmid).delete()
                
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "分配虚机",
                             "object": "%s %s" % (userid, vmids),
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "虚机管理", "取消分配虚拟机", "失败", role, "{}: 虚拟机:{},失败".format("取消分配虚拟机", vmnames)
                )
                return {"msg":"error"}

        logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": self.get_cookie('username', ""),
                     "op": "分配虚机",
                     "object": "%s %s" % (userid, vmids),
                     "role": self.get_cookie('role', ""),
                     "result": "成功",
                     })
        new_logger.log(
            self.username, "虚机管理", "取消分配虚拟机", "成功", role, "{}: 虚拟机:{},成功".format("取消分配虚拟机", vmnames)
        )
        
        return {"msg":"ok"}          



from api.prometheus.client import Client
import time

name = "内存硬件状态"
weigth = 20

def check():
    
    client = Client()
    res = client.query_vector_by_query("(1 - (node_memory_MemAvailable_bytes{job='openstack_hypervisors'} / (node_memory_MemTotal_bytes{job='openstack_hypervisors'})))* 100")
    
    
    for info in res:
        print(info)
    result_status = "ok"
    
    columns = [
            {
                "title":"主机名",
                "key":"hostname",
                "tooltip":True
            },
            {
                "title":"主机IP",
                "key":"instance",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"内存使用率",
                "key":"value",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"时间",
                "key":"date",
                "tooltip":True,
                "align":"center"
            }
        ]
    data = []
    
    
    for info in res:
        print(info)
        tmp = {}
        tmp["hostname"] = info["metric"]["hypervisor_hostname"]
        tmp["instance"] = info["metric"]["instance"]
        tmp["value"] = "%0.2f" % float(info["value"][1])
        if (float(tmp["value"]) > 70):
            result_status = "warning"
        tmp["date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info["value"][0]))
        data.append(tmp)
    
    result = {
        "key": "ram_status",
        "name": "内存硬件状态",
        "weigth": 20,
        "columns": columns,
        "result":result_status,    # ok (V), warning (!) ,error (X)
        "data": data
    }
    return result
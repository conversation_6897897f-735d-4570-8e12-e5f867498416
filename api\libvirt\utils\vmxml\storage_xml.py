from .default import XMLElement


class StoragePool(XMLElement):
    """
       通用元素据类型
       name: 名称
       """

    def __init__(self, tag="pool", attributes=None, text=None):
        super().__init__(tag, attributes, text)

    def set_name(self, name):
        self.set_tag("name", None, name)

    def set_target_element(self, path):
        target = self.set_tag("target")
        target.set_tag("path", None, path)

    def default_local_pool_xml(self, form):
        # 检查 form 是否是字典
        if not isinstance(form, dict):
            raise ValueError("form 参数必须是字典类型")

        # 检查必需的键是否存在
        required_keys = ['name', 'target_path']
        for key in required_keys:
            if key not in form:
                raise KeyError(f"storage_xml => get_default_local_pool() => form 参数缺少必需的键: {key}")

        # 如果所有必需的键都存在，继续处理
        name = form['name']
        target_path = form['target_path']
        self.set_name(name)
        self.set_target_element(target_path)
        return self.format_xml()


def default_local_pool_xml(form: dict) -> str:
    """
    创建默认的存储池xml文件
    Args:
        form:(dict): 一个包含存储池配置的字典，必须包含以下键：
            - name (str): 存储池的名称。
            - target_path (str): 存储池的目标路径，即存储磁盘映像的目录。
    Returns:
        str: 表示存储池定义的 XML 字符串。
    """
    # 检查必需的键是否存在
    required_keys = ['name', 'target_path']
    for key in required_keys:
        if key not in form:
            raise KeyError(f"storage_xml => get_default_local_pool() => form 参数缺少必需的键: {key}")

    # 如果所有必需的键都存在，继续处理
    pool = StoragePool()
    name = form['name']
    target_path = form['target_path']
    pool.set_name(name)
    pool.set_target_element(target_path)
    return pool.to_xml()


class StorageVolume(XMLElement):
    """
       通用元素据类型
       name: 名称
       """

    def __init__(self, tag="volume", attributes=None, text=None):
        super().__init__(tag, attributes, text)

    def set_name(self, name):
        self.set_tag("name", None, name)

    def set_allocation(self, allocation='0', atr=None):
        if atr is None:
            atr = {"uint": "bytes"}
        return self.set_tag("allocation", atr, allocation)

    def set_capacity(self, capacity, atr=None):
        if atr is None:
            atr = {"uint": "bytes"}
        return self.set_tag("capacity", atr, capacity)

    def set_target(self, name="target"):
        return self.set_tag(name)

    def set_format(self, vol_format="qcow2"):
        self.set_attribute("type", vol_format)

    def set_path(self, path):
        self.set_tag("path", None, path)

    def set_unit(self, unit="bytes"):
        self.set_attribute("unit", unit)

    def set_size(self, size):
        self.set_text(str(size))

    def default_local_volume_xml(self, form: dict) -> str:
        """
           创建默认的存储卷xml文件
           Args:
               form:(dict): 一个包含存储池配置的字典，必须包含以下键：
                   - name (str): 存储池的名称。
                   - target_path (str): 存储池的目标路径，即存储磁盘映像的目录。
           Returns:
               str: 表示存储池定义的 XML 字符串。
           """


class Target(XMLElement):
    def __init__(self, tag="target", attributes=None, text=None):
        super().__init__(tag, attributes, text)

    def set_format(self, vol_format):
        if vol_format == "":
            vol_format = "qcow2"
        self.set_tag("format", {"type": vol_format})

    def set_path(self, path):
        self.set_tag("path", None, path)


class BackingStore(XMLElement):
    def __init__(self, tag="backingStore", attributes=None, text=None):
        super().__init__(tag, attributes, text)

    def set_format(self, vol_format):
        if vol_format == "":
            vol_format = "qcow2"
        self.set_tag("format", {"type": vol_format})

    def set_path(self, path):
        self.set_tag("path", None, path)

'''
Created on Mar 28, 2023

@author: ct
'''
import requests
import json
import settings
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
import copy
import time
import urllib
from string import Template

class Client(object):
    
    thesc_url = settings.THESC_URI
    token = ""
    data = {}

    def get_migrate_recommend_list(self):
        method = "/v1/migrate/task/recommend/list"
        url = "%s%s" % (self.thesc_url, method)
        try:
            r = requests.get(url) 
        except Exception as e:
            print(e)
        data = json.loads(r.text)

        return data
    
    def post_migrate_task_run(self, form):
        method = "/v1/migrate/task/run"
        url = "%s%s" % (self.thesc_url, method)

        dd = json.dumps(form)
        
        r = requests.post(url, data=dd)
        
        if r.status_code == 200:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}       
    
    def post_instance_dayu_setconfig(self, form):
        method = "/v1/instances/dayu/setconfig"
        url = "%s%s" % (self.thesc_url, method)
        
        temp = """{
                "vmid": "$vmid",
                "enable": "$enable"
            }"""
        
        t = Template(temp)

        body = t.substitute(form)
        dd = json.dumps(json.loads(body))
        
        r = requests.post(url, data=dd)
        
        if r.status_code == 202:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
    
"""
for net_info in res:
    print(net_info.get("metric"))

"""
"""
libvirt虚拟机快照使用示例
演示如何使用快照管理服务和数据表
"""

from django.contrib.auth.models import User
from .models import VirtualMachine, Snapshot
from .snapshot_service import SnapshotService
import uuid


def example_usage():
    """快照管理使用示例"""
    
    # 1. 创建虚拟机记录（通常从libvirt同步）
    vm = VirtualMachine.objects.create(
        name='test-vm',
        uuid=uuid.uuid4(),
        hypervisor_uri='qemu:///system',
        status='running'
    )
    
    # 2. 获取用户
    user = User.objects.get(username='admin')
    
    # 3. 初始化快照服务
    snapshot_service = SnapshotService()
    
    # 4. 创建内部完整系统快照（默认）
    print("创建内部完整系统快照...")
    internal_snapshot = snapshot_service.create_snapshot(
        vm_id=vm.id,
        snapshot_name='internal-full-snapshot-1',
        description='内部完整系统快照，数据存储在原始磁盘文件中',
        snapshot_type='full_system',
        storage_type='internal',  # 内部快照，不需要额外配置
        user=user
    )
    print(f"内部快照创建成功: {internal_snapshot.name}")

    # 5. 创建外部磁盘快照
    print("创建外部磁盘快照...")
    disk_configs = [
        {
            'name': 'vda',
            'mode': 'external',
            'type': 'file',
            'source_file': '/var/lib/libvirt/images/test-vm-snapshot-1.qcow2',
            'driver_type': 'qcow2'
        },
        {
            'name': 'vdb',
            'mode': 'no'  # 不对此磁盘创建快照
        }
    ]

    external_disk_snapshot = snapshot_service.create_snapshot(
        vm_id=vm.id,
        snapshot_name='external-disk-snapshot-1',
        description='外部磁盘快照，快照文件单独存储',
        snapshot_type='disk_only',
        storage_type='external',
        disk_configs=disk_configs,
        user=user
    )
    print(f"外部磁盘快照创建成功: {external_disk_snapshot.name}")

    # 6. 创建外部完整系统快照
    print("创建外部完整系统快照...")
    memory_config = {
        'mode': 'external',
        'file': '/var/lib/libvirt/images/test-vm-memory-1.save',
        'size': 1024 * 1024 * 1024  # 1GB
    }

    external_full_snapshot = snapshot_service.create_snapshot(
        vm_id=vm.id,
        snapshot_name='external-full-snapshot-1',
        description='外部完整系统快照，磁盘和内存都单独存储',
        snapshot_type='full_system',
        storage_type='external',
        disk_configs=disk_configs,
        memory_config=memory_config,
        user=user
    )
    print(f"外部完整快照创建成功: {external_full_snapshot.name}")
    
    # 7. 查询快照树结构
    print("\n=== 查询快照树结构 ===")
    snapshots = Snapshot.objects.filter(virtual_machine=vm).select_related('parent')
    for snapshot in snapshots:
        parent_name = snapshot.parent.name if snapshot.parent else "无"
        current_mark = " (当前)" if snapshot.is_current else ""
        storage_type_display = snapshot.get_storage_type_display()
        print(f"  - {snapshot.name} (父快照: {parent_name}) [{storage_type_display}]{current_mark}")

    # 8. 查询不同类型快照的详细信息
    print("\n=== 查询快照详细信息 ===")
    all_snapshots = Snapshot.objects.filter(virtual_machine=vm).prefetch_related('disks', 'memory', 'operations')

    for snapshot in all_snapshots:
        print(f"\n快照: {snapshot.name}")
        print(f"  类型: {snapshot.get_snapshot_type_display()}")
        print(f"  存储: {snapshot.get_storage_type_display()}")
        print(f"  状态: {snapshot.get_state_display()}")
        print(f"  创建时间: {snapshot.creation_time}")

        # 根据存储类型显示不同信息
        if snapshot.storage_type == 'internal':
            print("  存储信息: 快照数据存储在原始磁盘文件中，无需额外文件")
        else:
            # 外部快照显示详细的磁盘和内存信息
            if snapshot.disks.exists():
                print("  磁盘快照信息:")
                for disk in snapshot.disks.all():
                    print(f"    - {disk.disk_name}: {disk.get_snapshot_mode_display()}")
                    if disk.snapshot_file:
                        print(f"      快照文件: {disk.snapshot_file}")

            if hasattr(snapshot, 'memory') and snapshot.memory:
                memory = snapshot.memory
                print(f"  内存快照: {memory.get_snapshot_mode_display()}")
                if memory.memory_file:
                    print(f"    内存文件: {memory.memory_file}")

        # 操作历史
        recent_operations = snapshot.operations.all()[:3]  # 最近3次操作
        if recent_operations:
            print("  最近操作:")
            for operation in recent_operations:
                status_display = operation.get_status_display()
                op_type_display = operation.get_operation_type_display()
                print(f"    - {operation.started_at.strftime('%Y-%m-%d %H:%M')}: {op_type_display} - {status_display}")
    
    # 9. 恢复到指定快照
    print("\n=== 快照恢复测试 ===")
    print("恢复到内部快照...")
    try:
        snapshot_service.revert_snapshot(internal_snapshot.id, user=user)
        print("内部快照恢复成功")
    except Exception as e:
        print(f"快照恢复失败: {e}")

    # 10. 删除快照
    print("\n=== 快照删除测试 ===")
    print("删除外部磁盘快照...")
    try:
        snapshot_service.delete_snapshot(external_disk_snapshot.id, user=user)
        print("外部磁盘快照删除成功")
    except Exception as e:
        print(f"快照删除失败: {e}")


def query_snapshot_statistics():
    """查询快照统计信息"""
    from django.db.models import Count, Sum, Avg
    from django.utils import timezone
    from datetime import timedelta
    
    print("=== 快照统计信息 ===")
    
    # 1. 按虚拟机统计快照数量
    vm_snapshot_counts = VirtualMachine.objects.annotate(
        snapshot_count=Count('snapshots')
    ).values('name', 'snapshot_count')
    
    print("各虚拟机快照数量:")
    for vm_stat in vm_snapshot_counts:
        print(f"  {vm_stat['name']}: {vm_stat['snapshot_count']} 个快照")
    
    # 2. 按快照类型统计
    type_stats = Snapshot.objects.values('snapshot_type').annotate(
        count=Count('id'),
        total_size=Sum('size_bytes')
    )
    
    print("按类型统计:")
    for stat in type_stats:
        size_gb = stat['total_size'] / (1024**3) if stat['total_size'] else 0
        print(f"  {stat['snapshot_type']}: {stat['count']} 个, 总大小: {size_gb:.2f} GB")
    
    # 3. 最近7天创建的快照
    week_ago = timezone.now() - timedelta(days=7)
    recent_snapshots = Snapshot.objects.filter(
        creation_time__gte=week_ago
    ).count()
    
    print(f"最近7天创建的快照: {recent_snapshots} 个")
    
    # 4. 快照操作成功率
    from django.db.models import Case, When, IntegerField
    
    operation_stats = SnapshotOperation.objects.aggregate(
        total=Count('id'),
        success=Count(Case(
            When(status='success', then=1),
            output_field=IntegerField()
        )),
        failed=Count(Case(
            When(status='failed', then=1),
            output_field=IntegerField()
        ))
    )
    
    if operation_stats['total'] > 0:
        success_rate = (operation_stats['success'] / operation_stats['total']) * 100
        print(f"快照操作成功率: {success_rate:.1f}% ({operation_stats['success']}/{operation_stats['total']})")
    
    # 5. 平均快照大小
    avg_size = Snapshot.objects.filter(
        size_bytes__isnull=False
    ).aggregate(avg_size=Avg('size_bytes'))
    
    if avg_size['avg_size']:
        avg_size_gb = avg_size['avg_size'] / (1024**3)
        print(f"平均快照大小: {avg_size_gb:.2f} GB")


def cleanup_old_snapshots(days=30):
    """清理旧快照"""
    from django.utils import timezone
    from datetime import timedelta
    
    cutoff_date = timezone.now() - timedelta(days=days)
    
    # 查找需要清理的快照（排除当前快照）
    old_snapshots = Snapshot.objects.filter(
        creation_time__lt=cutoff_date,
        is_current=False,
        deletion_in_progress=False
    )
    
    print(f"发现 {old_snapshots.count()} 个超过 {days} 天的旧快照")
    
    snapshot_service = SnapshotService()
    
    for snapshot in old_snapshots:
        try:
            print(f"删除快照: {snapshot.name} (创建于 {snapshot.creation_time})")
            snapshot_service.delete_snapshot(snapshot.id)
            print("删除成功")
        except Exception as e:
            print(f"删除失败: {e}")


if __name__ == "__main__":
    # 运行示例
    example_usage()
    
    # 查询统计信息
    query_snapshot_statistics()
    
    # 清理旧快照（可选）
    # cleanup_old_snapshots(days=30)

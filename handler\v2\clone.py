import random
import string

import pyrestful.rest
from db.model.task import InstanceCreateTask

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from api.openstack.client import Client
from api.model.volumes import Volume<PERSON><PERSON>Form
from db.model.user import User


class <PERSON><PERSON>Handler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v2/desk/clone", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def the_desk_clone_v2(self, form):
        try:
            # 获取虚拟机信息
            client = Client()
            print("参数：", form["vm_id"])
            res = client.NovaClient.openstack_getpost_server_detail(client, form['vm_id'])
            print("res信息：", res)
            if res["flavor"]:
                print("falvor_id：", res["flavor"]["id"])
                flavor_res = client.FlavorClient.openstack_get_flavor_detail(client, res["flavor"]["id"])
                res["vcpus"] = flavor_res["vcpus"]
                res["ram"] = int(flavor_res["ram"] / 1024)
                res["disk"] = flavor_res["disk"]
            if len(res["volume_id"]) > 0:
                res["attachment"] = []
                volume_detail = client.VolumeClient.openstack_get_volume_detail(client, res["volume_id"][0])

                res["imagename"] = volume_detail["volume_image_metadata"].get("image_name", "")
                if volume_detail["volume_image_metadata"]:
                    image_id = volume_detail["volume_image_metadata"]["image_id"]
                elif isinstance(res["image"], dict):
                    image_id = res["image"]["id"]
                else:
                    image_id = ""

                for volume in res["volume_id"]:
                    print("卷信息：", volume)
                    volume_detail = client.VolumeClient.openstack_get_volume_detail(client, volume)
                    print("卷详情：", volume_detail)

                    new_name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
                    # 克隆新卷
                    new_volume = VolumeCloneForm()
                    new_volume.name = "克隆卷_" + new_name
                    new_volume.size = volume_detail["size"]
                    new_volume.source_volid = volume

                    new_volume_res = client.VolumeClient.openstack_create_volume_by_type_to_clone(client, new_volume)
                    print("创建新卷的返回信息：", new_volume_res)

                    with self.application.session_scope() as session:
                        username = self.get_cookie("username", "")
                        user = session.query(User).filter(User.username == username).first()

                        task = InstanceCreateTask()
                        task.name = "克隆_" + new_name
                        task.flavorRef = res["flavor"]["id"]
                        task.volume_id = new_volume_res["id"]
                        task.imageRef = image_id
                        task.iso = "克隆"
                        task.availability_zone = form["availability_zone"]
                        task.user_id = user.id
                        task.networkRef = form["network_id"]
                        # 元数据处理
                        if len(res["metadata"]) > 0:
                            task.os_type = res["metadata"]["os_type"]

                        session.add(task)
                    session.commit()
                    print("数据库任务创建成功")
                    # 系统盘克隆后直接退出，不处理其他盘
                    break

            print("=========访问成功===========")

            res = {
                "code": 200,
                "msg": "ok",
            }
            return res
        except Exception as e:
            error_code = 400  # 500 表示服务器内部错误
            error_message = f"Internal Server Error: {e}"

            # 返回自定义的错误信息
            return {"code": error_code, "msg": error_message}

    @post(_path="/v3/desk/clone", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def the_desk_clone_v3(self, form):
        try:
            # 获取虚拟机信息
            client = Client()
            print("参数：", form["vm_id"])
            res = client.NovaClient.openstack_getpost_server_detail(client, form['vm_id'])
            print("res信息：", res)
            if res["flavor"]:
                print("falvor_id：", res["flavor"]["id"])
                flavor_res = client.FlavorClient.openstack_get_flavor_detail(client, res["flavor"]["id"])
                res["vcpus"] = flavor_res["vcpus"]
                res["ram"] = int(flavor_res["ram"] / 1024)
                res["disk"] = flavor_res["disk"]
            vo_name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
            subnet_id = form["subnet_id"]
            vmport = client.NeutronClient.openstack_create_port_withoutip(client, form["network_id"], subnet_id, "克隆_" + vo_name)
            if len(res["volume_id"]) > 0:
                res["attachment"] = []
                volume_detail = client.VolumeClient.openstack_get_volume_detail(client, res["volume_id"][0])

                res["imagename"] = volume_detail["volume_image_metadata"].get("image_name", "")
                if volume_detail["volume_image_metadata"]:
                    image_id = volume_detail["volume_image_metadata"]["image_id"]
                elif isinstance(res["image"], dict):
                    image_id = res["image"]["id"]
                else:
                    image_id = ""

                for volume in res["volume_id"]:
                    print("卷信息：", volume)
                    volume_detail = client.VolumeClient.openstack_get_volume_detail(client, volume)
                    print("卷详情：", volume_detail)

                    new_name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
                    # 克隆新卷
                    new_volume = VolumeCloneForm()
                    new_volume.name = "克隆卷_" + volume_detail["name"]  # lsl
                    new_volume.size = volume_detail["size"]
                    new_volume.source_volid = volume

                    new_volume_res = client.VolumeClient.openstack_create_volume_by_type_to_clone(client, new_volume)
                    print("创建新卷的返回信息：", new_volume_res)

                    # TODO 创建一个空的云主机 并且把vm_id关联到任务表中'
                    # form.setdefault('name', "克隆_" + res["name"])  # lsl
                    # form.setdefault('flavorRef', res["flavor"]["id"])
                    # form.setdefault('imageRef', image_id)
                    # form.setdefault('networkRef', form.get("network_id", ""))
                    # if vmport != "":
                    #     form["ipv4"] = vmport
                    # instance = client.NovaClient.openstack_fast_create_server(client, form)

                    with self.application.session_scope() as session:
                        username = self.get_cookie("username", "")
                        user = session.query(User).filter(User.username == username).first()

                        task = InstanceCreateTask()
                        task.name = "克隆_" + res["name"] # lsl
                        task.flavorRef = res["flavor"]["id"]
                        task.volume_id = new_volume_res["id"]
                        task.imageRef = image_id
                        task.iso = "克隆"
                        task.availability_zone = form["availability_zone"]
                        task.user_id = user.id
                        task.networkRef = form["network_id"]
                        task.ipv4 = vmport
                        if form.get("os_type", ""):
                            task.os_type = form.get("os_type", "")
                        # task.vmid = instance["id"]

                        session.add(task)
                    session.commit()
                    print("数据库任务创建成功")
                    # 系统盘克隆后直接退出，不处理其他盘
                    break

            print("=========访问成功===========")

            res = {
                "code": 200,
                "msg": "ok",
            }
            return res
        except Exception as e:
            error_code = 400  # 500 表示服务器内部错误
            error_message = f"Internal Server Error: {e}"

            # 返回自定义的错误信息
            return {"code": error_code, "msg": error_message}

    @post(_path="/v4/desk/clone", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def the_desk_clone_v4(self, form):
        try:
            # 获取虚拟机信息
            client = Client()
            print("参数：", form["vm_id"])
            res = client.NovaClient.openstack_getpost_server_detail(client, form['vm_id'])
            print("res信息：", res)
            if res["flavor"]:
                print("falvor_id：", res["flavor"]["id"])
                flavor_res = client.FlavorClient.openstack_get_flavor_detail(client, res["flavor"]["id"])
                res["vcpus"] = flavor_res["vcpus"]
                res["ram"] = int(flavor_res["ram"] / 1024)
                res["disk"] = flavor_res["disk"]
            vo_name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
            subnet_id = form["subnet_id"]
            vmport = client.NeutronClient.openstack_create_port_withoutip(client, form["network_id"], subnet_id,
                                                                          "克隆_" + vo_name)
            if len(res["volume_id"]) > 0:
                res["attachment"] = []
                volume_detail = client.VolumeClient.openstack_get_volume_detail(client, res["volume_id"][0])

                res["imagename"] = volume_detail["volume_image_metadata"].get("image_name", "")
                if volume_detail["volume_image_metadata"]:
                    image_id = volume_detail["volume_image_metadata"]["image_id"]
                elif isinstance(res["image"], dict):
                    image_id = res["image"]["id"]
                else:
                    image_id = ""

                for volume in res["volume_id"]:
                    print("卷信息：", volume)
                    volume_detail = client.VolumeClient.openstack_get_volume_detail(client, volume)
                    print("卷详情：", volume_detail)

                    new_name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
                    # 克隆新卷
                    new_volume = VolumeCloneForm()
                    new_volume.name = "克隆卷_" + volume_detail["name"]  # lsl
                    new_volume.size = volume_detail["size"]
                    new_volume.source_volid = volume

                    new_volume_res = client.VolumeClient.openstack_create_volume_by_type_to_clone(client, new_volume)
                    print("创建新卷的返回信息：", new_volume_res)

                    # TODO 创建一个空的云主机 并且把vm_id关联到任务表中'
                    form.setdefault('name', "克隆_" + res["name"])  # lsl
                    form.setdefault('flavorRef', res["flavor"]["id"])
                    form.setdefault('imageRef', image_id)
                    form.setdefault('networkRef', form.get("network_id", ""))
                    if vmport != "":
                        form["ipv4"] = vmport
                    instance = client.NovaClient.openstack_fast_create_server(client, form)

                    with self.application.session_scope() as session:
                        username = self.get_cookie("username", "")
                        user = session.query(User).filter(User.username == username).first()

                        task = InstanceCreateTask()
                        task.name = "克隆_" + res["name"]  # lsl
                        task.flavorRef = res["flavor"]["id"]
                        task.volume_id = new_volume_res["id"]
                        task.imageRef = image_id
                        task.iso = "克隆"
                        task.availability_zone = form["availability_zone"]
                        task.user_id = user.id
                        task.networkRef = form["network_id"]
                        task.ipv4 = vmport
                        if form.get("os_type", ""):
                            task.os_type = form.get("os_type", "")
                        task.vmid = instance["id"]

                        session.add(task)
                    session.commit()
                    print("数据库任务创建成功")
                    # 系统盘克隆后直接退出，不处理其他盘
                    break

            print("=========访问成功===========")

            res = {
                "code": 200,
                "msg": "ok",
            }
            return res
        except Exception as e:
            error_code = 400  # 500 表示服务器内部错误
            error_message = f"Internal Server Error: {e}"

            # 返回自定义的错误信息
            return {"code": error_code, "msg": error_message}

import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from dacite import from_dict
from string import Template
from vmware.vapi.vsphere.client import create_vsphere_client
from com.vmware.vcenter_client import VM, Datacenter, Cluster, Host
from com.vmware.vcenter.vm_client import Power
from six.moves import cString<PERSON>
from vmware.vapi.bindings.struct import <PERSON><PERSON><PERSON><PERSON>

def pp(value):
    """ Utility method used to print the data nicely. """
    output = cStringIO()
    PrettyPrinter(stream=output).pprint(value)
    return output.getvalue()


class VcenterClient:



    def get_all_vm_list(self, filter):
        level = filter.get("level", "cloudy")
        
        if level == "cloudy":
            result = self.client.vcenter.VM.list()
        
        if level == "datacenter":
            name = filter.get("name")
            filter_spec = VM.FilterSpec(datacenters=set([name]))
            return self.client.vcenter.VM.list(filter_spec)

        if level == "cluster":
            name = filter.get("name")
            filter_spec = VM.FilterSpec(clusters=set([name]))
            return self.client.vcenter.VM.list(filter_spec)

        if level == "host":
            name = filter.get("name")
            filter_spec = VM.FilterSpec(hosts=set([name]))
            return self.client.vcenter.VM.list(filter_spec)
        
        return result
    
    def get_dists(self, vm_name):
        names = set([vm_name])
        vms = self.client.vcenter.VM.list(VM.FilterSpec(names=names))
    
        if len(vms) == 0:
            print("VM with name ({}) not found".format(vm_name))
            return None
    
        vm = vms[0].vm
        print("Found VM '{}' ({})".format(vm_name, vm))
        
        disk_summaries =  self.client.vcenter.vm.hardware.Disk.list(vm=vm)

        #这个方法可以查询详细信息        
        #info = self.client.vcenter.VM.get(vm)
        
        
        
        
        summaries = []
        for disk_summary in disk_summaries:
            disk = disk_summary.disk
            disk_info = self.client.vcenter.vm.hardware.Disk.get(vm=vm, disk=disk)
            
            """
            硬盘信息
            vm.hardware.Disk.get(vm-1052, 2000) -> Info(
                backing=BackingInfo(
                    type='VMDK_FILE',
                    vmdk_file='[VMWare1-USS3000] agent.mysql.103.update.vrts/agent.mysql.103.update.vrts-000001.vmdk',
                ),
                capacity=42949672960,
                ide=None,
                label='Hard disk 1',
                nvme=None,
                sata=None,
                scsi=ScsiAddressInfo(
                    bus=0,
                    unit=0,
                ),
                type='SCSI',
            ),            
            
            """
            print('vm.hardware.Disk.get({}, {}) -> {}'.
                  format(vm, disk, pp(disk_info)))
            tmp = {
                "capacity": disk_info.capacity,
                "label": disk_info.label
            }
            summaries.append(tmp)

        return summaries
    

    
    def get_vm_by_name(self, vm_name):
        names = set([vm_name])
        vms = self.client.vcenter.VM.list(VM.FilterSpec(names=names))
        if len(vms) == 0:
            print("VM with name ({}) not found".format(vm_name))
            return None
    
        vm = vms[0].vm
        print("Found VM '{}' ({})".format(vm_name, vm))
        return vm


    def get_datacenters(self):
        return self.client.vcenter.Datacenter.list()
    

    def get_clusters(self, datacenter):
        filter_spec = Cluster.FilterSpec(datacenters=set([datacenter]))
        return self.client.vcenter.Cluster.list(filter_spec)
    
    def get_hosts(self, cluster):
        filter_spec = Host.FilterSpec(clusters=set([cluster]))
        return self.client.vcenter.Host.list(filter_spec)
    
    
    def get_vpx_url_by_vm(self, vm_name):
        
        #这个方法可以查询详细信息        
        #info = self.client.vcenter.VM.get(vm)
        my_cluster = None
        my_host = None
        my_datacenter = None
        
        clusters = self.client.vcenter.Cluster.list()
        
        for cluster in clusters:
            vms = self.client.vcenter.VM.list(VM.FilterSpec(clusters={cluster.cluster}))
            if vm_name in [vm.name for vm in vms]:
                cluster_name = cluster.name
                my_cluster = cluster
                break
        
        filter_spec = Host.FilterSpec(clusters=set([my_cluster.cluster]))
        hosts = self.client.vcenter.Host.list(filter_spec)
        for host in hosts:
            vms = self.client.vcenter.VM.list(VM.FilterSpec(hosts={host.host}))
            if vm_name in [vm.name for vm in vms]:
                my_host = host
                break
        
        
        datacenters = self.client.vcenter.Datacenter.list()
        for datacenter in datacenters:
            filter_spec = Cluster.FilterSpec(datacenters=set([datacenter.datacenter]))
            clusters = self.client.vcenter.Cluster.list(filter_spec)
            if my_cluster in clusters:
                my_datacenter = datacenter
        
        print(my_datacenter.name)
        print(my_cluster.name)
        print(my_host.name)
        return (my_datacenter.name, my_cluster.name, my_host.name)
        
        
        
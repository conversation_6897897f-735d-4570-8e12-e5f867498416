# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: rpc.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from etcd3.etcdrpc import kv_pb2 as kv__pb2
from etcd3.etcdrpc import auth_pb2 as auth__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='rpc.proto',
  package='etcdserverpb',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\trpc.proto\x12\x0c\x65tcdserverpb\x1a\x08kv.proto\x1a\nauth.proto\"\\\n\x0eResponseHeader\x12\x12\n\ncluster_id\x18\x01 \x01(\x04\x12\x11\n\tmember_id\x18\x02 \x01(\x04\x12\x10\n\x08revision\x18\x03 \x01(\x03\x12\x11\n\traft_term\x18\x04 \x01(\x04\"\xe4\x03\n\x0cRangeRequest\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12\x11\n\trange_end\x18\x02 \x01(\x0c\x12\r\n\x05limit\x18\x03 \x01(\x03\x12\x10\n\x08revision\x18\x04 \x01(\x03\x12\x38\n\nsort_order\x18\x05 \x01(\x0e\x32$.etcdserverpb.RangeRequest.SortOrder\x12:\n\x0bsort_target\x18\x06 \x01(\x0e\x32%.etcdserverpb.RangeRequest.SortTarget\x12\x14\n\x0cserializable\x18\x07 \x01(\x08\x12\x11\n\tkeys_only\x18\x08 \x01(\x08\x12\x12\n\ncount_only\x18\t \x01(\x08\x12\x18\n\x10min_mod_revision\x18\n \x01(\x03\x12\x18\n\x10max_mod_revision\x18\x0b \x01(\x03\x12\x1b\n\x13min_create_revision\x18\x0c \x01(\x03\x12\x1b\n\x13max_create_revision\x18\r \x01(\x03\".\n\tSortOrder\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06\x41SCEND\x10\x01\x12\x0b\n\x07\x44\x45SCEND\x10\x02\"B\n\nSortTarget\x12\x07\n\x03KEY\x10\x00\x12\x0b\n\x07VERSION\x10\x01\x12\n\n\x06\x43REATE\x10\x02\x12\x07\n\x03MOD\x10\x03\x12\t\n\x05VALUE\x10\x04\"y\n\rRangeResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x1d\n\x03kvs\x18\x02 \x03(\x0b\x32\x10.mvccpb.KeyValue\x12\x0c\n\x04more\x18\x03 \x01(\x08\x12\r\n\x05\x63ount\x18\x04 \x01(\x03\"t\n\nPutRequest\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12\r\n\x05value\x18\x02 \x01(\x0c\x12\r\n\x05lease\x18\x03 \x01(\x03\x12\x0f\n\x07prev_kv\x18\x04 \x01(\x08\x12\x14\n\x0cignore_value\x18\x05 \x01(\x08\x12\x14\n\x0cignore_lease\x18\x06 \x01(\x08\"^\n\x0bPutResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12!\n\x07prev_kv\x18\x02 \x01(\x0b\x32\x10.mvccpb.KeyValue\"E\n\x12\x44\x65leteRangeRequest\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12\x11\n\trange_end\x18\x02 \x01(\x0c\x12\x0f\n\x07prev_kv\x18\x03 \x01(\x08\"x\n\x13\x44\x65leteRangeResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x0f\n\x07\x64\x65leted\x18\x02 \x01(\x03\x12\"\n\x08prev_kvs\x18\x03 \x03(\x0b\x32\x10.mvccpb.KeyValue\"\xef\x01\n\tRequestOp\x12\x33\n\rrequest_range\x18\x01 \x01(\x0b\x32\x1a.etcdserverpb.RangeRequestH\x00\x12/\n\x0brequest_put\x18\x02 \x01(\x0b\x32\x18.etcdserverpb.PutRequestH\x00\x12@\n\x14request_delete_range\x18\x03 \x01(\x0b\x32 .etcdserverpb.DeleteRangeRequestH\x00\x12/\n\x0brequest_txn\x18\x04 \x01(\x0b\x32\x18.etcdserverpb.TxnRequestH\x00\x42\t\n\x07request\"\xf9\x01\n\nResponseOp\x12\x35\n\x0eresponse_range\x18\x01 \x01(\x0b\x32\x1b.etcdserverpb.RangeResponseH\x00\x12\x31\n\x0cresponse_put\x18\x02 \x01(\x0b\x32\x19.etcdserverpb.PutResponseH\x00\x12\x42\n\x15response_delete_range\x18\x03 \x01(\x0b\x32!.etcdserverpb.DeleteRangeResponseH\x00\x12\x31\n\x0cresponse_txn\x18\x04 \x01(\x0b\x32\x19.etcdserverpb.TxnResponseH\x00\x42\n\n\x08response\"\x96\x03\n\x07\x43ompare\x12\x33\n\x06result\x18\x01 \x01(\x0e\x32#.etcdserverpb.Compare.CompareResult\x12\x33\n\x06target\x18\x02 \x01(\x0e\x32#.etcdserverpb.Compare.CompareTarget\x12\x0b\n\x03key\x18\x03 \x01(\x0c\x12\x11\n\x07version\x18\x04 \x01(\x03H\x00\x12\x19\n\x0f\x63reate_revision\x18\x05 \x01(\x03H\x00\x12\x16\n\x0cmod_revision\x18\x06 \x01(\x03H\x00\x12\x0f\n\x05value\x18\x07 \x01(\x0cH\x00\x12\x0f\n\x05lease\x18\x08 \x01(\x03H\x00\x12\x11\n\trange_end\x18@ \x01(\x0c\"@\n\rCompareResult\x12\t\n\x05\x45QUAL\x10\x00\x12\x0b\n\x07GREATER\x10\x01\x12\x08\n\x04LESS\x10\x02\x12\r\n\tNOT_EQUAL\x10\x03\"G\n\rCompareTarget\x12\x0b\n\x07VERSION\x10\x00\x12\n\n\x06\x43REATE\x10\x01\x12\x07\n\x03MOD\x10\x02\x12\t\n\x05VALUE\x10\x03\x12\t\n\x05LEASE\x10\x04\x42\x0e\n\x0ctarget_union\"\x88\x01\n\nTxnRequest\x12&\n\x07\x63ompare\x18\x01 \x03(\x0b\x32\x15.etcdserverpb.Compare\x12(\n\x07success\x18\x02 \x03(\x0b\x32\x17.etcdserverpb.RequestOp\x12(\n\x07\x66\x61ilure\x18\x03 \x03(\x0b\x32\x17.etcdserverpb.RequestOp\"{\n\x0bTxnResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x11\n\tsucceeded\x18\x02 \x01(\x08\x12+\n\tresponses\x18\x03 \x03(\x0b\x32\x18.etcdserverpb.ResponseOp\"7\n\x11\x43ompactionRequest\x12\x10\n\x08revision\x18\x01 \x01(\x03\x12\x10\n\x08physical\x18\x02 \x01(\x08\"B\n\x12\x43ompactionResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"\r\n\x0bHashRequest\"!\n\rHashKVRequest\x12\x10\n\x08revision\x18\x01 \x01(\x03\"f\n\x0eHashKVResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x0c\n\x04hash\x18\x02 \x01(\r\x12\x18\n\x10\x63ompact_revision\x18\x03 \x01(\x03\"J\n\x0cHashResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x0c\n\x04hash\x18\x02 \x01(\r\"\x11\n\x0fSnapshotRequest\"g\n\x10SnapshotResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x17\n\x0fremaining_bytes\x18\x02 \x01(\x04\x12\x0c\n\x04\x62lob\x18\x03 \x01(\x0c\"\x97\x01\n\x0cWatchRequest\x12:\n\x0e\x63reate_request\x18\x01 \x01(\x0b\x32 .etcdserverpb.WatchCreateRequestH\x00\x12:\n\x0e\x63\x61ncel_request\x18\x02 \x01(\x0b\x32 .etcdserverpb.WatchCancelRequestH\x00\x42\x0f\n\rrequest_union\"\xdb\x01\n\x12WatchCreateRequest\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12\x11\n\trange_end\x18\x02 \x01(\x0c\x12\x16\n\x0estart_revision\x18\x03 \x01(\x03\x12\x17\n\x0fprogress_notify\x18\x04 \x01(\x08\x12<\n\x07\x66ilters\x18\x05 \x03(\x0e\x32+.etcdserverpb.WatchCreateRequest.FilterType\x12\x0f\n\x07prev_kv\x18\x06 \x01(\x08\"%\n\nFilterType\x12\t\n\x05NOPUT\x10\x00\x12\x0c\n\x08NODELETE\x10\x01\"&\n\x12WatchCancelRequest\x12\x10\n\x08watch_id\x18\x01 \x01(\x03\"\xc2\x01\n\rWatchResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x10\n\x08watch_id\x18\x02 \x01(\x03\x12\x0f\n\x07\x63reated\x18\x03 \x01(\x08\x12\x10\n\x08\x63\x61nceled\x18\x04 \x01(\x08\x12\x18\n\x10\x63ompact_revision\x18\x05 \x01(\x03\x12\x15\n\rcancel_reason\x18\x06 \x01(\t\x12\x1d\n\x06\x65vents\x18\x0b \x03(\x0b\x32\r.mvccpb.Event\",\n\x11LeaseGrantRequest\x12\x0b\n\x03TTL\x18\x01 \x01(\x03\x12\n\n\x02ID\x18\x02 \x01(\x03\"j\n\x12LeaseGrantResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\n\n\x02ID\x18\x02 \x01(\x03\x12\x0b\n\x03TTL\x18\x03 \x01(\x03\x12\r\n\x05\x65rror\x18\x04 \x01(\t\" \n\x12LeaseRevokeRequest\x12\n\n\x02ID\x18\x01 \x01(\x03\"C\n\x13LeaseRevokeResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"#\n\x15LeaseKeepAliveRequest\x12\n\n\x02ID\x18\x01 \x01(\x03\"_\n\x16LeaseKeepAliveResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\n\n\x02ID\x18\x02 \x01(\x03\x12\x0b\n\x03TTL\x18\x03 \x01(\x03\"2\n\x16LeaseTimeToLiveRequest\x12\n\n\x02ID\x18\x01 \x01(\x03\x12\x0c\n\x04keys\x18\x02 \x01(\x08\"\x82\x01\n\x17LeaseTimeToLiveResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\n\n\x02ID\x18\x02 \x01(\x03\x12\x0b\n\x03TTL\x18\x03 \x01(\x03\x12\x12\n\ngrantedTTL\x18\x04 \x01(\x03\x12\x0c\n\x04keys\x18\x05 \x03(\x0c\"\x14\n\x12LeaseLeasesRequest\"\x19\n\x0bLeaseStatus\x12\n\n\x02ID\x18\x01 \x01(\x03\"n\n\x13LeaseLeasesResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12)\n\x06leases\x18\x02 \x03(\x0b\x32\x19.etcdserverpb.LeaseStatus\"H\n\x06Member\x12\n\n\x02ID\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08peerURLs\x18\x03 \x03(\t\x12\x12\n\nclientURLs\x18\x04 \x03(\t\"$\n\x10MemberAddRequest\x12\x10\n\x08peerURLs\x18\x01 \x03(\t\"\x8e\x01\n\x11MemberAddResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12$\n\x06member\x18\x02 \x01(\x0b\x32\x14.etcdserverpb.Member\x12%\n\x07members\x18\x03 \x03(\x0b\x32\x14.etcdserverpb.Member\"!\n\x13MemberRemoveRequest\x12\n\n\x02ID\x18\x01 \x01(\x04\"k\n\x14MemberRemoveResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12%\n\x07members\x18\x02 \x03(\x0b\x32\x14.etcdserverpb.Member\"3\n\x13MemberUpdateRequest\x12\n\n\x02ID\x18\x01 \x01(\x04\x12\x10\n\x08peerURLs\x18\x02 \x03(\t\"k\n\x14MemberUpdateResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12%\n\x07members\x18\x02 \x03(\x0b\x32\x14.etcdserverpb.Member\"\x13\n\x11MemberListRequest\"i\n\x12MemberListResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12%\n\x07members\x18\x02 \x03(\x0b\x32\x14.etcdserverpb.Member\"\x13\n\x11\x44\x65\x66ragmentRequest\"B\n\x12\x44\x65\x66ragmentResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"%\n\x11MoveLeaderRequest\x12\x10\n\x08targetID\x18\x01 \x01(\x04\"B\n\x12MoveLeaderResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"\xb6\x01\n\x0c\x41larmRequest\x12\x36\n\x06\x61\x63tion\x18\x01 \x01(\x0e\x32&.etcdserverpb.AlarmRequest.AlarmAction\x12\x10\n\x08memberID\x18\x02 \x01(\x04\x12&\n\x05\x61larm\x18\x03 \x01(\x0e\x32\x17.etcdserverpb.AlarmType\"4\n\x0b\x41larmAction\x12\x07\n\x03GET\x10\x00\x12\x0c\n\x08\x41\x43TIVATE\x10\x01\x12\x0e\n\nDEACTIVATE\x10\x02\"G\n\x0b\x41larmMember\x12\x10\n\x08memberID\x18\x01 \x01(\x04\x12&\n\x05\x61larm\x18\x02 \x01(\x0e\x32\x17.etcdserverpb.AlarmType\"h\n\rAlarmResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12)\n\x06\x61larms\x18\x02 \x03(\x0b\x32\x19.etcdserverpb.AlarmMember\"\x0f\n\rStatusRequest\"\x94\x01\n\x0eStatusResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x0e\n\x06\x64\x62Size\x18\x03 \x01(\x03\x12\x0e\n\x06leader\x18\x04 \x01(\x04\x12\x11\n\traftIndex\x18\x05 \x01(\x04\x12\x10\n\x08raftTerm\x18\x06 \x01(\x04\"\x13\n\x11\x41uthEnableRequest\"\x14\n\x12\x41uthDisableRequest\"5\n\x13\x41uthenticateRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\"4\n\x12\x41uthUserAddRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\"\"\n\x12\x41uthUserGetRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"%\n\x15\x41uthUserDeleteRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"?\n\x1d\x41uthUserChangePasswordRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\"6\n\x18\x41uthUserGrantRoleRequest\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x0c\n\x04role\x18\x02 \x01(\t\"7\n\x19\x41uthUserRevokeRoleRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04role\x18\x02 \x01(\t\"\"\n\x12\x41uthRoleAddRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\"\n\x12\x41uthRoleGetRequest\x12\x0c\n\x04role\x18\x01 \x01(\t\"\x15\n\x13\x41uthUserListRequest\"\x15\n\x13\x41uthRoleListRequest\"%\n\x15\x41uthRoleDeleteRequest\x12\x0c\n\x04role\x18\x01 \x01(\t\"P\n\x1e\x41uthRoleGrantPermissionRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12 \n\x04perm\x18\x02 \x01(\x0b\x32\x12.authpb.Permission\"O\n\x1f\x41uthRoleRevokePermissionRequest\x12\x0c\n\x04role\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\x11\n\trange_end\x18\x03 \x01(\t\"B\n\x12\x41uthEnableResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"C\n\x13\x41uthDisableResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"S\n\x14\x41uthenticateResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\r\n\x05token\x18\x02 \x01(\t\"C\n\x13\x41uthUserAddResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"R\n\x13\x41uthUserGetResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\r\n\x05roles\x18\x02 \x03(\t\"F\n\x16\x41uthUserDeleteResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"N\n\x1e\x41uthUserChangePasswordResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"I\n\x19\x41uthUserGrantRoleResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"J\n\x1a\x41uthUserRevokeRoleResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"C\n\x13\x41uthRoleAddResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"e\n\x13\x41uthRoleGetResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12 \n\x04perm\x18\x02 \x03(\x0b\x32\x12.authpb.Permission\"S\n\x14\x41uthRoleListResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\r\n\x05roles\x18\x02 \x03(\t\"S\n\x14\x41uthUserListResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\x12\r\n\x05users\x18\x02 \x03(\t\"F\n\x16\x41uthRoleDeleteResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"O\n\x1f\x41uthRoleGrantPermissionResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader\"P\n AuthRoleRevokePermissionResponse\x12,\n\x06header\x18\x01 \x01(\x0b\x32\x1c.etcdserverpb.ResponseHeader*/\n\tAlarmType\x12\x08\n\x04NONE\x10\x00\x12\x0b\n\x07NOSPACE\x10\x01\x12\x0b\n\x07\x43ORRUPT\x10\x02\x32\xea\x02\n\x02KV\x12\x42\n\x05Range\x12\x1a.etcdserverpb.RangeRequest\x1a\x1b.etcdserverpb.RangeResponse\"\x00\x12<\n\x03Put\x12\x18.etcdserverpb.PutRequest\x1a\x19.etcdserverpb.PutResponse\"\x00\x12T\n\x0b\x44\x65leteRange\x12 .etcdserverpb.DeleteRangeRequest\x1a!.etcdserverpb.DeleteRangeResponse\"\x00\x12<\n\x03Txn\x12\x18.etcdserverpb.TxnRequest\x1a\x19.etcdserverpb.TxnResponse\"\x00\x12N\n\x07\x43ompact\x12\x1f.etcdserverpb.CompactionRequest\x1a .etcdserverpb.CompactionResponse\"\x00\x32O\n\x05Watch\x12\x46\n\x05Watch\x12\x1a.etcdserverpb.WatchRequest\x1a\x1b.etcdserverpb.WatchResponse\"\x00(\x01\x30\x01\x32\xcb\x03\n\x05Lease\x12Q\n\nLeaseGrant\x12\x1f.etcdserverpb.LeaseGrantRequest\x1a .etcdserverpb.LeaseGrantResponse\"\x00\x12T\n\x0bLeaseRevoke\x12 .etcdserverpb.LeaseRevokeRequest\x1a!.etcdserverpb.LeaseRevokeResponse\"\x00\x12\x61\n\x0eLeaseKeepAlive\x12#.etcdserverpb.LeaseKeepAliveRequest\x1a$.etcdserverpb.LeaseKeepAliveResponse\"\x00(\x01\x30\x01\x12`\n\x0fLeaseTimeToLive\x12$.etcdserverpb.LeaseTimeToLiveRequest\x1a%.etcdserverpb.LeaseTimeToLiveResponse\"\x00\x12T\n\x0bLeaseLeases\x12 .etcdserverpb.LeaseLeasesRequest\x1a!.etcdserverpb.LeaseLeasesResponse\"\x00\x32\xde\x02\n\x07\x43luster\x12N\n\tMemberAdd\x12\x1e.etcdserverpb.MemberAddRequest\x1a\x1f.etcdserverpb.MemberAddResponse\"\x00\x12W\n\x0cMemberRemove\x12!.etcdserverpb.MemberRemoveRequest\x1a\".etcdserverpb.MemberRemoveResponse\"\x00\x12W\n\x0cMemberUpdate\x12!.etcdserverpb.MemberUpdateRequest\x1a\".etcdserverpb.MemberUpdateResponse\"\x00\x12Q\n\nMemberList\x12\x1f.etcdserverpb.MemberListRequest\x1a .etcdserverpb.MemberListResponse\"\x00\x32\x95\x04\n\x0bMaintenance\x12\x42\n\x05\x41larm\x12\x1a.etcdserverpb.AlarmRequest\x1a\x1b.etcdserverpb.AlarmResponse\"\x00\x12\x45\n\x06Status\x12\x1b.etcdserverpb.StatusRequest\x1a\x1c.etcdserverpb.StatusResponse\"\x00\x12Q\n\nDefragment\x12\x1f.etcdserverpb.DefragmentRequest\x1a .etcdserverpb.DefragmentResponse\"\x00\x12?\n\x04Hash\x12\x19.etcdserverpb.HashRequest\x1a\x1a.etcdserverpb.HashResponse\"\x00\x12\x45\n\x06HashKV\x12\x1b.etcdserverpb.HashKVRequest\x1a\x1c.etcdserverpb.HashKVResponse\"\x00\x12M\n\x08Snapshot\x12\x1d.etcdserverpb.SnapshotRequest\x1a\x1e.etcdserverpb.SnapshotResponse\"\x00\x30\x01\x12Q\n\nMoveLeader\x12\x1f.etcdserverpb.MoveLeaderRequest\x1a .etcdserverpb.MoveLeaderResponse\"\x00\x32\xdd\x0b\n\x04\x41uth\x12Q\n\nAuthEnable\x12\x1f.etcdserverpb.AuthEnableRequest\x1a .etcdserverpb.AuthEnableResponse\"\x00\x12T\n\x0b\x41uthDisable\x12 .etcdserverpb.AuthDisableRequest\x1a!.etcdserverpb.AuthDisableResponse\"\x00\x12W\n\x0c\x41uthenticate\x12!.etcdserverpb.AuthenticateRequest\x1a\".etcdserverpb.AuthenticateResponse\"\x00\x12P\n\x07UserAdd\x12 .etcdserverpb.AuthUserAddRequest\x1a!.etcdserverpb.AuthUserAddResponse\"\x00\x12P\n\x07UserGet\x12 .etcdserverpb.AuthUserGetRequest\x1a!.etcdserverpb.AuthUserGetResponse\"\x00\x12S\n\x08UserList\x12!.etcdserverpb.AuthUserListRequest\x1a\".etcdserverpb.AuthUserListResponse\"\x00\x12Y\n\nUserDelete\x12#.etcdserverpb.AuthUserDeleteRequest\x1a$.etcdserverpb.AuthUserDeleteResponse\"\x00\x12q\n\x12UserChangePassword\x12+.etcdserverpb.AuthUserChangePasswordRequest\x1a,.etcdserverpb.AuthUserChangePasswordResponse\"\x00\x12\x62\n\rUserGrantRole\x12&.etcdserverpb.AuthUserGrantRoleRequest\x1a\'.etcdserverpb.AuthUserGrantRoleResponse\"\x00\x12\x65\n\x0eUserRevokeRole\x12\'.etcdserverpb.AuthUserRevokeRoleRequest\x1a(.etcdserverpb.AuthUserRevokeRoleResponse\"\x00\x12P\n\x07RoleAdd\x12 .etcdserverpb.AuthRoleAddRequest\x1a!.etcdserverpb.AuthRoleAddResponse\"\x00\x12P\n\x07RoleGet\x12 .etcdserverpb.AuthRoleGetRequest\x1a!.etcdserverpb.AuthRoleGetResponse\"\x00\x12S\n\x08RoleList\x12!.etcdserverpb.AuthRoleListRequest\x1a\".etcdserverpb.AuthRoleListResponse\"\x00\x12Y\n\nRoleDelete\x12#.etcdserverpb.AuthRoleDeleteRequest\x1a$.etcdserverpb.AuthRoleDeleteResponse\"\x00\x12t\n\x13RoleGrantPermission\x12,.etcdserverpb.AuthRoleGrantPermissionRequest\x1a-.etcdserverpb.AuthRoleGrantPermissionResponse\"\x00\x12w\n\x14RoleRevokePermission\x12-.etcdserverpb.AuthRoleRevokePermissionRequest\x1a..etcdserverpb.AuthRoleRevokePermissionResponse\"\x00\x62\x06proto3')
  ,
  dependencies=[kv__pb2.DESCRIPTOR,auth__pb2.DESCRIPTOR,])

_ALARMTYPE = _descriptor.EnumDescriptor(
  name='AlarmType',
  full_name='etcdserverpb.AlarmType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NOSPACE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CORRUPT', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7555,
  serialized_end=7602,
)
_sym_db.RegisterEnumDescriptor(_ALARMTYPE)

AlarmType = enum_type_wrapper.EnumTypeWrapper(_ALARMTYPE)
NONE = 0
NOSPACE = 1
CORRUPT = 2


_RANGEREQUEST_SORTORDER = _descriptor.EnumDescriptor(
  name='SortOrder',
  full_name='etcdserverpb.RangeRequest.SortOrder',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ASCEND', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DESCEND', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=514,
  serialized_end=560,
)
_sym_db.RegisterEnumDescriptor(_RANGEREQUEST_SORTORDER)

_RANGEREQUEST_SORTTARGET = _descriptor.EnumDescriptor(
  name='SortTarget',
  full_name='etcdserverpb.RangeRequest.SortTarget',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='KEY', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='VERSION', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CREATE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MOD', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='VALUE', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=562,
  serialized_end=628,
)
_sym_db.RegisterEnumDescriptor(_RANGEREQUEST_SORTTARGET)

_COMPARE_COMPARERESULT = _descriptor.EnumDescriptor(
  name='CompareResult',
  full_name='etcdserverpb.Compare.CompareResult',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='EQUAL', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GREATER', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LESS', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NOT_EQUAL', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1908,
  serialized_end=1972,
)
_sym_db.RegisterEnumDescriptor(_COMPARE_COMPARERESULT)

_COMPARE_COMPARETARGET = _descriptor.EnumDescriptor(
  name='CompareTarget',
  full_name='etcdserverpb.Compare.CompareTarget',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='VERSION', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CREATE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MOD', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='VALUE', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LEASE', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1974,
  serialized_end=2045,
)
_sym_db.RegisterEnumDescriptor(_COMPARE_COMPARETARGET)

_WATCHCREATEREQUEST_FILTERTYPE = _descriptor.EnumDescriptor(
  name='FilterType',
  full_name='etcdserverpb.WatchCreateRequest.FilterType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NOPUT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NODELETE', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3143,
  serialized_end=3180,
)
_sym_db.RegisterEnumDescriptor(_WATCHCREATEREQUEST_FILTERTYPE)

_ALARMREQUEST_ALARMACTION = _descriptor.EnumDescriptor(
  name='AlarmAction',
  full_name='etcdserverpb.AlarmRequest.AlarmAction',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GET', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTIVATE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEACTIVATE', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5174,
  serialized_end=5226,
)
_sym_db.RegisterEnumDescriptor(_ALARMREQUEST_ALARMACTION)


_RESPONSEHEADER = _descriptor.Descriptor(
  name='ResponseHeader',
  full_name='etcdserverpb.ResponseHeader',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cluster_id', full_name='etcdserverpb.ResponseHeader.cluster_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member_id', full_name='etcdserverpb.ResponseHeader.member_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='revision', full_name='etcdserverpb.ResponseHeader.revision', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raft_term', full_name='etcdserverpb.ResponseHeader.raft_term', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=49,
  serialized_end=141,
)


_RANGEREQUEST = _descriptor.Descriptor(
  name='RangeRequest',
  full_name='etcdserverpb.RangeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='etcdserverpb.RangeRequest.key', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='range_end', full_name='etcdserverpb.RangeRequest.range_end', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='etcdserverpb.RangeRequest.limit', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='revision', full_name='etcdserverpb.RangeRequest.revision', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort_order', full_name='etcdserverpb.RangeRequest.sort_order', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort_target', full_name='etcdserverpb.RangeRequest.sort_target', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='serializable', full_name='etcdserverpb.RangeRequest.serializable', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keys_only', full_name='etcdserverpb.RangeRequest.keys_only', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count_only', full_name='etcdserverpb.RangeRequest.count_only', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_mod_revision', full_name='etcdserverpb.RangeRequest.min_mod_revision', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_mod_revision', full_name='etcdserverpb.RangeRequest.max_mod_revision', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_create_revision', full_name='etcdserverpb.RangeRequest.min_create_revision', index=11,
      number=12, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_create_revision', full_name='etcdserverpb.RangeRequest.max_create_revision', index=12,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _RANGEREQUEST_SORTORDER,
    _RANGEREQUEST_SORTTARGET,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=144,
  serialized_end=628,
)


_RANGERESPONSE = _descriptor.Descriptor(
  name='RangeResponse',
  full_name='etcdserverpb.RangeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.RangeResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kvs', full_name='etcdserverpb.RangeResponse.kvs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='more', full_name='etcdserverpb.RangeResponse.more', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='etcdserverpb.RangeResponse.count', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=630,
  serialized_end=751,
)


_PUTREQUEST = _descriptor.Descriptor(
  name='PutRequest',
  full_name='etcdserverpb.PutRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='etcdserverpb.PutRequest.key', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='etcdserverpb.PutRequest.value', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lease', full_name='etcdserverpb.PutRequest.lease', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_kv', full_name='etcdserverpb.PutRequest.prev_kv', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignore_value', full_name='etcdserverpb.PutRequest.ignore_value', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignore_lease', full_name='etcdserverpb.PutRequest.ignore_lease', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=753,
  serialized_end=869,
)


_PUTRESPONSE = _descriptor.Descriptor(
  name='PutResponse',
  full_name='etcdserverpb.PutResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.PutResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_kv', full_name='etcdserverpb.PutResponse.prev_kv', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=871,
  serialized_end=965,
)


_DELETERANGEREQUEST = _descriptor.Descriptor(
  name='DeleteRangeRequest',
  full_name='etcdserverpb.DeleteRangeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='etcdserverpb.DeleteRangeRequest.key', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='range_end', full_name='etcdserverpb.DeleteRangeRequest.range_end', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_kv', full_name='etcdserverpb.DeleteRangeRequest.prev_kv', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=967,
  serialized_end=1036,
)


_DELETERANGERESPONSE = _descriptor.Descriptor(
  name='DeleteRangeResponse',
  full_name='etcdserverpb.DeleteRangeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.DeleteRangeResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='etcdserverpb.DeleteRangeResponse.deleted', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_kvs', full_name='etcdserverpb.DeleteRangeResponse.prev_kvs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1038,
  serialized_end=1158,
)


_REQUESTOP = _descriptor.Descriptor(
  name='RequestOp',
  full_name='etcdserverpb.RequestOp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_range', full_name='etcdserverpb.RequestOp.request_range', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_put', full_name='etcdserverpb.RequestOp.request_put', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_delete_range', full_name='etcdserverpb.RequestOp.request_delete_range', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_txn', full_name='etcdserverpb.RequestOp.request_txn', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='etcdserverpb.RequestOp.request',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=1161,
  serialized_end=1400,
)


_RESPONSEOP = _descriptor.Descriptor(
  name='ResponseOp',
  full_name='etcdserverpb.ResponseOp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='response_range', full_name='etcdserverpb.ResponseOp.response_range', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='response_put', full_name='etcdserverpb.ResponseOp.response_put', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='response_delete_range', full_name='etcdserverpb.ResponseOp.response_delete_range', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='response_txn', full_name='etcdserverpb.ResponseOp.response_txn', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='response', full_name='etcdserverpb.ResponseOp.response',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=1403,
  serialized_end=1652,
)


_COMPARE = _descriptor.Descriptor(
  name='Compare',
  full_name='etcdserverpb.Compare',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='etcdserverpb.Compare.result', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target', full_name='etcdserverpb.Compare.target', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='key', full_name='etcdserverpb.Compare.key', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version', full_name='etcdserverpb.Compare.version', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='create_revision', full_name='etcdserverpb.Compare.create_revision', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mod_revision', full_name='etcdserverpb.Compare.mod_revision', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='etcdserverpb.Compare.value', index=6,
      number=7, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lease', full_name='etcdserverpb.Compare.lease', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='range_end', full_name='etcdserverpb.Compare.range_end', index=8,
      number=64, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _COMPARE_COMPARERESULT,
    _COMPARE_COMPARETARGET,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='target_union', full_name='etcdserverpb.Compare.target_union',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=1655,
  serialized_end=2061,
)


_TXNREQUEST = _descriptor.Descriptor(
  name='TxnRequest',
  full_name='etcdserverpb.TxnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='compare', full_name='etcdserverpb.TxnRequest.compare', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='success', full_name='etcdserverpb.TxnRequest.success', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='failure', full_name='etcdserverpb.TxnRequest.failure', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2064,
  serialized_end=2200,
)


_TXNRESPONSE = _descriptor.Descriptor(
  name='TxnResponse',
  full_name='etcdserverpb.TxnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.TxnResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='succeeded', full_name='etcdserverpb.TxnResponse.succeeded', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='responses', full_name='etcdserverpb.TxnResponse.responses', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2202,
  serialized_end=2325,
)


_COMPACTIONREQUEST = _descriptor.Descriptor(
  name='CompactionRequest',
  full_name='etcdserverpb.CompactionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='revision', full_name='etcdserverpb.CompactionRequest.revision', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='physical', full_name='etcdserverpb.CompactionRequest.physical', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2327,
  serialized_end=2382,
)


_COMPACTIONRESPONSE = _descriptor.Descriptor(
  name='CompactionResponse',
  full_name='etcdserverpb.CompactionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.CompactionResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2384,
  serialized_end=2450,
)


_HASHREQUEST = _descriptor.Descriptor(
  name='HashRequest',
  full_name='etcdserverpb.HashRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2452,
  serialized_end=2465,
)


_HASHKVREQUEST = _descriptor.Descriptor(
  name='HashKVRequest',
  full_name='etcdserverpb.HashKVRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='revision', full_name='etcdserverpb.HashKVRequest.revision', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2467,
  serialized_end=2500,
)


_HASHKVRESPONSE = _descriptor.Descriptor(
  name='HashKVResponse',
  full_name='etcdserverpb.HashKVResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.HashKVResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hash', full_name='etcdserverpb.HashKVResponse.hash', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compact_revision', full_name='etcdserverpb.HashKVResponse.compact_revision', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2502,
  serialized_end=2604,
)


_HASHRESPONSE = _descriptor.Descriptor(
  name='HashResponse',
  full_name='etcdserverpb.HashResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.HashResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hash', full_name='etcdserverpb.HashResponse.hash', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2606,
  serialized_end=2680,
)


_SNAPSHOTREQUEST = _descriptor.Descriptor(
  name='SnapshotRequest',
  full_name='etcdserverpb.SnapshotRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2682,
  serialized_end=2699,
)


_SNAPSHOTRESPONSE = _descriptor.Descriptor(
  name='SnapshotResponse',
  full_name='etcdserverpb.SnapshotResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.SnapshotResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remaining_bytes', full_name='etcdserverpb.SnapshotResponse.remaining_bytes', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blob', full_name='etcdserverpb.SnapshotResponse.blob', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2701,
  serialized_end=2804,
)


_WATCHREQUEST = _descriptor.Descriptor(
  name='WatchRequest',
  full_name='etcdserverpb.WatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='create_request', full_name='etcdserverpb.WatchRequest.create_request', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cancel_request', full_name='etcdserverpb.WatchRequest.cancel_request', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request_union', full_name='etcdserverpb.WatchRequest.request_union',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=2807,
  serialized_end=2958,
)


_WATCHCREATEREQUEST = _descriptor.Descriptor(
  name='WatchCreateRequest',
  full_name='etcdserverpb.WatchCreateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='etcdserverpb.WatchCreateRequest.key', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='range_end', full_name='etcdserverpb.WatchCreateRequest.range_end', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_revision', full_name='etcdserverpb.WatchCreateRequest.start_revision', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='progress_notify', full_name='etcdserverpb.WatchCreateRequest.progress_notify', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='etcdserverpb.WatchCreateRequest.filters', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_kv', full_name='etcdserverpb.WatchCreateRequest.prev_kv', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _WATCHCREATEREQUEST_FILTERTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2961,
  serialized_end=3180,
)


_WATCHCANCELREQUEST = _descriptor.Descriptor(
  name='WatchCancelRequest',
  full_name='etcdserverpb.WatchCancelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='watch_id', full_name='etcdserverpb.WatchCancelRequest.watch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3182,
  serialized_end=3220,
)


_WATCHRESPONSE = _descriptor.Descriptor(
  name='WatchResponse',
  full_name='etcdserverpb.WatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.WatchResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='watch_id', full_name='etcdserverpb.WatchResponse.watch_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='etcdserverpb.WatchResponse.created', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='canceled', full_name='etcdserverpb.WatchResponse.canceled', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compact_revision', full_name='etcdserverpb.WatchResponse.compact_revision', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cancel_reason', full_name='etcdserverpb.WatchResponse.cancel_reason', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='events', full_name='etcdserverpb.WatchResponse.events', index=6,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3223,
  serialized_end=3417,
)


_LEASEGRANTREQUEST = _descriptor.Descriptor(
  name='LeaseGrantRequest',
  full_name='etcdserverpb.LeaseGrantRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='TTL', full_name='etcdserverpb.LeaseGrantRequest.TTL', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseGrantRequest.ID', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3419,
  serialized_end=3463,
)


_LEASEGRANTRESPONSE = _descriptor.Descriptor(
  name='LeaseGrantResponse',
  full_name='etcdserverpb.LeaseGrantResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.LeaseGrantResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseGrantResponse.ID', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='TTL', full_name='etcdserverpb.LeaseGrantResponse.TTL', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='etcdserverpb.LeaseGrantResponse.error', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3465,
  serialized_end=3571,
)


_LEASEREVOKEREQUEST = _descriptor.Descriptor(
  name='LeaseRevokeRequest',
  full_name='etcdserverpb.LeaseRevokeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseRevokeRequest.ID', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3573,
  serialized_end=3605,
)


_LEASEREVOKERESPONSE = _descriptor.Descriptor(
  name='LeaseRevokeResponse',
  full_name='etcdserverpb.LeaseRevokeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.LeaseRevokeResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3607,
  serialized_end=3674,
)


_LEASEKEEPALIVEREQUEST = _descriptor.Descriptor(
  name='LeaseKeepAliveRequest',
  full_name='etcdserverpb.LeaseKeepAliveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseKeepAliveRequest.ID', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3676,
  serialized_end=3711,
)


_LEASEKEEPALIVERESPONSE = _descriptor.Descriptor(
  name='LeaseKeepAliveResponse',
  full_name='etcdserverpb.LeaseKeepAliveResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.LeaseKeepAliveResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseKeepAliveResponse.ID', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='TTL', full_name='etcdserverpb.LeaseKeepAliveResponse.TTL', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3713,
  serialized_end=3808,
)


_LEASETIMETOLIVEREQUEST = _descriptor.Descriptor(
  name='LeaseTimeToLiveRequest',
  full_name='etcdserverpb.LeaseTimeToLiveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseTimeToLiveRequest.ID', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keys', full_name='etcdserverpb.LeaseTimeToLiveRequest.keys', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3810,
  serialized_end=3860,
)


_LEASETIMETOLIVERESPONSE = _descriptor.Descriptor(
  name='LeaseTimeToLiveResponse',
  full_name='etcdserverpb.LeaseTimeToLiveResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.LeaseTimeToLiveResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseTimeToLiveResponse.ID', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='TTL', full_name='etcdserverpb.LeaseTimeToLiveResponse.TTL', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='grantedTTL', full_name='etcdserverpb.LeaseTimeToLiveResponse.grantedTTL', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keys', full_name='etcdserverpb.LeaseTimeToLiveResponse.keys', index=4,
      number=5, type=12, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3863,
  serialized_end=3993,
)


_LEASELEASESREQUEST = _descriptor.Descriptor(
  name='LeaseLeasesRequest',
  full_name='etcdserverpb.LeaseLeasesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3995,
  serialized_end=4015,
)


_LEASESTATUS = _descriptor.Descriptor(
  name='LeaseStatus',
  full_name='etcdserverpb.LeaseStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.LeaseStatus.ID', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4017,
  serialized_end=4042,
)


_LEASELEASESRESPONSE = _descriptor.Descriptor(
  name='LeaseLeasesResponse',
  full_name='etcdserverpb.LeaseLeasesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.LeaseLeasesResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='leases', full_name='etcdserverpb.LeaseLeasesResponse.leases', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4044,
  serialized_end=4154,
)


_MEMBER = _descriptor.Descriptor(
  name='Member',
  full_name='etcdserverpb.Member',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.Member.ID', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.Member.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='peerURLs', full_name='etcdserverpb.Member.peerURLs', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='clientURLs', full_name='etcdserverpb.Member.clientURLs', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4156,
  serialized_end=4228,
)


_MEMBERADDREQUEST = _descriptor.Descriptor(
  name='MemberAddRequest',
  full_name='etcdserverpb.MemberAddRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='peerURLs', full_name='etcdserverpb.MemberAddRequest.peerURLs', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4230,
  serialized_end=4266,
)


_MEMBERADDRESPONSE = _descriptor.Descriptor(
  name='MemberAddResponse',
  full_name='etcdserverpb.MemberAddResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.MemberAddResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member', full_name='etcdserverpb.MemberAddResponse.member', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='members', full_name='etcdserverpb.MemberAddResponse.members', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4269,
  serialized_end=4411,
)


_MEMBERREMOVEREQUEST = _descriptor.Descriptor(
  name='MemberRemoveRequest',
  full_name='etcdserverpb.MemberRemoveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.MemberRemoveRequest.ID', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4413,
  serialized_end=4446,
)


_MEMBERREMOVERESPONSE = _descriptor.Descriptor(
  name='MemberRemoveResponse',
  full_name='etcdserverpb.MemberRemoveResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.MemberRemoveResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='members', full_name='etcdserverpb.MemberRemoveResponse.members', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4448,
  serialized_end=4555,
)


_MEMBERUPDATEREQUEST = _descriptor.Descriptor(
  name='MemberUpdateRequest',
  full_name='etcdserverpb.MemberUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ID', full_name='etcdserverpb.MemberUpdateRequest.ID', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='peerURLs', full_name='etcdserverpb.MemberUpdateRequest.peerURLs', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4557,
  serialized_end=4608,
)


_MEMBERUPDATERESPONSE = _descriptor.Descriptor(
  name='MemberUpdateResponse',
  full_name='etcdserverpb.MemberUpdateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.MemberUpdateResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='members', full_name='etcdserverpb.MemberUpdateResponse.members', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4610,
  serialized_end=4717,
)


_MEMBERLISTREQUEST = _descriptor.Descriptor(
  name='MemberListRequest',
  full_name='etcdserverpb.MemberListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4719,
  serialized_end=4738,
)


_MEMBERLISTRESPONSE = _descriptor.Descriptor(
  name='MemberListResponse',
  full_name='etcdserverpb.MemberListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.MemberListResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='members', full_name='etcdserverpb.MemberListResponse.members', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4740,
  serialized_end=4845,
)


_DEFRAGMENTREQUEST = _descriptor.Descriptor(
  name='DefragmentRequest',
  full_name='etcdserverpb.DefragmentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4847,
  serialized_end=4866,
)


_DEFRAGMENTRESPONSE = _descriptor.Descriptor(
  name='DefragmentResponse',
  full_name='etcdserverpb.DefragmentResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.DefragmentResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4868,
  serialized_end=4934,
)


_MOVELEADERREQUEST = _descriptor.Descriptor(
  name='MoveLeaderRequest',
  full_name='etcdserverpb.MoveLeaderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='targetID', full_name='etcdserverpb.MoveLeaderRequest.targetID', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4936,
  serialized_end=4973,
)


_MOVELEADERRESPONSE = _descriptor.Descriptor(
  name='MoveLeaderResponse',
  full_name='etcdserverpb.MoveLeaderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.MoveLeaderResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4975,
  serialized_end=5041,
)


_ALARMREQUEST = _descriptor.Descriptor(
  name='AlarmRequest',
  full_name='etcdserverpb.AlarmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='action', full_name='etcdserverpb.AlarmRequest.action', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='memberID', full_name='etcdserverpb.AlarmRequest.memberID', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alarm', full_name='etcdserverpb.AlarmRequest.alarm', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ALARMREQUEST_ALARMACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5044,
  serialized_end=5226,
)


_ALARMMEMBER = _descriptor.Descriptor(
  name='AlarmMember',
  full_name='etcdserverpb.AlarmMember',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='memberID', full_name='etcdserverpb.AlarmMember.memberID', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alarm', full_name='etcdserverpb.AlarmMember.alarm', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5228,
  serialized_end=5299,
)


_ALARMRESPONSE = _descriptor.Descriptor(
  name='AlarmResponse',
  full_name='etcdserverpb.AlarmResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AlarmResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alarms', full_name='etcdserverpb.AlarmResponse.alarms', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5301,
  serialized_end=5405,
)


_STATUSREQUEST = _descriptor.Descriptor(
  name='StatusRequest',
  full_name='etcdserverpb.StatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5407,
  serialized_end=5422,
)


_STATUSRESPONSE = _descriptor.Descriptor(
  name='StatusResponse',
  full_name='etcdserverpb.StatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.StatusResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version', full_name='etcdserverpb.StatusResponse.version', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dbSize', full_name='etcdserverpb.StatusResponse.dbSize', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='leader', full_name='etcdserverpb.StatusResponse.leader', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raftIndex', full_name='etcdserverpb.StatusResponse.raftIndex', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raftTerm', full_name='etcdserverpb.StatusResponse.raftTerm', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5425,
  serialized_end=5573,
)


_AUTHENABLEREQUEST = _descriptor.Descriptor(
  name='AuthEnableRequest',
  full_name='etcdserverpb.AuthEnableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5575,
  serialized_end=5594,
)


_AUTHDISABLEREQUEST = _descriptor.Descriptor(
  name='AuthDisableRequest',
  full_name='etcdserverpb.AuthDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5596,
  serialized_end=5616,
)


_AUTHENTICATEREQUEST = _descriptor.Descriptor(
  name='AuthenticateRequest',
  full_name='etcdserverpb.AuthenticateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthenticateRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='password', full_name='etcdserverpb.AuthenticateRequest.password', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5618,
  serialized_end=5671,
)


_AUTHUSERADDREQUEST = _descriptor.Descriptor(
  name='AuthUserAddRequest',
  full_name='etcdserverpb.AuthUserAddRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthUserAddRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='password', full_name='etcdserverpb.AuthUserAddRequest.password', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5673,
  serialized_end=5725,
)


_AUTHUSERGETREQUEST = _descriptor.Descriptor(
  name='AuthUserGetRequest',
  full_name='etcdserverpb.AuthUserGetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthUserGetRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5727,
  serialized_end=5761,
)


_AUTHUSERDELETEREQUEST = _descriptor.Descriptor(
  name='AuthUserDeleteRequest',
  full_name='etcdserverpb.AuthUserDeleteRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthUserDeleteRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5763,
  serialized_end=5800,
)


_AUTHUSERCHANGEPASSWORDREQUEST = _descriptor.Descriptor(
  name='AuthUserChangePasswordRequest',
  full_name='etcdserverpb.AuthUserChangePasswordRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthUserChangePasswordRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='password', full_name='etcdserverpb.AuthUserChangePasswordRequest.password', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5802,
  serialized_end=5865,
)


_AUTHUSERGRANTROLEREQUEST = _descriptor.Descriptor(
  name='AuthUserGrantRoleRequest',
  full_name='etcdserverpb.AuthUserGrantRoleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='user', full_name='etcdserverpb.AuthUserGrantRoleRequest.user', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='role', full_name='etcdserverpb.AuthUserGrantRoleRequest.role', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5867,
  serialized_end=5921,
)


_AUTHUSERREVOKEROLEREQUEST = _descriptor.Descriptor(
  name='AuthUserRevokeRoleRequest',
  full_name='etcdserverpb.AuthUserRevokeRoleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthUserRevokeRoleRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='role', full_name='etcdserverpb.AuthUserRevokeRoleRequest.role', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5923,
  serialized_end=5978,
)


_AUTHROLEADDREQUEST = _descriptor.Descriptor(
  name='AuthRoleAddRequest',
  full_name='etcdserverpb.AuthRoleAddRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthRoleAddRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5980,
  serialized_end=6014,
)


_AUTHROLEGETREQUEST = _descriptor.Descriptor(
  name='AuthRoleGetRequest',
  full_name='etcdserverpb.AuthRoleGetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='role', full_name='etcdserverpb.AuthRoleGetRequest.role', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6016,
  serialized_end=6050,
)


_AUTHUSERLISTREQUEST = _descriptor.Descriptor(
  name='AuthUserListRequest',
  full_name='etcdserverpb.AuthUserListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6052,
  serialized_end=6073,
)


_AUTHROLELISTREQUEST = _descriptor.Descriptor(
  name='AuthRoleListRequest',
  full_name='etcdserverpb.AuthRoleListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6075,
  serialized_end=6096,
)


_AUTHROLEDELETEREQUEST = _descriptor.Descriptor(
  name='AuthRoleDeleteRequest',
  full_name='etcdserverpb.AuthRoleDeleteRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='role', full_name='etcdserverpb.AuthRoleDeleteRequest.role', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6098,
  serialized_end=6135,
)


_AUTHROLEGRANTPERMISSIONREQUEST = _descriptor.Descriptor(
  name='AuthRoleGrantPermissionRequest',
  full_name='etcdserverpb.AuthRoleGrantPermissionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='etcdserverpb.AuthRoleGrantPermissionRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='perm', full_name='etcdserverpb.AuthRoleGrantPermissionRequest.perm', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6137,
  serialized_end=6217,
)


_AUTHROLEREVOKEPERMISSIONREQUEST = _descriptor.Descriptor(
  name='AuthRoleRevokePermissionRequest',
  full_name='etcdserverpb.AuthRoleRevokePermissionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='role', full_name='etcdserverpb.AuthRoleRevokePermissionRequest.role', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='key', full_name='etcdserverpb.AuthRoleRevokePermissionRequest.key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='range_end', full_name='etcdserverpb.AuthRoleRevokePermissionRequest.range_end', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6219,
  serialized_end=6298,
)


_AUTHENABLERESPONSE = _descriptor.Descriptor(
  name='AuthEnableResponse',
  full_name='etcdserverpb.AuthEnableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthEnableResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6300,
  serialized_end=6366,
)


_AUTHDISABLERESPONSE = _descriptor.Descriptor(
  name='AuthDisableResponse',
  full_name='etcdserverpb.AuthDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthDisableResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6368,
  serialized_end=6435,
)


_AUTHENTICATERESPONSE = _descriptor.Descriptor(
  name='AuthenticateResponse',
  full_name='etcdserverpb.AuthenticateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthenticateResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='token', full_name='etcdserverpb.AuthenticateResponse.token', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6437,
  serialized_end=6520,
)


_AUTHUSERADDRESPONSE = _descriptor.Descriptor(
  name='AuthUserAddResponse',
  full_name='etcdserverpb.AuthUserAddResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserAddResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6522,
  serialized_end=6589,
)


_AUTHUSERGETRESPONSE = _descriptor.Descriptor(
  name='AuthUserGetResponse',
  full_name='etcdserverpb.AuthUserGetResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserGetResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roles', full_name='etcdserverpb.AuthUserGetResponse.roles', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6591,
  serialized_end=6673,
)


_AUTHUSERDELETERESPONSE = _descriptor.Descriptor(
  name='AuthUserDeleteResponse',
  full_name='etcdserverpb.AuthUserDeleteResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserDeleteResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6675,
  serialized_end=6745,
)


_AUTHUSERCHANGEPASSWORDRESPONSE = _descriptor.Descriptor(
  name='AuthUserChangePasswordResponse',
  full_name='etcdserverpb.AuthUserChangePasswordResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserChangePasswordResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6747,
  serialized_end=6825,
)


_AUTHUSERGRANTROLERESPONSE = _descriptor.Descriptor(
  name='AuthUserGrantRoleResponse',
  full_name='etcdserverpb.AuthUserGrantRoleResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserGrantRoleResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6827,
  serialized_end=6900,
)


_AUTHUSERREVOKEROLERESPONSE = _descriptor.Descriptor(
  name='AuthUserRevokeRoleResponse',
  full_name='etcdserverpb.AuthUserRevokeRoleResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserRevokeRoleResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6902,
  serialized_end=6976,
)


_AUTHROLEADDRESPONSE = _descriptor.Descriptor(
  name='AuthRoleAddResponse',
  full_name='etcdserverpb.AuthRoleAddResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthRoleAddResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6978,
  serialized_end=7045,
)


_AUTHROLEGETRESPONSE = _descriptor.Descriptor(
  name='AuthRoleGetResponse',
  full_name='etcdserverpb.AuthRoleGetResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthRoleGetResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='perm', full_name='etcdserverpb.AuthRoleGetResponse.perm', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7047,
  serialized_end=7148,
)


_AUTHROLELISTRESPONSE = _descriptor.Descriptor(
  name='AuthRoleListResponse',
  full_name='etcdserverpb.AuthRoleListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthRoleListResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roles', full_name='etcdserverpb.AuthRoleListResponse.roles', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7150,
  serialized_end=7233,
)


_AUTHUSERLISTRESPONSE = _descriptor.Descriptor(
  name='AuthUserListResponse',
  full_name='etcdserverpb.AuthUserListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthUserListResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='users', full_name='etcdserverpb.AuthUserListResponse.users', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7235,
  serialized_end=7318,
)


_AUTHROLEDELETERESPONSE = _descriptor.Descriptor(
  name='AuthRoleDeleteResponse',
  full_name='etcdserverpb.AuthRoleDeleteResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthRoleDeleteResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7320,
  serialized_end=7390,
)


_AUTHROLEGRANTPERMISSIONRESPONSE = _descriptor.Descriptor(
  name='AuthRoleGrantPermissionResponse',
  full_name='etcdserverpb.AuthRoleGrantPermissionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthRoleGrantPermissionResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7392,
  serialized_end=7471,
)


_AUTHROLEREVOKEPERMISSIONRESPONSE = _descriptor.Descriptor(
  name='AuthRoleRevokePermissionResponse',
  full_name='etcdserverpb.AuthRoleRevokePermissionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='etcdserverpb.AuthRoleRevokePermissionResponse.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7473,
  serialized_end=7553,
)

_RANGEREQUEST.fields_by_name['sort_order'].enum_type = _RANGEREQUEST_SORTORDER
_RANGEREQUEST.fields_by_name['sort_target'].enum_type = _RANGEREQUEST_SORTTARGET
_RANGEREQUEST_SORTORDER.containing_type = _RANGEREQUEST
_RANGEREQUEST_SORTTARGET.containing_type = _RANGEREQUEST
_RANGERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_RANGERESPONSE.fields_by_name['kvs'].message_type = kv__pb2._KEYVALUE
_PUTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_PUTRESPONSE.fields_by_name['prev_kv'].message_type = kv__pb2._KEYVALUE
_DELETERANGERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_DELETERANGERESPONSE.fields_by_name['prev_kvs'].message_type = kv__pb2._KEYVALUE
_REQUESTOP.fields_by_name['request_range'].message_type = _RANGEREQUEST
_REQUESTOP.fields_by_name['request_put'].message_type = _PUTREQUEST
_REQUESTOP.fields_by_name['request_delete_range'].message_type = _DELETERANGEREQUEST
_REQUESTOP.fields_by_name['request_txn'].message_type = _TXNREQUEST
_REQUESTOP.oneofs_by_name['request'].fields.append(
  _REQUESTOP.fields_by_name['request_range'])
_REQUESTOP.fields_by_name['request_range'].containing_oneof = _REQUESTOP.oneofs_by_name['request']
_REQUESTOP.oneofs_by_name['request'].fields.append(
  _REQUESTOP.fields_by_name['request_put'])
_REQUESTOP.fields_by_name['request_put'].containing_oneof = _REQUESTOP.oneofs_by_name['request']
_REQUESTOP.oneofs_by_name['request'].fields.append(
  _REQUESTOP.fields_by_name['request_delete_range'])
_REQUESTOP.fields_by_name['request_delete_range'].containing_oneof = _REQUESTOP.oneofs_by_name['request']
_REQUESTOP.oneofs_by_name['request'].fields.append(
  _REQUESTOP.fields_by_name['request_txn'])
_REQUESTOP.fields_by_name['request_txn'].containing_oneof = _REQUESTOP.oneofs_by_name['request']
_RESPONSEOP.fields_by_name['response_range'].message_type = _RANGERESPONSE
_RESPONSEOP.fields_by_name['response_put'].message_type = _PUTRESPONSE
_RESPONSEOP.fields_by_name['response_delete_range'].message_type = _DELETERANGERESPONSE
_RESPONSEOP.fields_by_name['response_txn'].message_type = _TXNRESPONSE
_RESPONSEOP.oneofs_by_name['response'].fields.append(
  _RESPONSEOP.fields_by_name['response_range'])
_RESPONSEOP.fields_by_name['response_range'].containing_oneof = _RESPONSEOP.oneofs_by_name['response']
_RESPONSEOP.oneofs_by_name['response'].fields.append(
  _RESPONSEOP.fields_by_name['response_put'])
_RESPONSEOP.fields_by_name['response_put'].containing_oneof = _RESPONSEOP.oneofs_by_name['response']
_RESPONSEOP.oneofs_by_name['response'].fields.append(
  _RESPONSEOP.fields_by_name['response_delete_range'])
_RESPONSEOP.fields_by_name['response_delete_range'].containing_oneof = _RESPONSEOP.oneofs_by_name['response']
_RESPONSEOP.oneofs_by_name['response'].fields.append(
  _RESPONSEOP.fields_by_name['response_txn'])
_RESPONSEOP.fields_by_name['response_txn'].containing_oneof = _RESPONSEOP.oneofs_by_name['response']
_COMPARE.fields_by_name['result'].enum_type = _COMPARE_COMPARERESULT
_COMPARE.fields_by_name['target'].enum_type = _COMPARE_COMPARETARGET
_COMPARE_COMPARERESULT.containing_type = _COMPARE
_COMPARE_COMPARETARGET.containing_type = _COMPARE
_COMPARE.oneofs_by_name['target_union'].fields.append(
  _COMPARE.fields_by_name['version'])
_COMPARE.fields_by_name['version'].containing_oneof = _COMPARE.oneofs_by_name['target_union']
_COMPARE.oneofs_by_name['target_union'].fields.append(
  _COMPARE.fields_by_name['create_revision'])
_COMPARE.fields_by_name['create_revision'].containing_oneof = _COMPARE.oneofs_by_name['target_union']
_COMPARE.oneofs_by_name['target_union'].fields.append(
  _COMPARE.fields_by_name['mod_revision'])
_COMPARE.fields_by_name['mod_revision'].containing_oneof = _COMPARE.oneofs_by_name['target_union']
_COMPARE.oneofs_by_name['target_union'].fields.append(
  _COMPARE.fields_by_name['value'])
_COMPARE.fields_by_name['value'].containing_oneof = _COMPARE.oneofs_by_name['target_union']
_COMPARE.oneofs_by_name['target_union'].fields.append(
  _COMPARE.fields_by_name['lease'])
_COMPARE.fields_by_name['lease'].containing_oneof = _COMPARE.oneofs_by_name['target_union']
_TXNREQUEST.fields_by_name['compare'].message_type = _COMPARE
_TXNREQUEST.fields_by_name['success'].message_type = _REQUESTOP
_TXNREQUEST.fields_by_name['failure'].message_type = _REQUESTOP
_TXNRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_TXNRESPONSE.fields_by_name['responses'].message_type = _RESPONSEOP
_COMPACTIONRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_HASHKVRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_HASHRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_SNAPSHOTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_WATCHREQUEST.fields_by_name['create_request'].message_type = _WATCHCREATEREQUEST
_WATCHREQUEST.fields_by_name['cancel_request'].message_type = _WATCHCANCELREQUEST
_WATCHREQUEST.oneofs_by_name['request_union'].fields.append(
  _WATCHREQUEST.fields_by_name['create_request'])
_WATCHREQUEST.fields_by_name['create_request'].containing_oneof = _WATCHREQUEST.oneofs_by_name['request_union']
_WATCHREQUEST.oneofs_by_name['request_union'].fields.append(
  _WATCHREQUEST.fields_by_name['cancel_request'])
_WATCHREQUEST.fields_by_name['cancel_request'].containing_oneof = _WATCHREQUEST.oneofs_by_name['request_union']
_WATCHCREATEREQUEST.fields_by_name['filters'].enum_type = _WATCHCREATEREQUEST_FILTERTYPE
_WATCHCREATEREQUEST_FILTERTYPE.containing_type = _WATCHCREATEREQUEST
_WATCHRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_WATCHRESPONSE.fields_by_name['events'].message_type = kv__pb2._EVENT
_LEASEGRANTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_LEASEREVOKERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_LEASEKEEPALIVERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_LEASETIMETOLIVERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_LEASELEASESRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_LEASELEASESRESPONSE.fields_by_name['leases'].message_type = _LEASESTATUS
_MEMBERADDRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_MEMBERADDRESPONSE.fields_by_name['member'].message_type = _MEMBER
_MEMBERADDRESPONSE.fields_by_name['members'].message_type = _MEMBER
_MEMBERREMOVERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_MEMBERREMOVERESPONSE.fields_by_name['members'].message_type = _MEMBER
_MEMBERUPDATERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_MEMBERUPDATERESPONSE.fields_by_name['members'].message_type = _MEMBER
_MEMBERLISTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_MEMBERLISTRESPONSE.fields_by_name['members'].message_type = _MEMBER
_DEFRAGMENTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_MOVELEADERRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_ALARMREQUEST.fields_by_name['action'].enum_type = _ALARMREQUEST_ALARMACTION
_ALARMREQUEST.fields_by_name['alarm'].enum_type = _ALARMTYPE
_ALARMREQUEST_ALARMACTION.containing_type = _ALARMREQUEST
_ALARMMEMBER.fields_by_name['alarm'].enum_type = _ALARMTYPE
_ALARMRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_ALARMRESPONSE.fields_by_name['alarms'].message_type = _ALARMMEMBER
_STATUSRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEGRANTPERMISSIONREQUEST.fields_by_name['perm'].message_type = auth__pb2._PERMISSION
_AUTHENABLERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHDISABLERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHENTICATERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERADDRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERGETRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERDELETERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERCHANGEPASSWORDRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERGRANTROLERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERREVOKEROLERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEADDRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEGETRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEGETRESPONSE.fields_by_name['perm'].message_type = auth__pb2._PERMISSION
_AUTHROLELISTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHUSERLISTRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEDELETERESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEGRANTPERMISSIONRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
_AUTHROLEREVOKEPERMISSIONRESPONSE.fields_by_name['header'].message_type = _RESPONSEHEADER
DESCRIPTOR.message_types_by_name['ResponseHeader'] = _RESPONSEHEADER
DESCRIPTOR.message_types_by_name['RangeRequest'] = _RANGEREQUEST
DESCRIPTOR.message_types_by_name['RangeResponse'] = _RANGERESPONSE
DESCRIPTOR.message_types_by_name['PutRequest'] = _PUTREQUEST
DESCRIPTOR.message_types_by_name['PutResponse'] = _PUTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteRangeRequest'] = _DELETERANGEREQUEST
DESCRIPTOR.message_types_by_name['DeleteRangeResponse'] = _DELETERANGERESPONSE
DESCRIPTOR.message_types_by_name['RequestOp'] = _REQUESTOP
DESCRIPTOR.message_types_by_name['ResponseOp'] = _RESPONSEOP
DESCRIPTOR.message_types_by_name['Compare'] = _COMPARE
DESCRIPTOR.message_types_by_name['TxnRequest'] = _TXNREQUEST
DESCRIPTOR.message_types_by_name['TxnResponse'] = _TXNRESPONSE
DESCRIPTOR.message_types_by_name['CompactionRequest'] = _COMPACTIONREQUEST
DESCRIPTOR.message_types_by_name['CompactionResponse'] = _COMPACTIONRESPONSE
DESCRIPTOR.message_types_by_name['HashRequest'] = _HASHREQUEST
DESCRIPTOR.message_types_by_name['HashKVRequest'] = _HASHKVREQUEST
DESCRIPTOR.message_types_by_name['HashKVResponse'] = _HASHKVRESPONSE
DESCRIPTOR.message_types_by_name['HashResponse'] = _HASHRESPONSE
DESCRIPTOR.message_types_by_name['SnapshotRequest'] = _SNAPSHOTREQUEST
DESCRIPTOR.message_types_by_name['SnapshotResponse'] = _SNAPSHOTRESPONSE
DESCRIPTOR.message_types_by_name['WatchRequest'] = _WATCHREQUEST
DESCRIPTOR.message_types_by_name['WatchCreateRequest'] = _WATCHCREATEREQUEST
DESCRIPTOR.message_types_by_name['WatchCancelRequest'] = _WATCHCANCELREQUEST
DESCRIPTOR.message_types_by_name['WatchResponse'] = _WATCHRESPONSE
DESCRIPTOR.message_types_by_name['LeaseGrantRequest'] = _LEASEGRANTREQUEST
DESCRIPTOR.message_types_by_name['LeaseGrantResponse'] = _LEASEGRANTRESPONSE
DESCRIPTOR.message_types_by_name['LeaseRevokeRequest'] = _LEASEREVOKEREQUEST
DESCRIPTOR.message_types_by_name['LeaseRevokeResponse'] = _LEASEREVOKERESPONSE
DESCRIPTOR.message_types_by_name['LeaseKeepAliveRequest'] = _LEASEKEEPALIVEREQUEST
DESCRIPTOR.message_types_by_name['LeaseKeepAliveResponse'] = _LEASEKEEPALIVERESPONSE
DESCRIPTOR.message_types_by_name['LeaseTimeToLiveRequest'] = _LEASETIMETOLIVEREQUEST
DESCRIPTOR.message_types_by_name['LeaseTimeToLiveResponse'] = _LEASETIMETOLIVERESPONSE
DESCRIPTOR.message_types_by_name['LeaseLeasesRequest'] = _LEASELEASESREQUEST
DESCRIPTOR.message_types_by_name['LeaseStatus'] = _LEASESTATUS
DESCRIPTOR.message_types_by_name['LeaseLeasesResponse'] = _LEASELEASESRESPONSE
DESCRIPTOR.message_types_by_name['Member'] = _MEMBER
DESCRIPTOR.message_types_by_name['MemberAddRequest'] = _MEMBERADDREQUEST
DESCRIPTOR.message_types_by_name['MemberAddResponse'] = _MEMBERADDRESPONSE
DESCRIPTOR.message_types_by_name['MemberRemoveRequest'] = _MEMBERREMOVEREQUEST
DESCRIPTOR.message_types_by_name['MemberRemoveResponse'] = _MEMBERREMOVERESPONSE
DESCRIPTOR.message_types_by_name['MemberUpdateRequest'] = _MEMBERUPDATEREQUEST
DESCRIPTOR.message_types_by_name['MemberUpdateResponse'] = _MEMBERUPDATERESPONSE
DESCRIPTOR.message_types_by_name['MemberListRequest'] = _MEMBERLISTREQUEST
DESCRIPTOR.message_types_by_name['MemberListResponse'] = _MEMBERLISTRESPONSE
DESCRIPTOR.message_types_by_name['DefragmentRequest'] = _DEFRAGMENTREQUEST
DESCRIPTOR.message_types_by_name['DefragmentResponse'] = _DEFRAGMENTRESPONSE
DESCRIPTOR.message_types_by_name['MoveLeaderRequest'] = _MOVELEADERREQUEST
DESCRIPTOR.message_types_by_name['MoveLeaderResponse'] = _MOVELEADERRESPONSE
DESCRIPTOR.message_types_by_name['AlarmRequest'] = _ALARMREQUEST
DESCRIPTOR.message_types_by_name['AlarmMember'] = _ALARMMEMBER
DESCRIPTOR.message_types_by_name['AlarmResponse'] = _ALARMRESPONSE
DESCRIPTOR.message_types_by_name['StatusRequest'] = _STATUSREQUEST
DESCRIPTOR.message_types_by_name['StatusResponse'] = _STATUSRESPONSE
DESCRIPTOR.message_types_by_name['AuthEnableRequest'] = _AUTHENABLEREQUEST
DESCRIPTOR.message_types_by_name['AuthDisableRequest'] = _AUTHDISABLEREQUEST
DESCRIPTOR.message_types_by_name['AuthenticateRequest'] = _AUTHENTICATEREQUEST
DESCRIPTOR.message_types_by_name['AuthUserAddRequest'] = _AUTHUSERADDREQUEST
DESCRIPTOR.message_types_by_name['AuthUserGetRequest'] = _AUTHUSERGETREQUEST
DESCRIPTOR.message_types_by_name['AuthUserDeleteRequest'] = _AUTHUSERDELETEREQUEST
DESCRIPTOR.message_types_by_name['AuthUserChangePasswordRequest'] = _AUTHUSERCHANGEPASSWORDREQUEST
DESCRIPTOR.message_types_by_name['AuthUserGrantRoleRequest'] = _AUTHUSERGRANTROLEREQUEST
DESCRIPTOR.message_types_by_name['AuthUserRevokeRoleRequest'] = _AUTHUSERREVOKEROLEREQUEST
DESCRIPTOR.message_types_by_name['AuthRoleAddRequest'] = _AUTHROLEADDREQUEST
DESCRIPTOR.message_types_by_name['AuthRoleGetRequest'] = _AUTHROLEGETREQUEST
DESCRIPTOR.message_types_by_name['AuthUserListRequest'] = _AUTHUSERLISTREQUEST
DESCRIPTOR.message_types_by_name['AuthRoleListRequest'] = _AUTHROLELISTREQUEST
DESCRIPTOR.message_types_by_name['AuthRoleDeleteRequest'] = _AUTHROLEDELETEREQUEST
DESCRIPTOR.message_types_by_name['AuthRoleGrantPermissionRequest'] = _AUTHROLEGRANTPERMISSIONREQUEST
DESCRIPTOR.message_types_by_name['AuthRoleRevokePermissionRequest'] = _AUTHROLEREVOKEPERMISSIONREQUEST
DESCRIPTOR.message_types_by_name['AuthEnableResponse'] = _AUTHENABLERESPONSE
DESCRIPTOR.message_types_by_name['AuthDisableResponse'] = _AUTHDISABLERESPONSE
DESCRIPTOR.message_types_by_name['AuthenticateResponse'] = _AUTHENTICATERESPONSE
DESCRIPTOR.message_types_by_name['AuthUserAddResponse'] = _AUTHUSERADDRESPONSE
DESCRIPTOR.message_types_by_name['AuthUserGetResponse'] = _AUTHUSERGETRESPONSE
DESCRIPTOR.message_types_by_name['AuthUserDeleteResponse'] = _AUTHUSERDELETERESPONSE
DESCRIPTOR.message_types_by_name['AuthUserChangePasswordResponse'] = _AUTHUSERCHANGEPASSWORDRESPONSE
DESCRIPTOR.message_types_by_name['AuthUserGrantRoleResponse'] = _AUTHUSERGRANTROLERESPONSE
DESCRIPTOR.message_types_by_name['AuthUserRevokeRoleResponse'] = _AUTHUSERREVOKEROLERESPONSE
DESCRIPTOR.message_types_by_name['AuthRoleAddResponse'] = _AUTHROLEADDRESPONSE
DESCRIPTOR.message_types_by_name['AuthRoleGetResponse'] = _AUTHROLEGETRESPONSE
DESCRIPTOR.message_types_by_name['AuthRoleListResponse'] = _AUTHROLELISTRESPONSE
DESCRIPTOR.message_types_by_name['AuthUserListResponse'] = _AUTHUSERLISTRESPONSE
DESCRIPTOR.message_types_by_name['AuthRoleDeleteResponse'] = _AUTHROLEDELETERESPONSE
DESCRIPTOR.message_types_by_name['AuthRoleGrantPermissionResponse'] = _AUTHROLEGRANTPERMISSIONRESPONSE
DESCRIPTOR.message_types_by_name['AuthRoleRevokePermissionResponse'] = _AUTHROLEREVOKEPERMISSIONRESPONSE
DESCRIPTOR.enum_types_by_name['AlarmType'] = _ALARMTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ResponseHeader = _reflection.GeneratedProtocolMessageType('ResponseHeader', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSEHEADER,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.ResponseHeader)
  ))
_sym_db.RegisterMessage(ResponseHeader)

RangeRequest = _reflection.GeneratedProtocolMessageType('RangeRequest', (_message.Message,), dict(
  DESCRIPTOR = _RANGEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.RangeRequest)
  ))
_sym_db.RegisterMessage(RangeRequest)

RangeResponse = _reflection.GeneratedProtocolMessageType('RangeResponse', (_message.Message,), dict(
  DESCRIPTOR = _RANGERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.RangeResponse)
  ))
_sym_db.RegisterMessage(RangeResponse)

PutRequest = _reflection.GeneratedProtocolMessageType('PutRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.PutRequest)
  ))
_sym_db.RegisterMessage(PutRequest)

PutResponse = _reflection.GeneratedProtocolMessageType('PutResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.PutResponse)
  ))
_sym_db.RegisterMessage(PutResponse)

DeleteRangeRequest = _reflection.GeneratedProtocolMessageType('DeleteRangeRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERANGEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.DeleteRangeRequest)
  ))
_sym_db.RegisterMessage(DeleteRangeRequest)

DeleteRangeResponse = _reflection.GeneratedProtocolMessageType('DeleteRangeResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETERANGERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.DeleteRangeResponse)
  ))
_sym_db.RegisterMessage(DeleteRangeResponse)

RequestOp = _reflection.GeneratedProtocolMessageType('RequestOp', (_message.Message,), dict(
  DESCRIPTOR = _REQUESTOP,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.RequestOp)
  ))
_sym_db.RegisterMessage(RequestOp)

ResponseOp = _reflection.GeneratedProtocolMessageType('ResponseOp', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSEOP,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.ResponseOp)
  ))
_sym_db.RegisterMessage(ResponseOp)

Compare = _reflection.GeneratedProtocolMessageType('Compare', (_message.Message,), dict(
  DESCRIPTOR = _COMPARE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.Compare)
  ))
_sym_db.RegisterMessage(Compare)

TxnRequest = _reflection.GeneratedProtocolMessageType('TxnRequest', (_message.Message,), dict(
  DESCRIPTOR = _TXNREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.TxnRequest)
  ))
_sym_db.RegisterMessage(TxnRequest)

TxnResponse = _reflection.GeneratedProtocolMessageType('TxnResponse', (_message.Message,), dict(
  DESCRIPTOR = _TXNRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.TxnResponse)
  ))
_sym_db.RegisterMessage(TxnResponse)

CompactionRequest = _reflection.GeneratedProtocolMessageType('CompactionRequest', (_message.Message,), dict(
  DESCRIPTOR = _COMPACTIONREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.CompactionRequest)
  ))
_sym_db.RegisterMessage(CompactionRequest)

CompactionResponse = _reflection.GeneratedProtocolMessageType('CompactionResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMPACTIONRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.CompactionResponse)
  ))
_sym_db.RegisterMessage(CompactionResponse)

HashRequest = _reflection.GeneratedProtocolMessageType('HashRequest', (_message.Message,), dict(
  DESCRIPTOR = _HASHREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.HashRequest)
  ))
_sym_db.RegisterMessage(HashRequest)

HashKVRequest = _reflection.GeneratedProtocolMessageType('HashKVRequest', (_message.Message,), dict(
  DESCRIPTOR = _HASHKVREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.HashKVRequest)
  ))
_sym_db.RegisterMessage(HashKVRequest)

HashKVResponse = _reflection.GeneratedProtocolMessageType('HashKVResponse', (_message.Message,), dict(
  DESCRIPTOR = _HASHKVRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.HashKVResponse)
  ))
_sym_db.RegisterMessage(HashKVResponse)

HashResponse = _reflection.GeneratedProtocolMessageType('HashResponse', (_message.Message,), dict(
  DESCRIPTOR = _HASHRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.HashResponse)
  ))
_sym_db.RegisterMessage(HashResponse)

SnapshotRequest = _reflection.GeneratedProtocolMessageType('SnapshotRequest', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.SnapshotRequest)
  ))
_sym_db.RegisterMessage(SnapshotRequest)

SnapshotResponse = _reflection.GeneratedProtocolMessageType('SnapshotResponse', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.SnapshotResponse)
  ))
_sym_db.RegisterMessage(SnapshotResponse)

WatchRequest = _reflection.GeneratedProtocolMessageType('WatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _WATCHREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.WatchRequest)
  ))
_sym_db.RegisterMessage(WatchRequest)

WatchCreateRequest = _reflection.GeneratedProtocolMessageType('WatchCreateRequest', (_message.Message,), dict(
  DESCRIPTOR = _WATCHCREATEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.WatchCreateRequest)
  ))
_sym_db.RegisterMessage(WatchCreateRequest)

WatchCancelRequest = _reflection.GeneratedProtocolMessageType('WatchCancelRequest', (_message.Message,), dict(
  DESCRIPTOR = _WATCHCANCELREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.WatchCancelRequest)
  ))
_sym_db.RegisterMessage(WatchCancelRequest)

WatchResponse = _reflection.GeneratedProtocolMessageType('WatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _WATCHRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.WatchResponse)
  ))
_sym_db.RegisterMessage(WatchResponse)

LeaseGrantRequest = _reflection.GeneratedProtocolMessageType('LeaseGrantRequest', (_message.Message,), dict(
  DESCRIPTOR = _LEASEGRANTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseGrantRequest)
  ))
_sym_db.RegisterMessage(LeaseGrantRequest)

LeaseGrantResponse = _reflection.GeneratedProtocolMessageType('LeaseGrantResponse', (_message.Message,), dict(
  DESCRIPTOR = _LEASEGRANTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseGrantResponse)
  ))
_sym_db.RegisterMessage(LeaseGrantResponse)

LeaseRevokeRequest = _reflection.GeneratedProtocolMessageType('LeaseRevokeRequest', (_message.Message,), dict(
  DESCRIPTOR = _LEASEREVOKEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseRevokeRequest)
  ))
_sym_db.RegisterMessage(LeaseRevokeRequest)

LeaseRevokeResponse = _reflection.GeneratedProtocolMessageType('LeaseRevokeResponse', (_message.Message,), dict(
  DESCRIPTOR = _LEASEREVOKERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseRevokeResponse)
  ))
_sym_db.RegisterMessage(LeaseRevokeResponse)

LeaseKeepAliveRequest = _reflection.GeneratedProtocolMessageType('LeaseKeepAliveRequest', (_message.Message,), dict(
  DESCRIPTOR = _LEASEKEEPALIVEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseKeepAliveRequest)
  ))
_sym_db.RegisterMessage(LeaseKeepAliveRequest)

LeaseKeepAliveResponse = _reflection.GeneratedProtocolMessageType('LeaseKeepAliveResponse', (_message.Message,), dict(
  DESCRIPTOR = _LEASEKEEPALIVERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseKeepAliveResponse)
  ))
_sym_db.RegisterMessage(LeaseKeepAliveResponse)

LeaseTimeToLiveRequest = _reflection.GeneratedProtocolMessageType('LeaseTimeToLiveRequest', (_message.Message,), dict(
  DESCRIPTOR = _LEASETIMETOLIVEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseTimeToLiveRequest)
  ))
_sym_db.RegisterMessage(LeaseTimeToLiveRequest)

LeaseTimeToLiveResponse = _reflection.GeneratedProtocolMessageType('LeaseTimeToLiveResponse', (_message.Message,), dict(
  DESCRIPTOR = _LEASETIMETOLIVERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseTimeToLiveResponse)
  ))
_sym_db.RegisterMessage(LeaseTimeToLiveResponse)

LeaseLeasesRequest = _reflection.GeneratedProtocolMessageType('LeaseLeasesRequest', (_message.Message,), dict(
  DESCRIPTOR = _LEASELEASESREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseLeasesRequest)
  ))
_sym_db.RegisterMessage(LeaseLeasesRequest)

LeaseStatus = _reflection.GeneratedProtocolMessageType('LeaseStatus', (_message.Message,), dict(
  DESCRIPTOR = _LEASESTATUS,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseStatus)
  ))
_sym_db.RegisterMessage(LeaseStatus)

LeaseLeasesResponse = _reflection.GeneratedProtocolMessageType('LeaseLeasesResponse', (_message.Message,), dict(
  DESCRIPTOR = _LEASELEASESRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.LeaseLeasesResponse)
  ))
_sym_db.RegisterMessage(LeaseLeasesResponse)

Member = _reflection.GeneratedProtocolMessageType('Member', (_message.Message,), dict(
  DESCRIPTOR = _MEMBER,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.Member)
  ))
_sym_db.RegisterMessage(Member)

MemberAddRequest = _reflection.GeneratedProtocolMessageType('MemberAddRequest', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERADDREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberAddRequest)
  ))
_sym_db.RegisterMessage(MemberAddRequest)

MemberAddResponse = _reflection.GeneratedProtocolMessageType('MemberAddResponse', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERADDRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberAddResponse)
  ))
_sym_db.RegisterMessage(MemberAddResponse)

MemberRemoveRequest = _reflection.GeneratedProtocolMessageType('MemberRemoveRequest', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERREMOVEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberRemoveRequest)
  ))
_sym_db.RegisterMessage(MemberRemoveRequest)

MemberRemoveResponse = _reflection.GeneratedProtocolMessageType('MemberRemoveResponse', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERREMOVERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberRemoveResponse)
  ))
_sym_db.RegisterMessage(MemberRemoveResponse)

MemberUpdateRequest = _reflection.GeneratedProtocolMessageType('MemberUpdateRequest', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERUPDATEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberUpdateRequest)
  ))
_sym_db.RegisterMessage(MemberUpdateRequest)

MemberUpdateResponse = _reflection.GeneratedProtocolMessageType('MemberUpdateResponse', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERUPDATERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberUpdateResponse)
  ))
_sym_db.RegisterMessage(MemberUpdateResponse)

MemberListRequest = _reflection.GeneratedProtocolMessageType('MemberListRequest', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERLISTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberListRequest)
  ))
_sym_db.RegisterMessage(MemberListRequest)

MemberListResponse = _reflection.GeneratedProtocolMessageType('MemberListResponse', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERLISTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MemberListResponse)
  ))
_sym_db.RegisterMessage(MemberListResponse)

DefragmentRequest = _reflection.GeneratedProtocolMessageType('DefragmentRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEFRAGMENTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.DefragmentRequest)
  ))
_sym_db.RegisterMessage(DefragmentRequest)

DefragmentResponse = _reflection.GeneratedProtocolMessageType('DefragmentResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFRAGMENTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.DefragmentResponse)
  ))
_sym_db.RegisterMessage(DefragmentResponse)

MoveLeaderRequest = _reflection.GeneratedProtocolMessageType('MoveLeaderRequest', (_message.Message,), dict(
  DESCRIPTOR = _MOVELEADERREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MoveLeaderRequest)
  ))
_sym_db.RegisterMessage(MoveLeaderRequest)

MoveLeaderResponse = _reflection.GeneratedProtocolMessageType('MoveLeaderResponse', (_message.Message,), dict(
  DESCRIPTOR = _MOVELEADERRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.MoveLeaderResponse)
  ))
_sym_db.RegisterMessage(MoveLeaderResponse)

AlarmRequest = _reflection.GeneratedProtocolMessageType('AlarmRequest', (_message.Message,), dict(
  DESCRIPTOR = _ALARMREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AlarmRequest)
  ))
_sym_db.RegisterMessage(AlarmRequest)

AlarmMember = _reflection.GeneratedProtocolMessageType('AlarmMember', (_message.Message,), dict(
  DESCRIPTOR = _ALARMMEMBER,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AlarmMember)
  ))
_sym_db.RegisterMessage(AlarmMember)

AlarmResponse = _reflection.GeneratedProtocolMessageType('AlarmResponse', (_message.Message,), dict(
  DESCRIPTOR = _ALARMRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AlarmResponse)
  ))
_sym_db.RegisterMessage(AlarmResponse)

StatusRequest = _reflection.GeneratedProtocolMessageType('StatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _STATUSREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.StatusRequest)
  ))
_sym_db.RegisterMessage(StatusRequest)

StatusResponse = _reflection.GeneratedProtocolMessageType('StatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _STATUSRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.StatusResponse)
  ))
_sym_db.RegisterMessage(StatusResponse)

AuthEnableRequest = _reflection.GeneratedProtocolMessageType('AuthEnableRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHENABLEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthEnableRequest)
  ))
_sym_db.RegisterMessage(AuthEnableRequest)

AuthDisableRequest = _reflection.GeneratedProtocolMessageType('AuthDisableRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHDISABLEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthDisableRequest)
  ))
_sym_db.RegisterMessage(AuthDisableRequest)

AuthenticateRequest = _reflection.GeneratedProtocolMessageType('AuthenticateRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHENTICATEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthenticateRequest)
  ))
_sym_db.RegisterMessage(AuthenticateRequest)

AuthUserAddRequest = _reflection.GeneratedProtocolMessageType('AuthUserAddRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERADDREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserAddRequest)
  ))
_sym_db.RegisterMessage(AuthUserAddRequest)

AuthUserGetRequest = _reflection.GeneratedProtocolMessageType('AuthUserGetRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERGETREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserGetRequest)
  ))
_sym_db.RegisterMessage(AuthUserGetRequest)

AuthUserDeleteRequest = _reflection.GeneratedProtocolMessageType('AuthUserDeleteRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERDELETEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserDeleteRequest)
  ))
_sym_db.RegisterMessage(AuthUserDeleteRequest)

AuthUserChangePasswordRequest = _reflection.GeneratedProtocolMessageType('AuthUserChangePasswordRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERCHANGEPASSWORDREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserChangePasswordRequest)
  ))
_sym_db.RegisterMessage(AuthUserChangePasswordRequest)

AuthUserGrantRoleRequest = _reflection.GeneratedProtocolMessageType('AuthUserGrantRoleRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERGRANTROLEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserGrantRoleRequest)
  ))
_sym_db.RegisterMessage(AuthUserGrantRoleRequest)

AuthUserRevokeRoleRequest = _reflection.GeneratedProtocolMessageType('AuthUserRevokeRoleRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERREVOKEROLEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserRevokeRoleRequest)
  ))
_sym_db.RegisterMessage(AuthUserRevokeRoleRequest)

AuthRoleAddRequest = _reflection.GeneratedProtocolMessageType('AuthRoleAddRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEADDREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleAddRequest)
  ))
_sym_db.RegisterMessage(AuthRoleAddRequest)

AuthRoleGetRequest = _reflection.GeneratedProtocolMessageType('AuthRoleGetRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEGETREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleGetRequest)
  ))
_sym_db.RegisterMessage(AuthRoleGetRequest)

AuthUserListRequest = _reflection.GeneratedProtocolMessageType('AuthUserListRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERLISTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserListRequest)
  ))
_sym_db.RegisterMessage(AuthUserListRequest)

AuthRoleListRequest = _reflection.GeneratedProtocolMessageType('AuthRoleListRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLELISTREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleListRequest)
  ))
_sym_db.RegisterMessage(AuthRoleListRequest)

AuthRoleDeleteRequest = _reflection.GeneratedProtocolMessageType('AuthRoleDeleteRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEDELETEREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleDeleteRequest)
  ))
_sym_db.RegisterMessage(AuthRoleDeleteRequest)

AuthRoleGrantPermissionRequest = _reflection.GeneratedProtocolMessageType('AuthRoleGrantPermissionRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEGRANTPERMISSIONREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleGrantPermissionRequest)
  ))
_sym_db.RegisterMessage(AuthRoleGrantPermissionRequest)

AuthRoleRevokePermissionRequest = _reflection.GeneratedProtocolMessageType('AuthRoleRevokePermissionRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEREVOKEPERMISSIONREQUEST,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleRevokePermissionRequest)
  ))
_sym_db.RegisterMessage(AuthRoleRevokePermissionRequest)

AuthEnableResponse = _reflection.GeneratedProtocolMessageType('AuthEnableResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHENABLERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthEnableResponse)
  ))
_sym_db.RegisterMessage(AuthEnableResponse)

AuthDisableResponse = _reflection.GeneratedProtocolMessageType('AuthDisableResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHDISABLERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthDisableResponse)
  ))
_sym_db.RegisterMessage(AuthDisableResponse)

AuthenticateResponse = _reflection.GeneratedProtocolMessageType('AuthenticateResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHENTICATERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthenticateResponse)
  ))
_sym_db.RegisterMessage(AuthenticateResponse)

AuthUserAddResponse = _reflection.GeneratedProtocolMessageType('AuthUserAddResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERADDRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserAddResponse)
  ))
_sym_db.RegisterMessage(AuthUserAddResponse)

AuthUserGetResponse = _reflection.GeneratedProtocolMessageType('AuthUserGetResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERGETRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserGetResponse)
  ))
_sym_db.RegisterMessage(AuthUserGetResponse)

AuthUserDeleteResponse = _reflection.GeneratedProtocolMessageType('AuthUserDeleteResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERDELETERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserDeleteResponse)
  ))
_sym_db.RegisterMessage(AuthUserDeleteResponse)

AuthUserChangePasswordResponse = _reflection.GeneratedProtocolMessageType('AuthUserChangePasswordResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERCHANGEPASSWORDRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserChangePasswordResponse)
  ))
_sym_db.RegisterMessage(AuthUserChangePasswordResponse)

AuthUserGrantRoleResponse = _reflection.GeneratedProtocolMessageType('AuthUserGrantRoleResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERGRANTROLERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserGrantRoleResponse)
  ))
_sym_db.RegisterMessage(AuthUserGrantRoleResponse)

AuthUserRevokeRoleResponse = _reflection.GeneratedProtocolMessageType('AuthUserRevokeRoleResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERREVOKEROLERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserRevokeRoleResponse)
  ))
_sym_db.RegisterMessage(AuthUserRevokeRoleResponse)

AuthRoleAddResponse = _reflection.GeneratedProtocolMessageType('AuthRoleAddResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEADDRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleAddResponse)
  ))
_sym_db.RegisterMessage(AuthRoleAddResponse)

AuthRoleGetResponse = _reflection.GeneratedProtocolMessageType('AuthRoleGetResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEGETRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleGetResponse)
  ))
_sym_db.RegisterMessage(AuthRoleGetResponse)

AuthRoleListResponse = _reflection.GeneratedProtocolMessageType('AuthRoleListResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLELISTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleListResponse)
  ))
_sym_db.RegisterMessage(AuthRoleListResponse)

AuthUserListResponse = _reflection.GeneratedProtocolMessageType('AuthUserListResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHUSERLISTRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthUserListResponse)
  ))
_sym_db.RegisterMessage(AuthUserListResponse)

AuthRoleDeleteResponse = _reflection.GeneratedProtocolMessageType('AuthRoleDeleteResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEDELETERESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleDeleteResponse)
  ))
_sym_db.RegisterMessage(AuthRoleDeleteResponse)

AuthRoleGrantPermissionResponse = _reflection.GeneratedProtocolMessageType('AuthRoleGrantPermissionResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEGRANTPERMISSIONRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleGrantPermissionResponse)
  ))
_sym_db.RegisterMessage(AuthRoleGrantPermissionResponse)

AuthRoleRevokePermissionResponse = _reflection.GeneratedProtocolMessageType('AuthRoleRevokePermissionResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTHROLEREVOKEPERMISSIONRESPONSE,
  __module__ = 'rpc_pb2'
  # @@protoc_insertion_point(class_scope:etcdserverpb.AuthRoleRevokePermissionResponse)
  ))
_sym_db.RegisterMessage(AuthRoleRevokePermissionResponse)



_KV = _descriptor.ServiceDescriptor(
  name='KV',
  full_name='etcdserverpb.KV',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7605,
  serialized_end=7967,
  methods=[
  _descriptor.MethodDescriptor(
    name='Range',
    full_name='etcdserverpb.KV.Range',
    index=0,
    containing_service=None,
    input_type=_RANGEREQUEST,
    output_type=_RANGERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Put',
    full_name='etcdserverpb.KV.Put',
    index=1,
    containing_service=None,
    input_type=_PUTREQUEST,
    output_type=_PUTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteRange',
    full_name='etcdserverpb.KV.DeleteRange',
    index=2,
    containing_service=None,
    input_type=_DELETERANGEREQUEST,
    output_type=_DELETERANGERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Txn',
    full_name='etcdserverpb.KV.Txn',
    index=3,
    containing_service=None,
    input_type=_TXNREQUEST,
    output_type=_TXNRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Compact',
    full_name='etcdserverpb.KV.Compact',
    index=4,
    containing_service=None,
    input_type=_COMPACTIONREQUEST,
    output_type=_COMPACTIONRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_KV)

DESCRIPTOR.services_by_name['KV'] = _KV


_WATCH = _descriptor.ServiceDescriptor(
  name='Watch',
  full_name='etcdserverpb.Watch',
  file=DESCRIPTOR,
  index=1,
  serialized_options=None,
  serialized_start=7969,
  serialized_end=8048,
  methods=[
  _descriptor.MethodDescriptor(
    name='Watch',
    full_name='etcdserverpb.Watch.Watch',
    index=0,
    containing_service=None,
    input_type=_WATCHREQUEST,
    output_type=_WATCHRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_WATCH)

DESCRIPTOR.services_by_name['Watch'] = _WATCH


_LEASE = _descriptor.ServiceDescriptor(
  name='Lease',
  full_name='etcdserverpb.Lease',
  file=DESCRIPTOR,
  index=2,
  serialized_options=None,
  serialized_start=8051,
  serialized_end=8510,
  methods=[
  _descriptor.MethodDescriptor(
    name='LeaseGrant',
    full_name='etcdserverpb.Lease.LeaseGrant',
    index=0,
    containing_service=None,
    input_type=_LEASEGRANTREQUEST,
    output_type=_LEASEGRANTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='LeaseRevoke',
    full_name='etcdserverpb.Lease.LeaseRevoke',
    index=1,
    containing_service=None,
    input_type=_LEASEREVOKEREQUEST,
    output_type=_LEASEREVOKERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='LeaseKeepAlive',
    full_name='etcdserverpb.Lease.LeaseKeepAlive',
    index=2,
    containing_service=None,
    input_type=_LEASEKEEPALIVEREQUEST,
    output_type=_LEASEKEEPALIVERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='LeaseTimeToLive',
    full_name='etcdserverpb.Lease.LeaseTimeToLive',
    index=3,
    containing_service=None,
    input_type=_LEASETIMETOLIVEREQUEST,
    output_type=_LEASETIMETOLIVERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='LeaseLeases',
    full_name='etcdserverpb.Lease.LeaseLeases',
    index=4,
    containing_service=None,
    input_type=_LEASELEASESREQUEST,
    output_type=_LEASELEASESRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_LEASE)

DESCRIPTOR.services_by_name['Lease'] = _LEASE


_CLUSTER = _descriptor.ServiceDescriptor(
  name='Cluster',
  full_name='etcdserverpb.Cluster',
  file=DESCRIPTOR,
  index=3,
  serialized_options=None,
  serialized_start=8513,
  serialized_end=8863,
  methods=[
  _descriptor.MethodDescriptor(
    name='MemberAdd',
    full_name='etcdserverpb.Cluster.MemberAdd',
    index=0,
    containing_service=None,
    input_type=_MEMBERADDREQUEST,
    output_type=_MEMBERADDRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='MemberRemove',
    full_name='etcdserverpb.Cluster.MemberRemove',
    index=1,
    containing_service=None,
    input_type=_MEMBERREMOVEREQUEST,
    output_type=_MEMBERREMOVERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='MemberUpdate',
    full_name='etcdserverpb.Cluster.MemberUpdate',
    index=2,
    containing_service=None,
    input_type=_MEMBERUPDATEREQUEST,
    output_type=_MEMBERUPDATERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='MemberList',
    full_name='etcdserverpb.Cluster.MemberList',
    index=3,
    containing_service=None,
    input_type=_MEMBERLISTREQUEST,
    output_type=_MEMBERLISTRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_CLUSTER)

DESCRIPTOR.services_by_name['Cluster'] = _CLUSTER


_MAINTENANCE = _descriptor.ServiceDescriptor(
  name='Maintenance',
  full_name='etcdserverpb.Maintenance',
  file=DESCRIPTOR,
  index=4,
  serialized_options=None,
  serialized_start=8866,
  serialized_end=9399,
  methods=[
  _descriptor.MethodDescriptor(
    name='Alarm',
    full_name='etcdserverpb.Maintenance.Alarm',
    index=0,
    containing_service=None,
    input_type=_ALARMREQUEST,
    output_type=_ALARMRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Status',
    full_name='etcdserverpb.Maintenance.Status',
    index=1,
    containing_service=None,
    input_type=_STATUSREQUEST,
    output_type=_STATUSRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Defragment',
    full_name='etcdserverpb.Maintenance.Defragment',
    index=2,
    containing_service=None,
    input_type=_DEFRAGMENTREQUEST,
    output_type=_DEFRAGMENTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Hash',
    full_name='etcdserverpb.Maintenance.Hash',
    index=3,
    containing_service=None,
    input_type=_HASHREQUEST,
    output_type=_HASHRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='HashKV',
    full_name='etcdserverpb.Maintenance.HashKV',
    index=4,
    containing_service=None,
    input_type=_HASHKVREQUEST,
    output_type=_HASHKVRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Snapshot',
    full_name='etcdserverpb.Maintenance.Snapshot',
    index=5,
    containing_service=None,
    input_type=_SNAPSHOTREQUEST,
    output_type=_SNAPSHOTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='MoveLeader',
    full_name='etcdserverpb.Maintenance.MoveLeader',
    index=6,
    containing_service=None,
    input_type=_MOVELEADERREQUEST,
    output_type=_MOVELEADERRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_MAINTENANCE)

DESCRIPTOR.services_by_name['Maintenance'] = _MAINTENANCE


_AUTH = _descriptor.ServiceDescriptor(
  name='Auth',
  full_name='etcdserverpb.Auth',
  file=DESCRIPTOR,
  index=5,
  serialized_options=None,
  serialized_start=9402,
  serialized_end=10903,
  methods=[
  _descriptor.MethodDescriptor(
    name='AuthEnable',
    full_name='etcdserverpb.Auth.AuthEnable',
    index=0,
    containing_service=None,
    input_type=_AUTHENABLEREQUEST,
    output_type=_AUTHENABLERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='AuthDisable',
    full_name='etcdserverpb.Auth.AuthDisable',
    index=1,
    containing_service=None,
    input_type=_AUTHDISABLEREQUEST,
    output_type=_AUTHDISABLERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='Authenticate',
    full_name='etcdserverpb.Auth.Authenticate',
    index=2,
    containing_service=None,
    input_type=_AUTHENTICATEREQUEST,
    output_type=_AUTHENTICATERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserAdd',
    full_name='etcdserverpb.Auth.UserAdd',
    index=3,
    containing_service=None,
    input_type=_AUTHUSERADDREQUEST,
    output_type=_AUTHUSERADDRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserGet',
    full_name='etcdserverpb.Auth.UserGet',
    index=4,
    containing_service=None,
    input_type=_AUTHUSERGETREQUEST,
    output_type=_AUTHUSERGETRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserList',
    full_name='etcdserverpb.Auth.UserList',
    index=5,
    containing_service=None,
    input_type=_AUTHUSERLISTREQUEST,
    output_type=_AUTHUSERLISTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserDelete',
    full_name='etcdserverpb.Auth.UserDelete',
    index=6,
    containing_service=None,
    input_type=_AUTHUSERDELETEREQUEST,
    output_type=_AUTHUSERDELETERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserChangePassword',
    full_name='etcdserverpb.Auth.UserChangePassword',
    index=7,
    containing_service=None,
    input_type=_AUTHUSERCHANGEPASSWORDREQUEST,
    output_type=_AUTHUSERCHANGEPASSWORDRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserGrantRole',
    full_name='etcdserverpb.Auth.UserGrantRole',
    index=8,
    containing_service=None,
    input_type=_AUTHUSERGRANTROLEREQUEST,
    output_type=_AUTHUSERGRANTROLERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UserRevokeRole',
    full_name='etcdserverpb.Auth.UserRevokeRole',
    index=9,
    containing_service=None,
    input_type=_AUTHUSERREVOKEROLEREQUEST,
    output_type=_AUTHUSERREVOKEROLERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RoleAdd',
    full_name='etcdserverpb.Auth.RoleAdd',
    index=10,
    containing_service=None,
    input_type=_AUTHROLEADDREQUEST,
    output_type=_AUTHROLEADDRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RoleGet',
    full_name='etcdserverpb.Auth.RoleGet',
    index=11,
    containing_service=None,
    input_type=_AUTHROLEGETREQUEST,
    output_type=_AUTHROLEGETRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RoleList',
    full_name='etcdserverpb.Auth.RoleList',
    index=12,
    containing_service=None,
    input_type=_AUTHROLELISTREQUEST,
    output_type=_AUTHROLELISTRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RoleDelete',
    full_name='etcdserverpb.Auth.RoleDelete',
    index=13,
    containing_service=None,
    input_type=_AUTHROLEDELETEREQUEST,
    output_type=_AUTHROLEDELETERESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RoleGrantPermission',
    full_name='etcdserverpb.Auth.RoleGrantPermission',
    index=14,
    containing_service=None,
    input_type=_AUTHROLEGRANTPERMISSIONREQUEST,
    output_type=_AUTHROLEGRANTPERMISSIONRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='RoleRevokePermission',
    full_name='etcdserverpb.Auth.RoleRevokePermission',
    index=15,
    containing_service=None,
    input_type=_AUTHROLEREVOKEPERMISSIONREQUEST,
    output_type=_AUTHROLEREVOKEPERMISSIONRESPONSE,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_AUTH)

DESCRIPTOR.services_by_name['Auth'] = _AUTH

# @@protoc_insertion_point(module_scope)

#!/bin/bash
set -x

os=`uname -r`;
version=`git  log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10`;
argdate=`git show -s --date=format:'%Y-%m-%d-%H:%M:%S' --format=%cd`;
argtag=`git describe --abbrev=1  --tags`;
docker build --no-cache -t tianwen1:5000/hci_web:$argtag --build-arg ARG_VERSION=$version --build-arg ARG_TAG=$argtag --build-arg ARG_COMMIT_DATE=$argdate  --build-arg ARG_OS=$os .;
docker push  tianwen1:5000/hci_web:$argtag;
docker tag tianwen1:5000/hci_web:$argtag tianwen1:5000/hci_web:latest;
docker push tianwen1:5000/hci_web:latest;
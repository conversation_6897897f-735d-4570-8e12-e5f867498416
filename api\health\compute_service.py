'''
Created on Sep 6, 2022

@author: ct
'''
import requests
from api.openstack.client import Client
from dataclasses import dataclass
from dacite import from_dict

name = "计算服务状态"
weigth = 10

@dataclass
class ComputeService(object):
    binary : str
    host : str
    status : str
    state : str
    
def check():
    print("check method")
    client = Client()
    services = client.NovaClient.openstack_get_all_compute_services(client)
    data = []
    count = 0
    for service  in services:
        ins = from_dict(data_class=ComputeService, data=service)
        if ins.__dict__["state"] == "up":
            count = count + 1
        data.append(ins.__dict__)

    if count == len(services):
        res = "ok"
    elif count < len(services) and count > 0 :
        res = "warning"
    elif  count == 0 :
        res = "error"
            
    result = {
        "key": "compute_service",
        "name": name,
        "weigth": weigth,
        "result": res,    # ok (V), warning (!) ,error (X)
        "columns": [
            {
                "title":"服务",
                "key":"binary",
                "tooltip":True
            },
            {
                "title":"管理状态",
                "key":"status",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"运行状态",
                "key":"state",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"主机",
                "key":"host",
                "tooltip":True,
                "align":"center"
            }
        ],
        "data": data
    }
    return result





'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import uuid
import time

import settings
# from model.flavors import Flavor,FlavorDetail
from dataclasses import dataclass
from string import Template
import libvirt
import os
from xml.dom.minidom import Document
from api.libvirt.utils.utils import validate_data
from api.libvirt.utils.element import *
import xml.etree.ElementTree as ET
from api.libvirt.utils.vmxml.generate import *
from api.libvirt.utils.vmxml.domain_xml import Domain
from api.libvirt.utils.utils import capacity_to_bytes
from api.libvirt.utils.system_info import get_system_info


class VMClient:
    state_str = {
        libvirt.VIR_DOMAIN_NOSTATE:  'nostate',  # 无状态
        libvirt.VIR_DOMAIN_RUNNING: 'running',  # 运行中
        libvirt.VIR_DOMAIN_BLOCKED: 'blocked',  # 阻塞
        libvirt.VIR_DOMAIN_PAUSED: 'paused',  # 暂停
        libvirt.VIR_DOMAIN_SHUTDOWN: 'shutdown',  # 关机
        libvirt.VIR_DOMAIN_SHUTOFF: 'shutoff',  # 关机
        libvirt.VIR_DOMAIN_CRASHED: 'crashed',  # 崩溃
        libvirt.VIR_DOMAIN_PMSUSPENDED: 'suspended',  # 电源管理暂停
    }

    @validate_data(["vm_name", "memory_unit", "vcpu_unit", "disk", "interface"])
    def create_dom_vm(self, data):
        # 创建一个新的 DOM 文档对象
        doc = Document()

        # 创建根元素 <domain> 并添加属性 type='kvm'
        domain = doc.createElement('domain')
        domain.setAttribute('type', data.get('virtualization', 'qemu'))
        doc.appendChild(domain)

        # 创建 UUID
        uuid_elem = doc.createElement('uuid')
        vm_uuid = str(uuid.uuid4())
        uuid_elem.appendChild(doc.createTextNode(vm_uuid))
        domain.appendChild(uuid_elem)

        # 创建 <name> 元素
        vm_name = data.get('vm_name')
        name = doc.createElement('name')
        name.appendChild(doc.createTextNode(vm_name))
        domain.appendChild(name)

        # 创建 <memory> 元素
        memory_unit = data.get('memory_unit')
        memory_unit_type = data.get(
            'memory_unit_type', 'KiB')  # 从数据中获取单位类型,默认为 KiB
        memory = doc.createElement('memory')
        memory.setAttribute('unit', memory_unit_type)
        memory.appendChild(doc.createTextNode(str(memory_unit)))
        domain.appendChild(memory)

        # 添加 currentMemory 配置
        current_memory = doc.createElement('currentMemory')
        current_memory.setAttribute('unit', memory_unit_type)
        current_memory.appendChild(doc.createTextNode(str(memory_unit)))
        domain.appendChild(current_memory)

        # 创建 <vcpu> 元素
        vcpu_unit = data.get('vcpu_unit')
        vcpu = doc.createElement('vcpu')
        vcpu.appendChild(doc.createTextNode(str(vcpu_unit)))
        domain.appendChild(vcpu)


        # 添加事件处理配置
        on_poweroff = doc.createElement('on_poweroff')
        on_poweroff.appendChild(doc.createTextNode('destroy'))
        domain.appendChild(on_poweroff)

        on_reboot = doc.createElement('on_reboot')
        on_reboot.appendChild(doc.createTextNode('restart'))
        domain.appendChild(on_reboot)

        on_crash = doc.createElement('on_crash')
        on_crash.appendChild(doc.createTextNode('destroy'))
        domain.appendChild(on_crash)

        # 创建 metadata
        print("data:", data, "vm_name:")
        metadata = create_metadata(doc, data, vm_name, vm_uuid)
        domain.appendChild(metadata)

        # 创建 sysinfo
        sysinfo = create_sysinfo(doc, vm_uuid)
        domain.appendChild(sysinfo)

        # 创建 <os> 元素
        os = doc.createElement('os')
        domain.appendChild(os)

        systeminfo = get_system_info()
        arch = systeminfo['machine']
        if arch == "aarch64":
            os.setAttribute('firmware', 'efi')
        elif arch == "x86_64":
            # 添加电源管理配置
            # arm架构下，不需要这个配置，arm下用的是PSCI管理cpu电源，
            # pm是acpi的产物，acpi还有其他特性，所以acpi要在xml文件里面设置，但pm不需要
            # PSCI 是 ARM 虚拟化的底层默认协议，由 Hypervisor（如 KVM/QEMU）自动管理。不需要再xml文件中设置
            pm = doc.createElement('pm')
            suspend_to_mem = doc.createElement('suspend-to-mem')
            suspend_to_mem.setAttribute('enabled', 'no')
            pm.appendChild(suspend_to_mem)
            suspend_to_disk = doc.createElement('suspend-to-disk')
            suspend_to_disk.setAttribute('enabled', 'no')
            pm.appendChild(suspend_to_disk)
            domain.appendChild(pm)
        os_type = doc.createElement('type')
        os_type.setAttribute('arch', arch)
        # os_type.setAttribute('machine', 'pc-i440fx')
        os_type.appendChild(doc.createTextNode('hvm'))
        os.appendChild(os_type)

        if data.get('cdrom'):
            # 设置启动顺序：先从光盘启动，后从硬盘启动
            boot1 = doc.createElement('boot')
            boot1.setAttribute('dev', 'cdrom')
            os.appendChild(boot1)

        boot = doc.createElement('boot')
        boot.setAttribute('dev', 'hd')
        os.appendChild(boot)

        smbios = doc.createElement('smbios')
        smbios.setAttribute('mode', 'sysinfo')
        os.appendChild(smbios)

        # 创建 features
        features = create_features(doc, arch)
        domain.appendChild(features)

        # 创建 clock
        clock = create_clock(doc)
        domain.appendChild(clock)

        # 创建 CPU 配置
        cpu = create_cpu(doc, data.get('vcpu_unit', 2), arch)
        domain.appendChild(cpu)

        # 创建 <devices> 元素
        devices = doc.createElement('devices')
        domain.appendChild(devices)

        # # 创建网络接口
        # network_list = data.get('interface', [])
        # for network in network_list:
        #     interface = create_interface_element_ovs(doc, network)
        #     devices.appendChild(interface)
        # 创建网络接口
        network_list = data.get('interface_info', [])
        for network in network_list:
            interface = create_interface_element_ovs(doc, network)
            devices.appendChild(interface)

        # 添加系统盘
        xml_parts = []
        used_devices = set()  # 记录已使用的设备名（如 vda, vdb...）
        if data.get('disk'):
            disk_list = data.get("disk", [])
            for disk_dict in disk_list:
                bus = disk_dict.get("bus", "virtio")

                # 自动分配设备名（确保唯一）
                if bus == "virtio":
                    dev_prefix = "vd"
                elif bus in ("sata", "scsi"):
                    dev_prefix = "sd"
                elif bus == "ide":
                    dev_prefix = "hd"
                else:
                    raise ValueError(f"Unsupported bus type: {bus}")

                # 找到第一个未使用的设备名
                for letter_code in range(ord('a'), ord('z')+1):
                    dev = f"{dev_prefix}{chr(letter_code)}"
                    if dev not in used_devices:
                        used_devices.add(dev)
                        break
                else:
                    raise RuntimeError(
                        "No available device names (a-z exhausted)")

                storage_type_code = disk_dict.get('storage_type_code')
                if storage_type_code is None or storage_type_code == '':
                    storage_type_code = 'qcow2'
                    disk_dict['storage_type_code'] = storage_type_code
                system_disk = create_local_disk_element1(doc, disk_dict, dev)
                devices.appendChild(system_disk)

        # 添加 CDROM 设备
        # 添加 CDROM 设备（完全适配您的数据结构）
        if data.get('cdrom'):
            cdrom_list = data['cdrom']
            if isinstance(cdrom_list, list) and cdrom_list:  # 确保是非空列表
                first_cdrom = cdrom_list[0]
                if isinstance(first_cdrom, dict):
                    cdrom_path = first_cdrom.get('path', '').strip()
                    if cdrom_path:  # 只有path非空时才处理
                        cdrom = create_cdrom_element(doc, first_cdrom, arch)
                        devices.appendChild(cdrom)

        # 添加其他设备
        add_additional_devices(doc, devices,arch, data.get('display_protocol'))

        # 添加graphics设备
        if data.get('display_protocol'):
            graphics = create_graphics_element(
                doc, data.get('display_protocol'))
            devices.appendChild(graphics)

        # 添加鼠标和键盘
        if data.get('input_devices'):
            create_input_element(doc, devices, data)

        # 添加GPU设备
        if data.get('enable_gpu'):
            gpu = create_gpu_element(doc)
            devices.appendChild(gpu)

        # 生成XML字符串
        vm_xml = doc.toprettyxml(indent="  ")
        print("xxxxxxxxxx:", vm_xml)

        try:
            dom = self.conn.defineXML(vm_xml)
            if dom is None:
                return {'status': 'failed', 'message': 'Failed to define a persistent domain.'}

            # 启动虚拟机
            dom.create()
            data["vm_xml"] = vm_xml
            # 获取虚拟机XML并解析spice/vnc端口
            xml_desc = dom.XMLDesc()
            root = ET.fromstring(xml_desc)
            vnc_port = 0
            spice_port = 0

            for graphics in root.findall(".//graphics"):
                if graphics.get("type") == "vnc":
                    vnc_port = int(graphics.get("port", "0"))
                elif graphics.get("type") == "spice":
                    spice_port = int(graphics.get("port", "0"))

            data["vnc_port"] = vnc_port
            data["spice_port"] = spice_port
            # 获取当前虚拟机状态（在启动前检查）
            state, _ = dom.state()
            data["status"] = VMClient.state_str[state]

            data["result"] = "success"

            return data

        except libvirt.libvirtError as e:
            data["result"] = "failed"
            return data

    def test_create_vm(self):
        # 定义虚拟机的XML配置
        vm_xml = """
        <domain type='kvm'>
        <name>test-an</name>
        <memory unit='KiB'>1048576</memory>
        <vcpu placement='static'>1</vcpu>
        <os>
            <type arch='x86_64' machine='pc-i440fx-2.9'>hvm</type>
            <boot dev='hd'/>
        </os>
        <devices>
            <disk type='file' device='disk'>
            <driver name='qemu' type='qcow2'/>
            <source file='/var/lib/libvirt/images/an84.qcow2'/>
            <target dev='vda' bus='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x04' function='0x0'/>
            </disk>
            <interface type='network'>
            <mac address='52:54:00:6b:3c:52'/>
            <source network='default'/>
            <model type='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x03' function='0x0'/>
            </interface>
            <graphics type='vnc' port='-1' autoport='yes'/>
        </devices>
        </domain>
        """

        # 创建虚拟机
        try:
            self.conn.createXML(vm_xml, 0)
            print('Virtual Machine created successfully.')
        except libvirt.libvirtError as e:
            print(f'Failed to create VM: {e}')

        return True

    def test_define_vm(self):

        # 连接到 libvirtd 服务
        conn = libvirt.open('qemu:///system')
        if conn is None:
            print('Failed to open connection to qemu:///system')
            return

        # 定义虚拟机的XML配置
        vm_xml = """
        <domain type='kvm'>
        <name>test-an</name>
        <memory unit='KiB'>1048576</memory>
        <vcpu placement='static'>1</vcpu>
        <os>
            <type arch='x86_64' machine='pc-i440fx-2.9'>hvm</type>
            <boot dev='hd'/>
        </os>
        <devices>
            <disk type='file' device='disk'>
            <driver name='qemu' type='qcow2'/>
            <source file='/var/lib/libvirt/images/an84.qcow2'/>
            <target dev='vda' bus='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x04' function='0x0'/>
            </disk>
            <interface type='network'>
            <mac address='52:54:00:6b:3c:52'/>
            <source network='default'/>
            <model type='virtio'/>
            <address type='pci' domain='0x0000' bus='0x00' slot='0x03' function='0x0'/>
            </interface>
            <graphics type='vnc' port='-1' autoport='yes'/>
        </devices>
        </domain>
        """

        # 创建虚拟机
        try:
            dom = conn.defineXML(vm_xml)
            if dom is None:
                print('Failed to define a persistent domain.')
            else:
                print('Virtual Machine defined successfully.')

            # 启动虚拟机
            dom.create()
            print('Virtual Machine started successfully.')
        except libvirt.libvirtError as e:
            print(f'Failed to create VM: {e}')

        # 关闭连接
        conn.close()
        return True

    def get_vm_list(self):
        vms = []
        domains = self.conn.listAllDomains()
        for domain in domains:
            state, reason = domain.state()
            info = domain.info()
            print(state, reason, info)
            vm = {
                "name": domain.name(),
                "cpu_count": info[3],
                "memory": info[1] / 1024,
                "uuid": domain.UUIDString(),
                # "xml": domain.XMLDesc(),
                "state": VMClient.state_str[state]
            }
            vms.append(vm)

        return vms

    def get_vm_info_by_name(self, name):
        """
        通过名字获取虚拟机详情
        """
        domain = self.conn.lookupByName(name)
        info = domain.info()
        state, reason = domain.state()
        xml = domain.XMLDesc()
        uuid = domain.UUIDString()
        disk_paths = []

        # 使用 ElementTree 解析 XML 配置
        tree = ET.ElementTree(ET.fromstring(xml))
        root = tree.getroot()
        for disk in root.findall(".//disk"):
            source = disk.find(".//source")
            if source is not None:
                file_path = source.get("file")  # 获取磁盘文件的路径
                if file_path:
                    disk_paths.append(file_path)
                    
         # 2. 获取图形端口信息
        spice_port = 0
        vnc_port = 0
        for graphics in root.findall(".//graphics"):
            g_type = graphics.get("type")
            port = graphics.get("port")
            if g_type == "spice" and port:
                spice_port = int(port)
            elif g_type == "vnc" and port:
                vnc_port = int(port)
        vm = {
            "name": domain.name(),
            "cpu_count": info[3],
            "memory": info[1] / 1024,
            "uuid": uuid,
            "xml": xml,
            "state": VMClient.state_str[state],
            "disk": disk_paths,
            "spice_port": spice_port,
            "vnc_port": vnc_port
        }
        return vm

    def start_vm(self, vm_name):
        """启动虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            state, _ = domain.state()
        
            # 检查虚拟机状态 (state[0] 是状态码)
            if state == libvirt.VIR_DOMAIN_RUNNING:
                print(f"Virtual Machine {vm_name} is already running.")
                return True
            domain.create()  # 启动虚拟机
            print(f"Virtual Machine {vm_name} started.")
            return True
        except libvirt.libvirtError as e:
            error_code = e.get_error_code()
            if error_code == libvirt.VIR_ERR_NO_DOMAIN:
                print(f"Virtual Machine {vm_name} not found.")
            else:
                print(f"Error starting VM {vm_name}: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error starting VM {vm_name}: {e}")
            return False

    # 停止虚拟机
    def stop_vm(self, vm_name):
        """停止虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.shutdown()  # 关机命令
            print(f"Virtual Machine {vm_name} is shutting down.")
        except Exception as e:
            print(f"Error stopping VM: {e}")

    # 强制停止虚拟机
    def force_stop_vm(self, vm_name):
        """强制停止虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.destroy()  # 强制关闭虚拟机
            print(f"Virtual Machine {vm_name} is forcefully stopped.")
        except Exception as e:
            print(f"Error forcing stop VM: {e}")

    # 重启虚拟机
    def reboot_vm(self, vm_name):
        """重启虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.reboot()  # 重启虚拟机
            print(f"Virtual Machine {vm_name} is rebooting.")
        except Exception as e:
            print(f"Error rebooting VM: {e}")

    def force_reboot_vm(self, vm_name):
        """重启虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.destroy()  # 强制关闭虚拟机
            domain.create()  # 重新启动虚拟机 # 重启虚拟机
            print(f"Virtual Machine {vm_name} is rebooting.")
        except Exception as e:
            print(f"Error rebooting VM: {e}")

    def suspend_vm(self, vm_name):
        """暂停虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.suspend()  # 暂停虚拟机
            print(f"Virtual Machine {vm_name} is suspend.")
        except Exception as e:
            print(f"Error suspending VM: {e}")

    def resume_vm(self, vm_name):
        """恢复虚拟机"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.resume()  # 恢复虚拟机
            print(f"Virtual Machine {vm_name} is resume.")
        except Exception as e:
            print(f"Error resuming VM: {e}")

    # 获取虚拟机的 XML 配置
    def get_vm_xml(self, vm_name):
        """获取虚拟机的 XML 配置"""
        try:
            domain = self.conn.lookupByName(vm_name)
            xml_desc = domain.XMLDesc()  # 获取虚拟机的 XML 配置
            return xml_desc
        except Exception as e:
            print(f"Error getting VM XML: {e}")

    def del_vm(self, vm_name):
        """
        根据名称删除虚拟机及其磁盘文件
        """
        try:
            # 获取domain对象
            domain = self.conn.lookupByName(vm_name)

            # 获取磁盘信息
            xml_desc = domain.XMLDesc()
            root = ET.fromstring(xml_desc)
            disk_paths = []
            for disk in root.findall(".//disk"):
                source = disk.find(".//source")
                if source is not None:
                    file_path = source.get("file")
                    if file_path:
                        disk_paths.append(file_path)

            # 检查并关闭虚拟机
            state, _ = domain.state()
            if state == libvirt.VIR_DOMAIN_RUNNING:
                domain.destroy()
                time.sleep(2)  # 等待虚拟机完全关闭

            # 取消定义虚拟机
            domain.undefineFlags(libvirt.VIR_DOMAIN_UNDEFINE_MANAGED_SAVE |
                                 libvirt.VIR_DOMAIN_UNDEFINE_SNAPSHOTS_METADATA)

            # 删除磁盘文件
            # for disk_path in disk_paths:
            #     if os.path.exists(disk_path):
            #         try:
            #             os.remove(disk_path)
            #         except OSError as e:
            #             print(f"删除磁盘文件失败: {disk_path}, 错误: {e}")

            return True

        except libvirt.libvirtError as e:
            err_msg = str(e)
            # 判断是否为“虚拟机不存在”错误
            if "Domain not found" in err_msg or "no domain with matching name" in err_msg:
                print(f"虚拟机不存在，视为删除成功: {vm_name}")
                return True
            print(f"删除虚拟机失败: {vm_name}, 错误: {e}")
            return False
        except Exception as e:
            print(f"删除虚拟机时发生未知错误: {vm_name}, 错误: {e}")
            return False

    def set_vm_memory(self, vm_name, new_memory):
        """动态增加虚拟机内存"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.setMemory(new_memory)  # 设置内存，单位是字节
            print(f"Memory of VM {vm_name} set to {new_memory} bytes.")
        except Exception as e:
            print(f"Error setting memory for VM {vm_name}: {e}")

    def set_vm_vcpus(self, vm_name, new_vcpus):
        """动态增加虚拟机 CPU 核心数"""
        try:
            domain = self.conn.lookupByName(vm_name)
            domain.setVcpus(new_vcpus)  # 设置 CPU 核心数
            print(f"VM {vm_name} now has {new_vcpus} vCPUs.")
        except Exception as e:
            print(f"Error setting vCPUs for VM {vm_name}: {e}")

    def vm_clone(self, form):
        """
        有问题
        需要关闭虚拟机以后进行
        """
        name = form["vm_name"]
        clone_name = form["clone_name"]
        clone = form.get("clone", "1")  # 克隆方式：链接克隆，全量克隆
        # 获取虚拟机信息
        domain = self.conn.lookupByName(name)
        # 判断虚拟机的状态
        state, _ = domain.state()
        if state == libvirt.VIR_DOMAIN_RUNNING:
            # 关机
            domain.destroy()  # 强制关闭虚拟机

        xml_desc = domain.XMLDesc()

        # 解析 XML 配置
        root = ET.fromstring(xml_desc)
        # 修改虚拟机名称
        name_elem = root.find('.//name')
        if name_elem is not None:
            name_elem.text = clone_name
        # 去掉 UUID
        uuid_elem = root.find('.//uuid')
        if uuid_elem is not None:
            root.remove(uuid_elem)

        # 克隆磁盘
        from collections import deque
        disks_info = deque()
        for disk in form["disks"]:
            clone_form = {
                "volume_name": disk["volume_name"],
                "new_volume_name": disk["volume_name"] + "-clone",
                "storage_pool_name": disk["pool_name"]
            }
            new_volume = self.clone_storage_pool_volume(self, clone_form)
            disk_info = self.get_storage_pool_volume_info(
                self, disk["pool_name"], clone_form["new_volume_name"])
            disks_info.append(disk_info)
        # 获取所有磁盘的信息
        disks = root.findall('.//disk')
        for disk in disks:
            if len(disks_info) != 0:
                new_disk = disks_info.pop()

                source_elem = disk.find('.//source')
                target_elem = disk.find('.//target')
                # 找到并删除 backingStore 元素（如果存在）
                backing_store = disk.find('backingStore')
                if backing_store is not None:
                    disk.remove(backing_store)

                # 修改 source 元素中的 file 属性
                source = disk.find('source')
                if source is not None:
                    source.set('file', new_disk["path"])
            else:
                root.remove(disk)

        new_xml = ET.tostring(root, encoding='unicode', method='xml')
        dom = self.conn.defineXML(new_xml)
        if dom is None:
            return {'status': 'failed', 'message': 'Failed to define a persistent domain.'}

            # 启动虚拟机
        dom.create()

        return dom

    def test_create_vm_xml(self, form):
        name = form["name"]
        mem = form["memory"]
        vcpu = form["vcpu"]
        disk_form = form["disks"]
        print(self.conn.getVersion())
        dom = Domain()
        dom.set_name(name)

        vm_mem = str(capacity_to_bytes(mem))
        dom.set_memory(vm_mem)
        dom.set_current_memory(vm_mem)
        dom.set_vcpu(vcpu)

        os_boot = dom.set_os()
        host_info = self.get_host_info(self)  # 获取远程主机信息
        os_boot.set_type("hvm", attributes={
                         "arch": host_info["arch"], "machine": "pc-i440fx-oracular"})
        os_boot.set_boot("", attributes={"dev": "hd"})
        os_boot.set_boot_menu("")

        feature = dom.set_feature()
        feature.set_acpi()
        feature.set_apic()
        feature.set_vmport({"vmport": "off"})

        cpu = dom.set_cpu()
        cpu.set_atr_model("host-passthrough")

        clock = dom.set_clock()
        clock.set_default_timer()

        dom.set_on_poweroff()
        dom.set_on_reboot()
        dom.set_on_crash()

        pm = dom.set_pm()
        pm.set_suspend_to_mem()
        pm.set_suspend_to_disk()

        # devices设备处理
        devices = dom.set_devices()

        # 磁盘
        for d in disk_form:
            disk_atr_type = d["disk_atr_type"]
            disk = devices.set_disk()
            disk.set_atr_type(disk_atr_type)
            disk.set_atr_device()
            disk.set_driver(d["driver_type"])
            if disk_atr_type == "file":
                d_source_atr = {
                    "file": d["path"]
                }
                disk.set_source(d_source_atr)
            elif disk_atr_type == "block":
                d_source_atr = {
                    "dev": d["path"]
                }
                disk.set_source(d_source_atr)

            disk.set_target()

        # 控制器

        # 网络

        devices.set_console()
        devices.set_channel("")

        devices.set_default_input()
        devices.set_default_graphics()
        devices.set_audio_backend()
        devices.set_default_sound()

        devices.set_default_redirect()
        # devices.set_default_redirect()

        xml_desc = dom.format_xml()

        return xml_desc

import uuid
import logging
import datetime

from app.celery import app
from app.tasks.vm_tasks import VmCreateTask
from sqlalchemy.orm import joinedload
from utils.db import get_dbi
from db.model.hci.network import (
    Switch, SwitchPorts, SwitchPortGroups, 
    SwitchPortGroupRules, HostSwitchMapping
)
from db.model.hci.compute import Host, Cluster
from api.ovs.client import Client as OvsClient
from utils.tools import generate_unique_port_name

@app.task(base=VmCreateTask, name="create_network")
def create_network(domain_id, resources):
    #创建虚拟机之前先创建网络
    print(f"Creating VM Network: {domain_id} with resources: {resources}")
    
    new_interfaces = []
    interfaces = resources.get("interface", [])
    
    with  get_dbi().session_scope() as session:
        for interface in interfaces:
            port_id = interface.get("port_id")
            mac_address = interface.get("mac_address")
            port = session.query(SwitchPorts).filter(SwitchPorts.id==port_id).options(joinedload(SwitchPorts.switch)).first()
            
            
            interface_type = port.switch.vswitch_type
            if interface_type == "ovs":
                # 创建ovs网络
                new_interface = {
                    "interface_type": interface_type,
                    "bridge": port.switch.name,
                    "mac":  mac_address
                }
                new_interfaces.append(new_interface)
            
            #TODO: 添加其他类型网络
            if interface_type == "other":
                pass
            
        
    return {"status": "success", "form": resources, "interface": new_interfaces}


@app.task(name="configure_network")
def configure_network(network_name, configuration):
    # 配置网络的逻辑
    print(f"Configuring Network: {network_name} with configuration: {configuration}")
    import time
    time.sleep(3)
    return f"Network {network_name} configured successfully."


@app.task(name="create_bridge")
def create_bridge(form):
    """
    创建网桥
    """
    ip = form.get("host_ip", "")
    switch_id = form.get("switch_id", "")
    name = form.get("name", "")
    client = OvsClient(ip)
    res = client.create_ovs_bridge(client, name)
    if res:
        dbi = get_dbi()
        with dbi.session_scope() as session:
            # 检查交换机是否存在
            exists = session.query(Switch).filter(Switch.id == switch_id).scalar() is not None
            if not exists:
                return {"msg": "交换机不存在", "code": 404}
            # 构建更新字典
            update_data = {Switch.status: "active"}

            # 如果有数据需要更新，则执行更新
            if update_data:
                session.query(Switch).filter(Switch.id == switch_id).update(update_data)


@app.task(name="create_bridge_call_back")
def create_bridge_call_back(form):
    """
    创建网桥回调
    """
    switch_id = form.get("switch_id", "")
    dbi = get_dbi()
    with dbi.session_scope() as session:
        # 检查交换机是否存在
        exists = session.query(Switch).filter(Switch.id == switch_id).scalar() is not None
        if not exists:
            return {"msg": "交换机不存在", "code": 404}
        # 构建更新字典
        update_data = {Switch.status: "active"}

        # 如果有数据需要更新，则执行更新
        if update_data:
            session.query(Switch).filter(Switch.id == switch_id).update(update_data)
            

@app.task(name="create_distributed_bridge_callback")
def create_distributed_bridge_callback(form, switch_id):
    """
    创建分布式网桥回调，处理状态更新和物理网卡端口信息。
    """
    print("创建分布式网桥回调参数 form:", form)
    
    # 初始化状态和接口信息收集
    status = "active"
    all_bound_interfaces = []
    all_failed_interfaces = []
    
    # 检查所有任务的执行结果
    for res in form:
        # 检查主任务结果
        if res.get("result") != "success":
            status = "abnormal"  # 分布式交换机有异常
            break
            
        # 收集物理接口信息
        bound_interfaces = res.get("bound_interfaces", [])
        failed_interfaces = res.get("failed_interfaces", [])
        
        if bound_interfaces:
            all_bound_interfaces.extend(bound_interfaces)
        if failed_interfaces:
            all_failed_interfaces.extend(failed_interfaces)
           
    dbi = get_dbi() 
    with dbi.session_scope() as session:
        try:
            # 查询交换机是否存在
            switch = session.query(Switch).filter(Switch.id == switch_id).first()
            
            if not switch:
                return {"msg": "交换机不存在", "code": 404}
            
            # 更新交换机状态
            switch.status = status
            session.add(switch)
            
            # 为成功绑定的物理接口创建端口记录
            for port_info in all_bound_interfaces:
                try:
                    switch_port = SwitchPorts(
                        switchs_id=switch_id,
                        name=port_info['name'],
                        interface=port_info['interface'],
                        port_type=port_info['type'],
                        is_physical=True if port_info.get('is_physical') else False,
                        # 状态根据实际端口状态设置
                        status=port_info['status'] if port_info['status'] != 'exists' else 'active',
                        extra_config=str(port_info.get('extra_config', {})),  # 转换为字符串存储
                        create_time=datetime.datetime.now()
                    )
                    session.add(switch_port)
                    print(f"Added physical port {port_info['name']} to switch {switch_id}")
                except Exception as e:
                    print(f"Failed to add port {port_info['name']}: {str(e)}")
                    all_failed_interfaces.append({
                        'name': port_info['name'],
                        'error': str(e)
                    })
            
            # 提交所有更改
            session.commit()
            
            # 准备返回消息
            result_msg = f"交换机状态更新为{status}"
            if all_bound_interfaces:
                result_msg += f", 成功添加{len(all_bound_interfaces)}个物理端口"
            if all_failed_interfaces:
                result_msg += f", {len(all_failed_interfaces)}个端口添加失败"
            
            print(f"create_distributed_bridge_callback 执行结果: {result_msg}")
            
            return {
                "msg": result_msg,
                "code": 200,
                "data": {
                    "switch_id": str(switch_id),
                    "status": status,
                    "bound_ports": len(all_bound_interfaces),
                    "failed_ports": len(all_failed_interfaces),
                    "failed_interfaces": all_failed_interfaces
                }
            }
            
        except Exception as e:
            print(f"创建分布式网桥回调出错：{str(e)}")
            session.rollback()
            return {
                "msg": f"更新交换机状态失败: {str(e)}",
                "code": 500,
                "data": {
                    "switch_id": str(switch_id)
                }
            }
        

@app.task(name="del_bridge")
def del_bridge(form):
    """
    删除网桥
    """
    dbi = get_dbi()
    with dbi.session_scope() as session:
        for switch_id in form:
            switch = session.query(Switch).filter(Switch.id == switch_id).first()
            if switch.vswitch_type == "local":
                # 删除本地交换机
                # name = switch.name
                # 查询主机信息
                host = session.query(Host).filter(Host.id == switch.host_id).first()
                # 删除分布式交换机
                client = OvsClient(host.ip)
                res = client.delete_ovs_bridge(client, switch.name)
                if res:
                    #删除数据库中的数据
                    session.delete(switch)
                    # switch = session.query(Switch).filter(Switch.id == switch_id).delete(synchronize_session=False)

            elif switch.vswitch_type == "distributed":
                # 删除分布式交换机 todo
                cluster = session.query(Cluster).options(joinedload(Cluster.hosts)).filter(
                    Cluster.id == switch.cluster_id).first()
                if cluster.hosts:
                    for host in cluster.hosts:
                        client = OvsClient(host.ip)
                        res = client.delete_ovs_bridge(client, switch.name)
                        if res:
                            # 删除数据库中的数据
                            switch = session.query(Switch).filter(Switch.id == switch_id).delete(
                                synchronize_session=False)
                            

@app.task
def del_bridge_call_back(form):
    """回调任务：清理分布式交换机相关的所有数据库记录"""
    try:
        switch_id = form.get("switch_id", "")
        if not switch_id:
            print("Error: Missing switch_id in del_bridge_call_back")
            return {"msg": "缺少交换机ID", "code": 400}

        dbi = get_dbi()
        with dbi.session_scope() as session:
            try:
                # 1. 删除主机与交换机的映射关系
                session.query(HostSwitchMapping)\
                    .filter(HostSwitchMapping.switch_id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"Deleted host mappings for switch {switch_id}")

                # 2. 删除交换机端口
                session.query(SwitchPorts)\
                    .filter(SwitchPorts.switchs_id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"Deleted switch ports for switch {switch_id}")

                # 3. 删除端口组规则
                port_groups = session.query(SwitchPortGroups)\
                    .filter(SwitchPortGroups.switchs_id == switch_id)\
                    .all()
                for pg in port_groups:
                    session.query(SwitchPortGroupRules)\
                        .filter(SwitchPortGroupRules.switch_port_group_id == pg.id)\
                        .delete(synchronize_session=False)
                print(f"Deleted port group rules for switch {switch_id}")

                # 4. 删除端口组
                session.query(SwitchPortGroups)\
                    .filter(SwitchPortGroups.switchs_id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"Deleted port groups for switch {switch_id}")

                # 5. 最后删除交换机
                deleted = session.query(Switch)\
                    .filter(Switch.id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"Deleted switch {switch_id}, affected rows: {deleted}")

                session.commit()
                return {
                    "msg": "删除交换机及相关数据成功",
                    "code": 200,
                    "data": {"switch_id": switch_id}
                }

            except Exception as db_error:
                session.rollback()
                error_msg = f"删除交换机数据库记录失败: {str(db_error)}"
                print(error_msg)
                return {"msg": error_msg, "code": 500}

    except Exception as e:
        error_msg = f"删除交换机回调处理失败: {str(e)}"
        print(error_msg)
        return {"msg": error_msg, "code": 500}
        

@app.task(base=VmCreateTask, name="create_bridge_port")
def create_bridge_port(vm_name, form):
    """
    在网桥上创建端口
    """
    host_id = form.get("host_id", "")
    # 端口组id
    switch_port_group_id = form.get("switch_port_id")
    dbi = get_dbi()
    with dbi.session_scope() as session:
        # 写入数据库
        result = session.query(Switch, SwitchPortGroups) \
            .join(SwitchPortGroups, Switch.id == SwitchPortGroups.switchs_id) \
            .filter(SwitchPortGroups.id == switch_port_group_id) \
            .first()
        if result:
            switch_info, switch_port_group_info = result

        host = session.query(Host).filter(Host.id == host_id).first()
        host_ip = host.ip
        client = OvsClient(host_ip)
        port_name = generate_unique_port_name(prefix=switch_port_group_info.name)

        form["bridge_name"] = switch_info.name
        form["tap_name"] = port_name
        form["mtu"] = switch_info.mtu
        form["vlan_tag"] = switch_port_group_info.vlan_id
        client.create_tap_interface(client, form)

        # 创建新的端口实例
        switch_port = SwitchPorts(
            switchs_id=switch_info.id,
            name=port_name,
            switch_port_group_id=switch_port_group_id
        )

        session.add(switch_port)

        res = {
            "port_name": port_name
        }
    return res

@app.task(name="create_bridge_port_callback")
def create_bridge_port_callback(form):
    """回调任务：更新数据库（需手动处理结果）"""
    logger = logging.getLogger(__name__)
    logger.info("开始更新端口数据库记录")
    logger.info(f"create_bridge_port_callback: {form}")
    
    dbi = get_dbi()
    with dbi.session_scope() as session:
        new_ports = []
        for res in form:
            if res.get("operation_status") == "success":
                switch_port_id = res.get("switch_port_id")
                exists = False
                if switch_port_id:
                    exists = session.query(SwitchPorts).filter(SwitchPorts.id == switch_port_id).first() is not None
                if not exists:
                    new_port = SwitchPorts(
                        switchs_id=res.get("switch_id", ""),
                        name=res.get("port_name", ""),
                        switch_port_group_id=res.get("switch_port_group_id", "")
                    )
                    session.add(new_port)
                    session.flush()  # 获取新生成的id
                    res["switch_port_id"] = str(new_port.id)
                    new_ports.append(new_port)
                else:
                    logger.info(f"端口 switch_port_id={switch_port_id} 已存在，跳过添加")
        if new_ports:
            try:
                session.commit()
                logger.info(f"成功创建 {len(new_ports)} 个端口记录")
            except Exception as e:
                logger.error(f"创建端口数据库记录失败: {str(e)}")
                session.rollback()
                raise
        else:
            logger.warning("没有需要新建的端口记录")
    return form

@app.task(name="del_bridge_port")
def del_bridge_port(form):
    """
    从网桥上删除端口
    """
    ip = form.get("ip", "")
    client = OvsClient(ip)
    client.delete_tap_interface(client,form)

@app.task(name="del_distributed_bridge_callback")
def del_distributed_bridge_callback(form, switch_id):
    """分布式交换机删除回调：清理所有相关数据库记录
    
    Args:
        form: 删除操作的结果列表，每个主机的执行结果
        switch_id: 要删除的交换机ID
        
    Returns:
        dict: 包含执行结果的字典
    """
    print(f"开始处理分布式交换机 {switch_id} 删除回调")
    print(f"删除任务执行结果: {form}")
    
    try:
        # 检查删除操作结果
        all_success = True
        failed_hosts = []
        
        for res in form:
            if not res:  # 空结果视为失败
                all_success = False
                continue
                
            host_ip = res.get("host_ip")
            status = res.get("status", False)
            
            if not status:
                all_success = False
                failed_hosts.append(host_ip)
        
        if not all_success:
            error_msg = f"部分主机删除失败: {', '.join(failed_hosts)}"
            print(error_msg)
            return {"msg": error_msg, "code": 500}
            
        # 所有主机都删除成功，开始清理数据库记录
        dbi = get_dbi()
        with dbi.session_scope() as session:
            try:
                # 1. 删除主机与交换机的映射关系
                session.query(HostSwitchMapping)\
                    .filter(HostSwitchMapping.switch_id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"已删除交换机 {switch_id} 的主机映射")

                # 2. 删除交换机端口记录
                session.query(SwitchPorts)\
                    .filter(SwitchPorts.switchs_id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"已删除交换机 {switch_id} 的端口记录")

                # 3. 删除端口组规则
                port_groups = session.query(SwitchPortGroups)\
                    .filter(SwitchPortGroups.switchs_id == switch_id)\
                    .all()
                for pg in port_groups:
                    session.query(SwitchPortGroupRules)\
                        .filter(SwitchPortGroupRules.switch_port_group_id == pg.id)\
                        .delete(synchronize_session=False)
                print(f"已删除交换机 {switch_id} 的端口组规则")

                # 4. 删除端口组
                session.query(SwitchPortGroups)\
                    .filter(SwitchPortGroups.switchs_id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"已删除交换机 {switch_id} 的端口组")

                # 5. 删除交换机记录
                deleted = session.query(Switch)\
                    .filter(Switch.id == switch_id)\
                    .delete(synchronize_session=False)
                print(f"已删除交换机 {switch_id}, 影响行数: {deleted}")

                session.commit()
                return {
                    "msg": "删除分布式交换机及相关数据成功",
                    "code": 200,
                    "data": {"switch_id": str(switch_id)}
                }

            except Exception as db_error:
                session.rollback()
                error_msg = f"删除交换机数据库记录失败: {str(db_error)}"
                print(error_msg)
                return {"msg": error_msg, "code": 500}

    except Exception as e:
        error_msg = f"处理分布式交换机删除回调失败: {str(e)}"
        print(error_msg)
        return {"msg": error_msg, "code": 500}



@app.task(name="switch_bind_physical_interface_callback")
def switch_bind_physical_interface_callback(form):
    """
    绑定物理网卡到交换机回调
    Args:
        switch_id: 交换机ID
        nic_name: 物理网卡名称
        host_id: 主机ID
    Returns:
        dict: 包含操作结果的字典
    """
    switch_name = form.get("switch_name", "")
    nic_name = form.get("nic_name", "")
    host_id = form.get("host_id", "")
    task_status = form.get("task_status", "failed")
    switch_id = form.get("switch_id", "")
    switch_port_group_id = form.get("switch_port_group_id", None)  # 新
    if task_status == "success":
        print("绑定成功,开始创建数据库数据")
        dbi = get_dbi()
        with dbi.session_scope() as session:
            
            port = SwitchPorts(
                    switchs_id=switch_id,
                    name=nic_name,
                    is_physical=1,
                    switch_port_group_id=switch_port_group_id if switch_port_group_id else None  # 新增：端口组ID
                )
            session.add(port)
    
    
@app.task(name="switch_unbind_physical_interface_callback")
def switch_unbind_physical_interface_callback(form):
    """
    交换机解绑物理网卡回调函数
    Args:
        switch_id: 交换机ID
        nic_name: 物理网卡名称
        host_id: 主机ID
    Returns:
        dict: 包含操作结果的字典
    """
    switch_name = form.get("switch_name", "")
    nic_name = form.get("nic_name", "")
    host_id = form.get("host_id", "")
    task_status = form.get("task_status", "failed")
    switch_id = form.get("switch_id", "")
    if task_status == "success":
        print("解绑成功,开始删除数据库数据")
        dbi = get_dbi()
        with dbi.session_scope() as session:
            # 1. 检查交换机是否存在
            switch = session.query(Switch).filter(Switch.id == switch_id).first()
            if not switch:
                return {"msg": "交换机不存在", "code": 200}

            # 2. 检查网卡是否已绑定到该主机的交换机
            host_info = session.query(Host).filter(Host.id == host_id).first()
            existing_port = session.query(SwitchPorts).join(Switch).join(HostSwitchMapping) \
                .filter(
                    SwitchPorts.is_physical == 1,
                    SwitchPorts.name == nic_name,
                    Switch.id == switch_id,
                    HostSwitchMapping.switch_id == switch_id,
                    HostSwitchMapping.host_id == host_id
                ).first()
            

            if not existing_port:
                return {"msg": f"网卡 {nic_name} 未绑定到主机 {host_info.name} 的交换机 {switch.name}", "code": 200}

            session.delete(existing_port)
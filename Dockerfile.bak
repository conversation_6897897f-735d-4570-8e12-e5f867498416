FROM tianwen1:5000/python:3.8 as builder


RUN apt-get install -y git

RUN git clone http://192.168.5.54/hci/hci_db.git \
    && cd /hci_db \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_db_commit_version \
    && git describe --abbrev=1  --tags > hci_db_latest_tag \   
    && rm -rf /hci_db/.git

RUN git clone http://192.168.5.54/hci/hci_api.git \
    && cd /hci_api \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > hci_api_commit_version \
    && git describe --abbrev=1  --tags > hci_api_latest_tag \ 
    && rm -rf /hci_api/.git



FROM tianwen1:5000/python:3.8 

#RUN apt-get install -y gcc

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS

COPY --from=builder /hci_db /hci_db
COPY --from=builder /hci_api /hci_api

ENV PYTHONPATH=/hci_api:/hci_db

ADD . /code
WORKDIR /code

#RUN apt update -y && apt install -y libvirt-dev

RUN pip3 install -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple
#RUN pip3 install -r requirements.txt -i  https://mirrors.aliyun.com/pypi/simple/
#RUN pip3 install -r requirements.txt

#CMD python -u main.py

#EXPOSE 8085

# -*- coding: utf-8 -*-

from calendar import c
from multiprocessing import pool
from unittest import result
from venv import create
import tornado.ioloop
from sqlalchemy import asc, desc

import pyrestful.rest
from api.prometheus.client import Client as Pclient
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom

from api.log.log import CustomLogger
from celery import Celery, chord


import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class ClusterHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v5/cluster/detail/{cluster_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_cluster_detail(self, cluster_id):

        with self.session_scope() as session:
            #查询cluster记录
            cluster = session.query(Cluster).filter(Cluster.id == cluster_id).first()
            if cluster:
                data =  Cluster.to_dict_merge(cluster)
                return data

        return {"msg":"没有找到虚机", "code": 404}

    @post(_path="/v5/cluster/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_create(self, form):
        role = self.get_cookie('role', "")
        cluster_name = form.get("name", "")
        pool_id = form.get("pool_id", "")
        remark = form.get("remark", "")
        
        try:
            with self.session_scope() as session:
                cluster = Cluster()
                cluster.name = cluster_name
                cluster.pool_id = pool_id
                cluster.remark = remark
                session.add(cluster)
                
        except Exception as e:
            new_logger.log(
                self.username, "集群", "创建集群", "失败", role,
                "创建集群失败: %s" % str(e)
            )
            return {"status": "error", "message": "创建集群失败: %s" % str(e)}
        
        new_logger.log(
            self.username, "集群", "创建集群", "成功", role,
            "创建集群成功"
        )
        return {"msg": "创建集群完成", "code": 200}


    @post(_path="/v5/cluster/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_delete(self, form):
        name = form.get("name", "")
        role = self.get_cookie('role', "")
        
        cluster_id = form.get("id", "")
        try:
            with self.session_scope() as session:
                cluster = session.query(Cluster).filter(Cluster.id == cluster_id).first()
                if cluster.hosts:
                    new_logger.log(
                        self.username, "集群", "删除集群", "失败", role, "删除集群{name}失败"
                    )
                    return {"msg": "删除集群失败，请确认集群下是否存在主机", "code": 400}
                session.query(Cluster).filter(Cluster.id == cluster_id).delete()

        except Exception as e:
            new_logger.log(
                self.username, "集群", "删除集群", "失败", role, f"删除集群{name}失败"
            )
            traceback.print_exc()
            return {"code": 400, "msg": f"删除集群{name}失败"}
        
        new_logger.log(
            self.username, "集群", "删除集群", "成功", role,
            f"删除集群{name}成功"
        )
             
        return {"msg": "删除集群完成", "code": 200}
    
    
    # @log("集群", "删除集群")
    # @post(_path="/v5/cluster/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    # def hci_post_cluster_delete(self, form):
    #     name = form.get("name", "")
    #     role = self.get_cookie('role', "")
        
    #     cluster_id = form.get("id", "")

    #     with self.session_scope() as session:
    #         cluster = session.query(Cluster).filter(Cluster.id == cluster_id).delete()
                

             
    #     return {"msg": "删除集群完成", "code": 200}
    
    @post(_path="/v5/cluster/name/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_update(self, form):
        role = self.get_cookie('role', "")
        cluster_id = form.get("id", "")
        cluster_name = form.get("name", "")
        remark = form.get("remark", "")
        try:
            with self.session_scope() as session:
                session.query(Cluster).filter(Cluster.id == cluster_id).update({Cluster.name: cluster_name, Cluster.remark: remark})
                
        except Exception as e:
            new_logger.log(
                self.username, "集群", "更新集群", "失败", role,
                "更新集群: {},失败".format(cluster_name)
            ) 
            traceback.print_exc()
            return {"msg": "更新集群失败", "code": 500}
        
        new_logger.log(
            self.username, "集群", "更新集群", "成功", role,
            "更新集群: {},成功".format(cluster_name)
        )
            
        
        return {"msg": "更新集群成功", "code": 200}

    @post(_path="/v5/cluster/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_cluster_list(self, form):
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        type = form.get('type', 'recource')
        _id = form.get('_id', '')

        with self.session_scope() as session:
            # 首先创建基础查询
            query = session.query(Cluster)

            # 根据不同类型进行过滤
            if type == 'recource':
                # 获取所有相关的域
                query = query.join(Pool)

            elif type == 'pool':
                query = query.join(Pool).filter(Pool.id == _id)

            # 添加搜索条件
            if search_str:
                query = query.filter(Host.name.ilike(f"%{search_str}%"))

            # 添加排序
            if hasattr(Host, order_by):
                order_column = getattr(Host, order_by)
                if order_type == 'asc':
                    query = query.order_by(asc(order_column))
                else:
                    query = query.order_by(desc(order_column))

            # 获取总记录数
            total_records = query.count()

            # 执行分页
            hosts = query.offset((page - 1) * per_page).limit(per_page).all()

            # 将结果转换为字典列表
            host_list = [host.to_dict() for host in hosts]

            return {
                "msg": "获取集群列表成功",
                "code": 200,
                "total": total_records,
                "data": host_list
            }

    @get(_path="/v5/cluster/lists", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_get_cluster_list(self):
        with self.session_scope() as session:
            # 首先创建基础查询
            hosts = session.query(Cluster).all()
            host_list = [host.to_dict() for host in hosts]

            return host_list


    @get(_path="/v5/cluster/overview/{_id}", _produces=mediatypes.APPLICATION_JSON)
    def hci_cluster_overview(self, _id):
        role = self.get_cookie('role', "")
        data = {}

        with self.session_scope() as session:
            cluster = session.query(Cluster).filter(Cluster.id == _id).first()
            # data["cpu_architecture"] = cluster.cpu_architecture

            # Step 1: 查询所有Clusters下的所有 Hosts
            hosts = session.query(Host).filter(Host.cluster_id == _id).all()
            data["host_count"] = str(len(hosts))

            # Step 2: 查询所有 Host 下的所有虚拟机数量
            vms = session.query(Domain).filter(Domain.cluster_id == _id).all()
            data["vm_count"] = str(len(vms))
            logic_cpu_count = 0
            logic_mem_count = 0
            logic_disk_count = 0
            for vm in vms:
                logic_cpu_count += int(vm.vcpu)
                logic_mem_count += int(vm.memory)

            # Step 5: 查询所有 物理cpu，逻辑cpu，内存，磁盘
            data["cpu_allocation_count"] = str(logic_cpu_count)
            # data["cpu_allocation_rate"] = str(0)
            data["mem_allocation_count"] = str(logic_mem_count)
            data["disk_allocation_count"] = str(logic_disk_count) # 存疑

            c = Pclient()
            ip_pattern_parts = [f"{d.ip}:9100"  for d in hosts]
            ip_regex = "|".join(ip_pattern_parts)
            cpu_all_count = c.query_vector_by_query('sum(count(node_cpu_seconds_total{job = "node_exporter",mode ="idle"}) by (instance))')
            if cpu_all_count:
                data["cpu_all_count"] = cpu_all_count[0]["value"][1]
            all_cpu_hz = c.query_vector_by_query(f'sum(node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}})')
            use_cpu_hz = c.query_vector_by_query(f'sum((node_cpu_scaling_frequency_hertz{{job="node_exporter", instance=~"{ip_regex}"}} / node_cpu_frequency_max_hertz{{job="node_exporter", instance=~"{ip_regex}"}}))')
            if all_cpu_hz:
                data["cpu_all_hz"] = all_cpu_hz[0]["value"][1]
                data["cpu_use_hz"] = use_cpu_hz[0]["value"][1]

            all_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            use_mem_count = c.query_vector_by_query(f'sum(node_memory_MemTotal_bytes{{job = "node_exporter", instance=~"{ip_regex}"}}) - sum(node_memory_MemAvailable_bytes{{job = "node_exporter", instance=~"{ip_regex}"}})')
            if all_mem_count:
                data["mem_all_count"] = all_mem_count[0]["value"][1]
                data["mem_use_count"] = use_mem_count[0]["value"][1]

            all_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            use_disk_count = c.query_vector_by_query(f'sum(node_filesystem_size_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})- sum(node_filesystem_free_bytes{{job = "node_exporter", fstype!~"tmpfs|overlay|squashfs", device=~"/dev/[^/]+$", mountpoint="/", instance=~"{ip_regex}"}})')
            if all_disk_count:
                data["disk_all_count"] = all_disk_count[0]["value"][1]
                data["disk_use_count"] = use_disk_count[0]["value"][1]

        return {"msg": "查询集群概览成功", "code": 200, "data": data}
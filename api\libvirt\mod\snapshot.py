'''
Created on Mar 1, 2022

@author: maojj
'''

import json
import time

import settings
# from model.flavors import <PERSON>lavor,FlavorDetail
from dataclasses import dataclass
from string import Template
import libvirt
import os
import xml.etree.ElementTree as ET


class Client:

    def create_snapshot(self, form):

        domain_name = form['vm_name']
        snapshot_name = form.get("snapshot_name", "")
        # 获取指定虚拟机的 domain 对象
        now = int(time.time())
        if snapshot_name == "":
            snapshot_name = domain_name + str(now)
        domain = self.conn.lookupByName(domain_name)
        if domain is None:
            print(f'Failed to find the domain {domain_name}')
            return

        # 创建快照 XML 描述
        snapshot_xml = f"""
        <domainsnapshot>
            <name>{snapshot_name}</name>

            <description>{snapshot_name} Snapshot</description>

        </domainsnapshot>

        """

        # 创建快照
        try:
            domain.snapshotCreateXML(snapshot_xml, 0)
            print(f'Snapshot {snapshot_name} created for domain {domain_name}')
        except libvirt.libvirtError as e:
            print(f'Failed to create snapshot: {e}')
        return True

    def delete_snapshot(self, form):
        """
        删除快照
        :param form:
        :return:
        """

        domain_name = form['vm_name']
        snapshot_name = form.get("snapshot_name", "")
        # 获取指定虚拟机的 domain 对象
        now = int(time.time())
        if snapshot_name == "":
            snapshot_name = domain_name + str(now)
        domain = self.conn.lookupByName(domain_name)
        if domain is None:
            print(f'Failed to find the domain {domain_name}')
            return
        try:
            # 获取快照对象
            snapshot = domain.snapshotLookupByName(snapshot_name)

            # 删除快照
            snapshot.delete()  # 可选标志，可根据需要删除
            print(f'Snapshot {snapshot_name} created for domain {domain_name}')
        except libvirt.libvirtError as e:
            print(f'Failed to create snapshot: {e}')
        return True

    def restore_snapshot(self, form):
        # 查找目标虚拟机
        domain_name = form['vm_name']
        snapshot_name = form["snapshot_name"]
        try:
            dom = self.conn.lookupByName(domain_name)
        except libvirt.libvirtError:
            print(f'Failed to find the domain {domain_name}')
            return False

        # 查找快照
        try:
            snapshot = dom.snapshotLookupByName(snapshot_name, 0)
        except libvirt.libvirtError:
            print(f'Failed to find the snapshot {snapshot_name} for domain {domain_name}')
            return False

        # 恢复快照
        try:
            dom.revertToSnapshot(snapshot)
            print(f'Successfully restored snapshot {snapshot_name} for domain {domain_name}')
        except libvirt.libvirtError as e:
            print(f'Failed to restore snapshot: {e}')
            return False

        return True

    def snapshot_list(self, form):
        """
        快照列表
        :param form:
        :return:
        """
        vm = form.get("vm_name", "")
        domain = self.conn.lookupByName(vm)
        if domain is None:
            print(f'Failed to find the domain {vm}')
            return False

        try:
            # 获取快照列表
            snapshot_names = domain.snapshotListNames()
            # print("快照列表:")
            # for name in snapshot_names:
            #     print(f"- {name}")
        except libvirt.libvirtError as e:
            print(f"无法获取快照列表: {e}")
            return []

        return snapshot_names

    def create_manual_snapshot(self, form):
        """
        创建虚拟机快照
        todo 待完善
        """
        name = form["name"]
        # 获取虚拟机对象
        dom = self.conn.lookupByName(name)
        if dom is None:
            print(f"虚拟机 {name} 不存在。")
            return None
        xml_desc = dom.XMLDesc()
        # 解析XML
        tree = ET.fromstring(xml_desc)

        # 创建根元素
        domainsnapshot = ET.Element('domainsnapshot')
        # 添加描述
        description = ET.SubElement(domainsnapshot, 'description')
        description.text = f'虚拟机 {name} 快照'

        # mem = ET.SubElement(domainsnapshot, "memory", attrib={"snapshot": "no"})

        # 查找所有的磁盘目标
        disks = tree.findall('.//disk')
        for disk in disks:
            target_dev = disk.find('target').get('dev')  # 获取磁盘在虚拟机中的设备名
            source_path = disk.find('source').get('file')  # 获取磁盘文件的路径
            disk_type = disk.find('driver').get('type')
            print(f"Virtual Machine ID: {id}, Name: {dom.name()}")
            print(f"Disk Device: {target_dev}, Path: {source_path}")

            snap_disks = ET.SubElement(domainsnapshot, 'disks')

            # 添加第一个磁盘元素
            disk_xml = ET.SubElement(snap_disks, 'disk', attrib={'snapshot': 'manual', 'name': target_dev})

            current_timestamp = int(time.time())

            # 使用 split 按 "/" 分割，提取最后一部分
            disk_name = source_path.split('/')[-1]
            snap_name = name + "-snapshot-" + str(current_timestamp) + "." + disk_type
            new_path = source_path.rsplit(disk_name, 1)[0] + snap_name

            source = ET.SubElement(disk_xml, "source")
            source_xml = ET.SubElement(disk_xml, 'source', attrib={'file': new_path})
            break

        tree = ET.ElementTree(domainsnapshot)
        xml_string = ET.tostring(domainsnapshot, encoding='unicode', method='xml', xml_declaration=False)
        print(xml_string)

        # 创建快照
        snapshot = dom.snapshotCreateXML(xml_string, 0)
        if snapshot is None:
            print(f"无法创建虚拟机 {name}的快照。")
            return None

        print(f"虚拟机 {name} 快照已创建。")
        return snapshot

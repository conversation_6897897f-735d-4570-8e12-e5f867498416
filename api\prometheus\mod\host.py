import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from dacite import from_dict
from string import Template
import time
from datetime import datetime, timedelta
from api.grafana.client import Client
from ..client import fill_black_list
import numpy as np

POINT_COUNT = 12

class HostClient:
    def host_test(self, id):
        print(self.query_url)
        print(id)

 
    @fill_black_list
    def get_host_cpu_usage_rate(self, start, end):
        #query = """(1 - avg(rate(node_cpu_seconds_total{ job="node-exporter",mode="idle"}[5m])) by (instance)) * 100"""
        query = """(1 - avg(rate(node_cpu_seconds_total{ job="node_exporter",mode="idle"}[5m])) by (instance)) * 100"""
        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)
        
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))

        return self.convert_host_response_json(res)

    @fill_black_list
    def get_host_mem_usage_rate(self, start, end):
        #query = """(1 - (avg by (instance)(node_memory_MemAvailable_bytes{job="node-exporter"}) / avg by (instance)(node_memory_MemTotal_bytes{job="node-exporter"}))) * 100"""
        query = """(1 - (avg by (instance)(node_memory_MemAvailable_bytes{job="node_exporter"}) / avg by (instance)(node_memory_MemTotal_bytes{job="node_exporter"}))) * 100"""
        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)
        
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))

        return self.convert_host_response_json(res)

    @fill_black_list
    def get_host_io_usage_rate(self,start, end):
        #query = """( (sum by (instance)(avg(node_filesystem_size_bytes{job="node-exporter", fstype=~"xfs|ext.*"}) by (instance, device) - avg(node_filesystem_free_bytes{job="node-exporter", fstype=~"xfs|ext.*"}) by (instance, device))) * 100 ) / (sum by (instance)(avg(node_filesystem_size_bytes{job="node-exporter", fstype=~"xfs|ext.*"}) by (instance)))"""
        #下面这个query出现过问题， 有时能超过100%
        query = """( (sum by (instance)(avg(node_filesystem_size_bytes{job="node_exporter", fstype=~"xfs|ext.*"}) by (instance, device) - avg(node_filesystem_free_bytes{job="node_exporter", fstype=~"xfs|ext.*"}) by (instance, device))) * 100 ) / (sum by (instance)(avg(node_filesystem_size_bytes{job="node_exporter", fstype=~"xfs|ext.*"}) by (instance)))"""
        #query = """max((node_filesystem_size_bytes{job="node_exporter",fstype=~"ext.?|xfs"}-node_filesystem_free_bytes{job="node_exporter",fstype=~"ext.?|xfs"}) *100/(node_filesystem_avail_bytes {job="node_exporter",fstype=~"ext.?|xfs"}+(node_filesystem_size_bytes{job="node_exporter",fstype=~"ext.?|xfs"}-node_filesystem_free_bytes{job="node_exporter",fstype=~"ext.?|xfs"})))by(hypervisor_hostname)"""

        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)
        
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))

        return self.convert_host_response_json(res)


    @fill_black_list
    def get_host_network(self, start, end):
        up_query = """sum by (instance)(increase(node_network_transmit_bytes_total{job="node_exporter",device!~"lo|docker.*"}[60m]))"""
        
        print(start, end)
        up_res = self.query_vector_by_query_range(up_query, start, end, round((end-start)/POINT_COUNT))        
        
        down_query = """sum by (instance)(increase(node_network_receive_bytes_total{job="node_exporter",device!~"lo|docker.*"}[60m]))"""
        
        down_res = self.query_vector_by_query_range(down_query, start, end, round((end-start)/POINT_COUNT))
        
        return self.convert_host_network_response_json(up_res, down_res)
        
    def get_host_network_up(self, start, end):
        query = """sum by (instance)(increase(node_network_transmit_bytes_total{job="node_exporter",device!~"lo|docker.*"}[60m]))"""
        
        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)
        
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))

        return self.convert_host_response_json(res)

    def get_host_network_down(self, start, end):
        query = """sum by (instance)(increase(node_network_receive_bytes_total{job="node_exporter",device!~"lo|docker.*"}[60m]))"""
        
        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)
        
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))

        return self.convert_host_response_json(res)

    def get_host_network_status(self):
        query = """node_network_status"""
        d     = self.query_vector_by_query(query)
        res   = {}

        if d:
            for ins_dict in d:
                device    = ins_dict["metric"]["device"]
                ipaddress = ins_dict["metric"]["instance"].split(":")[0]
                status    = ins_dict["value"][-1]

                res[device + ipaddress] = {"device": device,
                                           "ipaddress": ipaddress,
                                           "status": status}

        return res

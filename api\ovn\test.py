from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl

# TCP 连接字符串
nb_ip = "************"
nb_port = 6641
nb_connection_str = "tcp:{}:{}".format(nb_ip, nb_port)

# 连接到 OVN Northbound 数据库
conn_nb = connection.Connection(
    idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
    timeout=10
)
nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)

# 示例：创建一个逻辑交换机
def create_logical_switch(switch_name):
    with nb_api.transaction(check_error=True) as txn:
        txn.add(nb_api.ls_add(switch_name))
        print(f"Logical switch {switch_name} created")

# 示例：列出所有逻辑交换机
def list_logical_switches():
    switches = nb_api.ls_list().execute(check_error=True)
    for switch in switches:
        print(f"Logical switch: {switch.name}")

# 示例：删除一个逻辑交换机
def delete_logical_switch(switch_name):
    with nb_api.transaction(check_error=True) as txn:
        txn.add(nb_api.ls_del(switch_name))
        print(f"Logical switch {switch_name} deleted")

# 示例调用
create_logical_switch("test_switch")
list_logical_switches()
#delete_logical_switch("test_switch")
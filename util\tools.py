
import time
import uuid


import hashlib

def generate_unique_port_name(prefix='qvo', max_length=15):
    """
    生成符合长度限制的唯一端口名称

    :param prefix: 名称前缀（默认'qvo'）
    :param max_length: 最大允许长度（默认15，需≥8）
    :return: 格式为"[前缀]-[哈希]"的标准化名称
    """
    # 参数校验
    if max_length < 8:
        raise ValueError("max_length must be at least 8")
    
    # 生成唯一标识（UUID + 时间戳双重保障）
    unique_seed = f"{uuid.uuid4()}-{time.time_ns()}"
    unique_hash = hashlib.md5(unique_seed.encode()).hexdigest()
    
    # 计算可用前缀长度（至少保留1字符前缀）
    prefix_max_len = max(1, max_length - 7)  # 7 = 1分隔符 + 6哈希
    clean_prefix = ''.join(c for c in prefix if c.isalnum())[:prefix_max_len]
    
    # 组合名称（格式：前缀-6位哈希）
    return f"{clean_prefix}-{unique_hash[:6]}"[:max_length]


def convert_to_bytes(value, unit):
    """
    将给定的存储大小转换为字节（Bytes）
    
    :param value: 数值或数字字符串，例如 20、"20"、"3.14"
    :param unit: 单位 ['B', 'KB', 'MB', 'GB']
    :return: 转换后的字节数（int 或 float）
    """
    unit = unit.upper()
    units = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 ** 2,
        'GB': 1024 ** 3
    }

    # 如果是字符串，尝试转成数字
    if isinstance(value, str):
        try:
            # 判断是否为整数还是浮点数
            if '.' in value:
                value = float(value)
            else:
                value = int(value)
        except ValueError:
            raise ValueError(f"Invalid numeric string: {value}")

    if not isinstance(value, (int, float)):
        raise TypeError("Value must be a number or numeric string")

    if unit not in units:
        raise ValueError(f"Unsupported unit: {unit}. Use one of {list(units.keys())}")

    return value * units[unit]
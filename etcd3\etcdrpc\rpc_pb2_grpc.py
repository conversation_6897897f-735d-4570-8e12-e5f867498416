# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from etcd3.etcdrpc import rpc_pb2 as rpc__pb2


class KVStub(object):
  """for grpc-gateway

  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Range = channel.unary_unary(
        '/etcdserverpb.KV/Range',
        request_serializer=rpc__pb2.RangeRequest.SerializeToString,
        response_deserializer=rpc__pb2.RangeResponse.FromString,
        )
    self.Put = channel.unary_unary(
        '/etcdserverpb.KV/Put',
        request_serializer=rpc__pb2.PutRequest.SerializeToString,
        response_deserializer=rpc__pb2.PutResponse.FromString,
        )
    self.DeleteRange = channel.unary_unary(
        '/etcdserverpb.KV/DeleteRange',
        request_serializer=rpc__pb2.DeleteRangeRequest.SerializeToString,
        response_deserializer=rpc__pb2.DeleteRangeResponse.FromString,
        )
    self.Txn = channel.unary_unary(
        '/etcdserverpb.KV/Txn',
        request_serializer=rpc__pb2.TxnRequest.SerializeToString,
        response_deserializer=rpc__pb2.TxnResponse.FromString,
        )
    self.Compact = channel.unary_unary(
        '/etcdserverpb.KV/Compact',
        request_serializer=rpc__pb2.CompactionRequest.SerializeToString,
        response_deserializer=rpc__pb2.CompactionResponse.FromString,
        )


class KVServicer(object):
  """for grpc-gateway

  """

  def Range(self, request, context):
    """Range gets the keys in the range from the key-value store.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Put(self, request, context):
    """Put puts the given key into the key-value store.
    A put request increments the revision of the key-value store
    and generates one event in the event history.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteRange(self, request, context):
    """DeleteRange deletes the given range from the key-value store.
    A delete request increments the revision of the key-value store
    and generates a delete event in the event history for every deleted key.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Txn(self, request, context):
    """Txn processes multiple requests in a single transaction.
    A txn request increments the revision of the key-value store
    and generates events with the same revision for every completed request.
    It is not allowed to modify the same key several times within one txn.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Compact(self, request, context):
    """Compact compacts the event history in the etcd key-value store. The key-value
    store should be periodically compacted or the event history will continue to grow
    indefinitely.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_KVServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Range': grpc.unary_unary_rpc_method_handler(
          servicer.Range,
          request_deserializer=rpc__pb2.RangeRequest.FromString,
          response_serializer=rpc__pb2.RangeResponse.SerializeToString,
      ),
      'Put': grpc.unary_unary_rpc_method_handler(
          servicer.Put,
          request_deserializer=rpc__pb2.PutRequest.FromString,
          response_serializer=rpc__pb2.PutResponse.SerializeToString,
      ),
      'DeleteRange': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteRange,
          request_deserializer=rpc__pb2.DeleteRangeRequest.FromString,
          response_serializer=rpc__pb2.DeleteRangeResponse.SerializeToString,
      ),
      'Txn': grpc.unary_unary_rpc_method_handler(
          servicer.Txn,
          request_deserializer=rpc__pb2.TxnRequest.FromString,
          response_serializer=rpc__pb2.TxnResponse.SerializeToString,
      ),
      'Compact': grpc.unary_unary_rpc_method_handler(
          servicer.Compact,
          request_deserializer=rpc__pb2.CompactionRequest.FromString,
          response_serializer=rpc__pb2.CompactionResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'etcdserverpb.KV', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class WatchStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Watch = channel.stream_stream(
        '/etcdserverpb.Watch/Watch',
        request_serializer=rpc__pb2.WatchRequest.SerializeToString,
        response_deserializer=rpc__pb2.WatchResponse.FromString,
        )


class WatchServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Watch(self, request_iterator, context):
    """Watch watches for events happening or that have happened. Both input and output
    are streams; the input stream is for creating and canceling watchers and the output
    stream sends events. One watch RPC can watch on multiple key ranges, streaming events
    for several watches at once. The entire event history can be watched starting from the
    last compaction revision.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_WatchServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Watch': grpc.stream_stream_rpc_method_handler(
          servicer.Watch,
          request_deserializer=rpc__pb2.WatchRequest.FromString,
          response_serializer=rpc__pb2.WatchResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'etcdserverpb.Watch', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class LeaseStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.LeaseGrant = channel.unary_unary(
        '/etcdserverpb.Lease/LeaseGrant',
        request_serializer=rpc__pb2.LeaseGrantRequest.SerializeToString,
        response_deserializer=rpc__pb2.LeaseGrantResponse.FromString,
        )
    self.LeaseRevoke = channel.unary_unary(
        '/etcdserverpb.Lease/LeaseRevoke',
        request_serializer=rpc__pb2.LeaseRevokeRequest.SerializeToString,
        response_deserializer=rpc__pb2.LeaseRevokeResponse.FromString,
        )
    self.LeaseKeepAlive = channel.stream_stream(
        '/etcdserverpb.Lease/LeaseKeepAlive',
        request_serializer=rpc__pb2.LeaseKeepAliveRequest.SerializeToString,
        response_deserializer=rpc__pb2.LeaseKeepAliveResponse.FromString,
        )
    self.LeaseTimeToLive = channel.unary_unary(
        '/etcdserverpb.Lease/LeaseTimeToLive',
        request_serializer=rpc__pb2.LeaseTimeToLiveRequest.SerializeToString,
        response_deserializer=rpc__pb2.LeaseTimeToLiveResponse.FromString,
        )
    self.LeaseLeases = channel.unary_unary(
        '/etcdserverpb.Lease/LeaseLeases',
        request_serializer=rpc__pb2.LeaseLeasesRequest.SerializeToString,
        response_deserializer=rpc__pb2.LeaseLeasesResponse.FromString,
        )


class LeaseServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def LeaseGrant(self, request, context):
    """LeaseGrant creates a lease which expires if the server does not receive a keepAlive
    within a given time to live period. All keys attached to the lease will be expired and
    deleted if the lease expires. Each expired key generates a delete event in the event history.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def LeaseRevoke(self, request, context):
    """LeaseRevoke revokes a lease. All keys attached to the lease will expire and be deleted.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def LeaseKeepAlive(self, request_iterator, context):
    """LeaseKeepAlive keeps the lease alive by streaming keep alive requests from the client
    to the server and streaming keep alive responses from the server to the client.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def LeaseTimeToLive(self, request, context):
    """LeaseTimeToLive retrieves lease information.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def LeaseLeases(self, request, context):
    """LeaseLeases lists all existing leases.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_LeaseServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'LeaseGrant': grpc.unary_unary_rpc_method_handler(
          servicer.LeaseGrant,
          request_deserializer=rpc__pb2.LeaseGrantRequest.FromString,
          response_serializer=rpc__pb2.LeaseGrantResponse.SerializeToString,
      ),
      'LeaseRevoke': grpc.unary_unary_rpc_method_handler(
          servicer.LeaseRevoke,
          request_deserializer=rpc__pb2.LeaseRevokeRequest.FromString,
          response_serializer=rpc__pb2.LeaseRevokeResponse.SerializeToString,
      ),
      'LeaseKeepAlive': grpc.stream_stream_rpc_method_handler(
          servicer.LeaseKeepAlive,
          request_deserializer=rpc__pb2.LeaseKeepAliveRequest.FromString,
          response_serializer=rpc__pb2.LeaseKeepAliveResponse.SerializeToString,
      ),
      'LeaseTimeToLive': grpc.unary_unary_rpc_method_handler(
          servicer.LeaseTimeToLive,
          request_deserializer=rpc__pb2.LeaseTimeToLiveRequest.FromString,
          response_serializer=rpc__pb2.LeaseTimeToLiveResponse.SerializeToString,
      ),
      'LeaseLeases': grpc.unary_unary_rpc_method_handler(
          servicer.LeaseLeases,
          request_deserializer=rpc__pb2.LeaseLeasesRequest.FromString,
          response_serializer=rpc__pb2.LeaseLeasesResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'etcdserverpb.Lease', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class ClusterStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.MemberAdd = channel.unary_unary(
        '/etcdserverpb.Cluster/MemberAdd',
        request_serializer=rpc__pb2.MemberAddRequest.SerializeToString,
        response_deserializer=rpc__pb2.MemberAddResponse.FromString,
        )
    self.MemberRemove = channel.unary_unary(
        '/etcdserverpb.Cluster/MemberRemove',
        request_serializer=rpc__pb2.MemberRemoveRequest.SerializeToString,
        response_deserializer=rpc__pb2.MemberRemoveResponse.FromString,
        )
    self.MemberUpdate = channel.unary_unary(
        '/etcdserverpb.Cluster/MemberUpdate',
        request_serializer=rpc__pb2.MemberUpdateRequest.SerializeToString,
        response_deserializer=rpc__pb2.MemberUpdateResponse.FromString,
        )
    self.MemberList = channel.unary_unary(
        '/etcdserverpb.Cluster/MemberList',
        request_serializer=rpc__pb2.MemberListRequest.SerializeToString,
        response_deserializer=rpc__pb2.MemberListResponse.FromString,
        )


class ClusterServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def MemberAdd(self, request, context):
    """MemberAdd adds a member into the cluster.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def MemberRemove(self, request, context):
    """MemberRemove removes an existing member from the cluster.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def MemberUpdate(self, request, context):
    """MemberUpdate updates the member configuration.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def MemberList(self, request, context):
    """MemberList lists all the members in the cluster.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ClusterServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'MemberAdd': grpc.unary_unary_rpc_method_handler(
          servicer.MemberAdd,
          request_deserializer=rpc__pb2.MemberAddRequest.FromString,
          response_serializer=rpc__pb2.MemberAddResponse.SerializeToString,
      ),
      'MemberRemove': grpc.unary_unary_rpc_method_handler(
          servicer.MemberRemove,
          request_deserializer=rpc__pb2.MemberRemoveRequest.FromString,
          response_serializer=rpc__pb2.MemberRemoveResponse.SerializeToString,
      ),
      'MemberUpdate': grpc.unary_unary_rpc_method_handler(
          servicer.MemberUpdate,
          request_deserializer=rpc__pb2.MemberUpdateRequest.FromString,
          response_serializer=rpc__pb2.MemberUpdateResponse.SerializeToString,
      ),
      'MemberList': grpc.unary_unary_rpc_method_handler(
          servicer.MemberList,
          request_deserializer=rpc__pb2.MemberListRequest.FromString,
          response_serializer=rpc__pb2.MemberListResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'etcdserverpb.Cluster', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class MaintenanceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Alarm = channel.unary_unary(
        '/etcdserverpb.Maintenance/Alarm',
        request_serializer=rpc__pb2.AlarmRequest.SerializeToString,
        response_deserializer=rpc__pb2.AlarmResponse.FromString,
        )
    self.Status = channel.unary_unary(
        '/etcdserverpb.Maintenance/Status',
        request_serializer=rpc__pb2.StatusRequest.SerializeToString,
        response_deserializer=rpc__pb2.StatusResponse.FromString,
        )
    self.Defragment = channel.unary_unary(
        '/etcdserverpb.Maintenance/Defragment',
        request_serializer=rpc__pb2.DefragmentRequest.SerializeToString,
        response_deserializer=rpc__pb2.DefragmentResponse.FromString,
        )
    self.Hash = channel.unary_unary(
        '/etcdserverpb.Maintenance/Hash',
        request_serializer=rpc__pb2.HashRequest.SerializeToString,
        response_deserializer=rpc__pb2.HashResponse.FromString,
        )
    self.HashKV = channel.unary_unary(
        '/etcdserverpb.Maintenance/HashKV',
        request_serializer=rpc__pb2.HashKVRequest.SerializeToString,
        response_deserializer=rpc__pb2.HashKVResponse.FromString,
        )
    self.Snapshot = channel.unary_stream(
        '/etcdserverpb.Maintenance/Snapshot',
        request_serializer=rpc__pb2.SnapshotRequest.SerializeToString,
        response_deserializer=rpc__pb2.SnapshotResponse.FromString,
        )
    self.MoveLeader = channel.unary_unary(
        '/etcdserverpb.Maintenance/MoveLeader',
        request_serializer=rpc__pb2.MoveLeaderRequest.SerializeToString,
        response_deserializer=rpc__pb2.MoveLeaderResponse.FromString,
        )


class MaintenanceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Alarm(self, request, context):
    """Alarm activates, deactivates, and queries alarms regarding cluster health.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Status(self, request, context):
    """Status gets the status of the member.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Defragment(self, request, context):
    """Defragment defragments a member's backend database to recover storage space.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Hash(self, request, context):
    """Hash computes the hash of the KV's backend.
    This is designed for testing; do not use this in production when there
    are ongoing transactions.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def HashKV(self, request, context):
    """HashKV computes the hash of all MVCC keys up to a given revision.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Snapshot(self, request, context):
    """Snapshot sends a snapshot of the entire backend from a member over a stream to a client.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def MoveLeader(self, request, context):
    """MoveLeader requests current leader node to transfer its leadership to transferee.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MaintenanceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Alarm': grpc.unary_unary_rpc_method_handler(
          servicer.Alarm,
          request_deserializer=rpc__pb2.AlarmRequest.FromString,
          response_serializer=rpc__pb2.AlarmResponse.SerializeToString,
      ),
      'Status': grpc.unary_unary_rpc_method_handler(
          servicer.Status,
          request_deserializer=rpc__pb2.StatusRequest.FromString,
          response_serializer=rpc__pb2.StatusResponse.SerializeToString,
      ),
      'Defragment': grpc.unary_unary_rpc_method_handler(
          servicer.Defragment,
          request_deserializer=rpc__pb2.DefragmentRequest.FromString,
          response_serializer=rpc__pb2.DefragmentResponse.SerializeToString,
      ),
      'Hash': grpc.unary_unary_rpc_method_handler(
          servicer.Hash,
          request_deserializer=rpc__pb2.HashRequest.FromString,
          response_serializer=rpc__pb2.HashResponse.SerializeToString,
      ),
      'HashKV': grpc.unary_unary_rpc_method_handler(
          servicer.HashKV,
          request_deserializer=rpc__pb2.HashKVRequest.FromString,
          response_serializer=rpc__pb2.HashKVResponse.SerializeToString,
      ),
      'Snapshot': grpc.unary_stream_rpc_method_handler(
          servicer.Snapshot,
          request_deserializer=rpc__pb2.SnapshotRequest.FromString,
          response_serializer=rpc__pb2.SnapshotResponse.SerializeToString,
      ),
      'MoveLeader': grpc.unary_unary_rpc_method_handler(
          servicer.MoveLeader,
          request_deserializer=rpc__pb2.MoveLeaderRequest.FromString,
          response_serializer=rpc__pb2.MoveLeaderResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'etcdserverpb.Maintenance', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class AuthStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.AuthEnable = channel.unary_unary(
        '/etcdserverpb.Auth/AuthEnable',
        request_serializer=rpc__pb2.AuthEnableRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthEnableResponse.FromString,
        )
    self.AuthDisable = channel.unary_unary(
        '/etcdserverpb.Auth/AuthDisable',
        request_serializer=rpc__pb2.AuthDisableRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthDisableResponse.FromString,
        )
    self.Authenticate = channel.unary_unary(
        '/etcdserverpb.Auth/Authenticate',
        request_serializer=rpc__pb2.AuthenticateRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthenticateResponse.FromString,
        )
    self.UserAdd = channel.unary_unary(
        '/etcdserverpb.Auth/UserAdd',
        request_serializer=rpc__pb2.AuthUserAddRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserAddResponse.FromString,
        )
    self.UserGet = channel.unary_unary(
        '/etcdserverpb.Auth/UserGet',
        request_serializer=rpc__pb2.AuthUserGetRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserGetResponse.FromString,
        )
    self.UserList = channel.unary_unary(
        '/etcdserverpb.Auth/UserList',
        request_serializer=rpc__pb2.AuthUserListRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserListResponse.FromString,
        )
    self.UserDelete = channel.unary_unary(
        '/etcdserverpb.Auth/UserDelete',
        request_serializer=rpc__pb2.AuthUserDeleteRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserDeleteResponse.FromString,
        )
    self.UserChangePassword = channel.unary_unary(
        '/etcdserverpb.Auth/UserChangePassword',
        request_serializer=rpc__pb2.AuthUserChangePasswordRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserChangePasswordResponse.FromString,
        )
    self.UserGrantRole = channel.unary_unary(
        '/etcdserverpb.Auth/UserGrantRole',
        request_serializer=rpc__pb2.AuthUserGrantRoleRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserGrantRoleResponse.FromString,
        )
    self.UserRevokeRole = channel.unary_unary(
        '/etcdserverpb.Auth/UserRevokeRole',
        request_serializer=rpc__pb2.AuthUserRevokeRoleRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthUserRevokeRoleResponse.FromString,
        )
    self.RoleAdd = channel.unary_unary(
        '/etcdserverpb.Auth/RoleAdd',
        request_serializer=rpc__pb2.AuthRoleAddRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthRoleAddResponse.FromString,
        )
    self.RoleGet = channel.unary_unary(
        '/etcdserverpb.Auth/RoleGet',
        request_serializer=rpc__pb2.AuthRoleGetRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthRoleGetResponse.FromString,
        )
    self.RoleList = channel.unary_unary(
        '/etcdserverpb.Auth/RoleList',
        request_serializer=rpc__pb2.AuthRoleListRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthRoleListResponse.FromString,
        )
    self.RoleDelete = channel.unary_unary(
        '/etcdserverpb.Auth/RoleDelete',
        request_serializer=rpc__pb2.AuthRoleDeleteRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthRoleDeleteResponse.FromString,
        )
    self.RoleGrantPermission = channel.unary_unary(
        '/etcdserverpb.Auth/RoleGrantPermission',
        request_serializer=rpc__pb2.AuthRoleGrantPermissionRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthRoleGrantPermissionResponse.FromString,
        )
    self.RoleRevokePermission = channel.unary_unary(
        '/etcdserverpb.Auth/RoleRevokePermission',
        request_serializer=rpc__pb2.AuthRoleRevokePermissionRequest.SerializeToString,
        response_deserializer=rpc__pb2.AuthRoleRevokePermissionResponse.FromString,
        )


class AuthServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def AuthEnable(self, request, context):
    """AuthEnable enables authentication.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AuthDisable(self, request, context):
    """AuthDisable disables authentication.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Authenticate(self, request, context):
    """Authenticate processes an authenticate request.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserAdd(self, request, context):
    """UserAdd adds a new user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserGet(self, request, context):
    """UserGet gets detailed user information.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserList(self, request, context):
    """UserList gets a list of all users.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserDelete(self, request, context):
    """UserDelete deletes a specified user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserChangePassword(self, request, context):
    """UserChangePassword changes the password of a specified user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserGrantRole(self, request, context):
    """UserGrant grants a role to a specified user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UserRevokeRole(self, request, context):
    """UserRevokeRole revokes a role of specified user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RoleAdd(self, request, context):
    """RoleAdd adds a new role.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RoleGet(self, request, context):
    """RoleGet gets detailed role information.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RoleList(self, request, context):
    """RoleList gets lists of all roles.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RoleDelete(self, request, context):
    """RoleDelete deletes a specified role.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RoleGrantPermission(self, request, context):
    """RoleGrantPermission grants a permission of a specified key or range to a specified role.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RoleRevokePermission(self, request, context):
    """RoleRevokePermission revokes a key or range permission of a specified role.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_AuthServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'AuthEnable': grpc.unary_unary_rpc_method_handler(
          servicer.AuthEnable,
          request_deserializer=rpc__pb2.AuthEnableRequest.FromString,
          response_serializer=rpc__pb2.AuthEnableResponse.SerializeToString,
      ),
      'AuthDisable': grpc.unary_unary_rpc_method_handler(
          servicer.AuthDisable,
          request_deserializer=rpc__pb2.AuthDisableRequest.FromString,
          response_serializer=rpc__pb2.AuthDisableResponse.SerializeToString,
      ),
      'Authenticate': grpc.unary_unary_rpc_method_handler(
          servicer.Authenticate,
          request_deserializer=rpc__pb2.AuthenticateRequest.FromString,
          response_serializer=rpc__pb2.AuthenticateResponse.SerializeToString,
      ),
      'UserAdd': grpc.unary_unary_rpc_method_handler(
          servicer.UserAdd,
          request_deserializer=rpc__pb2.AuthUserAddRequest.FromString,
          response_serializer=rpc__pb2.AuthUserAddResponse.SerializeToString,
      ),
      'UserGet': grpc.unary_unary_rpc_method_handler(
          servicer.UserGet,
          request_deserializer=rpc__pb2.AuthUserGetRequest.FromString,
          response_serializer=rpc__pb2.AuthUserGetResponse.SerializeToString,
      ),
      'UserList': grpc.unary_unary_rpc_method_handler(
          servicer.UserList,
          request_deserializer=rpc__pb2.AuthUserListRequest.FromString,
          response_serializer=rpc__pb2.AuthUserListResponse.SerializeToString,
      ),
      'UserDelete': grpc.unary_unary_rpc_method_handler(
          servicer.UserDelete,
          request_deserializer=rpc__pb2.AuthUserDeleteRequest.FromString,
          response_serializer=rpc__pb2.AuthUserDeleteResponse.SerializeToString,
      ),
      'UserChangePassword': grpc.unary_unary_rpc_method_handler(
          servicer.UserChangePassword,
          request_deserializer=rpc__pb2.AuthUserChangePasswordRequest.FromString,
          response_serializer=rpc__pb2.AuthUserChangePasswordResponse.SerializeToString,
      ),
      'UserGrantRole': grpc.unary_unary_rpc_method_handler(
          servicer.UserGrantRole,
          request_deserializer=rpc__pb2.AuthUserGrantRoleRequest.FromString,
          response_serializer=rpc__pb2.AuthUserGrantRoleResponse.SerializeToString,
      ),
      'UserRevokeRole': grpc.unary_unary_rpc_method_handler(
          servicer.UserRevokeRole,
          request_deserializer=rpc__pb2.AuthUserRevokeRoleRequest.FromString,
          response_serializer=rpc__pb2.AuthUserRevokeRoleResponse.SerializeToString,
      ),
      'RoleAdd': grpc.unary_unary_rpc_method_handler(
          servicer.RoleAdd,
          request_deserializer=rpc__pb2.AuthRoleAddRequest.FromString,
          response_serializer=rpc__pb2.AuthRoleAddResponse.SerializeToString,
      ),
      'RoleGet': grpc.unary_unary_rpc_method_handler(
          servicer.RoleGet,
          request_deserializer=rpc__pb2.AuthRoleGetRequest.FromString,
          response_serializer=rpc__pb2.AuthRoleGetResponse.SerializeToString,
      ),
      'RoleList': grpc.unary_unary_rpc_method_handler(
          servicer.RoleList,
          request_deserializer=rpc__pb2.AuthRoleListRequest.FromString,
          response_serializer=rpc__pb2.AuthRoleListResponse.SerializeToString,
      ),
      'RoleDelete': grpc.unary_unary_rpc_method_handler(
          servicer.RoleDelete,
          request_deserializer=rpc__pb2.AuthRoleDeleteRequest.FromString,
          response_serializer=rpc__pb2.AuthRoleDeleteResponse.SerializeToString,
      ),
      'RoleGrantPermission': grpc.unary_unary_rpc_method_handler(
          servicer.RoleGrantPermission,
          request_deserializer=rpc__pb2.AuthRoleGrantPermissionRequest.FromString,
          response_serializer=rpc__pb2.AuthRoleGrantPermissionResponse.SerializeToString,
      ),
      'RoleRevokePermission': grpc.unary_unary_rpc_method_handler(
          servicer.RoleRevokePermission,
          request_deserializer=rpc__pb2.AuthRoleRevokePermissionRequest.FromString,
          response_serializer=rpc__pb2.AuthRoleRevokePermissionResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'etcdserverpb.Auth', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))

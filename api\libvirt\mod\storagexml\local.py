"""
创建本地目录存储格式的xml文件
"""
import xml.etree.ElementTree as ET
from api.libvirt.utils.vmxml.storage_xml import default_local_pool_xml
from api.libvirt.utils.vmxml.storage_xml import StoragePool, StorageVolume


class local:
    def create_xml(self, form):
        # 创建根元素 <volume>
        volume_elem = ET.Element("volume")

        # 添加 name 子元素
        name_elem = ET.SubElement(volume_elem, "name")
        name_elem.text = form["name"] + "." + form['volume_format']

        # 计算总字节数
        size_bytes = form["capacity"]

        # 添加 capacity 子元素
        capacity_elem = ET.SubElement(volume_elem, "capacity")
        capacity_elem.set("unit", "bytes")
        capacity_elem.text = str(size_bytes)

        # 添加 allocation 子元素，默认为0
        allocation_elem = ET.SubElement(volume_elem, "allocation")
        allocation_elem.set("unit", "bytes")
        allocation_elem.text = "0"  # 按需分配

        # 添加 target 子元素
        target_elem = ET.SubElement(volume_elem, "target")

        # 添加 path 子元素
        path_elem = ET.SubElement(target_elem, "path")
        path_elem.text = form['path'] + "/" + form['name'] + "." + form["volume_format"]

        # 添加 format 子元素
        format_elem = ET.SubElement(target_elem, "format")
        format_elem.set("type", form["volume_format"])

        # 创建 XML 字符串
        volume_xml = ET.tostring(volume_elem, encoding='unicode', method='xml')
        return volume_xml

    @staticmethod
    def transformation_size(size):
        # 计算容量大小（字节）
        # 去除可能的大小写敏感性并获取单位
        size_map = {
            'G': 1024 ** 3,
            'M': 1024 ** 2,
            'K': 1024,
            '': 1  # 没有单位时默认为字节
        }
        size_unit = size[-1].upper()

        # 转换为基本单位（字节）
        size_multiplier = size_map.get(size_unit, size_map[''])

        # 去除单位并转换为整数
        size_value = int(float(size[:-1]))

        # 计算总字节数
        size_bytes = size_value * size_multiplier
        return size_bytes

    def create_pool_xml(self, form):
        pool_xml = default_local_pool_xml(form)
        return pool_xml

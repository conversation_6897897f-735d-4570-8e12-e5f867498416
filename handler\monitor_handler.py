# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.prometheus.client import Client
from api.prometheus.client_alert import Alert<PERSON>lient

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
import time

from model.instances import Clusters, Instances
from model.user import User<PERSON>orm, UserStatusForm
from util.cov import todict, serialize, theevent
from api.grafana.mod.host import HostClient, CephClient
from api.grafana.mod.vm import VmClient
from db.model.user import User
from db.model.event import Event
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

class MonitorHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/monitor/storage/base", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_detail(self):
        timestamp = time.time()
        three_s_ago = timestamp - 30
        r ={}
        client = Client()
        # alertcounts
        client1 = AlertClient()
        res = client1.get_alert_ceph_alert()
        # alertcounts = client.StorageClient.get_storage_alert_counts(client, three_s_ago, timestamp)
        # counts = alertcounts['data']['result'][0]['values'][0][1]
        r["alertcounts"] = len(res)
        # total
        total = client.StorageClient.get_storage_total(client, three_s_ago, timestamp)
        t = total['data']['result'][0]['values'][0][1]
        r['total'] = int(t)
        # used
        used = client.StorageClient.get_storage_used(client, three_s_ago, timestamp)
        u = used['data']['result'][0]['values'][0][1]
        r['used'] = int(u)
        # residue
        residue = client.StorageClient.get_storage_residue(client, three_s_ago, timestamp)
        re = residue['data']['result'][0]['values'][0][1]
        r['residue'] = int(re)
        # status
        status = client.StorageClient.get_storage_status(client, three_s_ago, timestamp)
        s = status['data']['result'][0]['values'][0][1]
        if s =='0':
            r['status'] = "正常"
        else:
            r['status'] = "异常"
        # osd_out
        osd_out = client.StorageClient.get_storage_osd_out(client, three_s_ago, timestamp)
        oo = osd_out['data']['result'][0]['values'][0][1]
        r['osd_out'] = int(oo)

        # osd_in
        osd_in = client.StorageClient.get_storage_osd_in(client, three_s_ago, timestamp)
        oi = osd_in['data']['result'][0]['values'][0][1]
        r['osd_in'] = int(oi)
        # osd_down
        osd_down = client.StorageClient.get_storage_osd_down(client, three_s_ago, timestamp)
        od = osd_down['data']['result'][0]['values'][0][1]
        r['osd_down'] = int(od)
        # osd_up
        osd_up = client.StorageClient.get_storage_osd_up(client, three_s_ago, timestamp)
        ou = osd_up['data']['result'][0]['values'][0][1]
        r['osd_up'] = int(ou)
        return r

    @post(_path="/v1/monitor/storage/iops", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_iops(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.StorageClient.get_storage_iops_r(client, start, end)
        res_c =client.StorageClient.fill_black_query_vector_convert(client, start, end, res, "IOPS读")
        if res_c.get("data") is not None:
            #res_c["data"][0]["list"] = [-float(x) for x in res_c["data"][0]["list"]]
            res_c["data"][0]["list"] = [-float(x) if x is not None else 0.0 for x in res_c["data"][0]["list"]]
        res1 = client.StorageClient.get_storage_iops_w(client, start, end)
        res1_c =client.StorageClient.fill_black_query_vector_convert(client, start, end, res1, "IOPS写")
        r = client.StorageClient.query_vector_montage(client, res_c, res1_c)
        r["unit"] = "io/s"
        return r

    @post(_path="/v1/monitor/storage/throughput", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_throughput(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.StorageClient.get_storage_throughput_r(client, start, end)
        res_c =client.StorageClient.fill_black_query_vector_convert(client, start, end, res, "吞吐量读")
        res1 = client.StorageClient.get_storage_throughput_w(client, start, end)
        res1_c =client.StorageClient.fill_black_query_vector_convert(client, start, end, res1, "吞吐量写")
        r = client.StorageClient.query_vector_montage(client, res_c, res1_c)
        r["unit"] = "B"
        return r

    @post(_path="/v1/monitor/storage/networkup", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_network_up(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        total = client.StorageClient.get_storage_net_up(client, start, end)
        res = client.StorageClient.fill_black_query_vector_convert(client, start, end, total, "网络上行")
        res["unit"] = "B/s"
        return res

    @post(_path="/v1/monitor/storage/networkdown", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_network_down(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        total = client.StorageClient.get_storage_net_down(client, start, end)
        res = client.StorageClient.fill_black_query_vector_convert(client, start, end, total, "网络下行")
        res["unit"] = "B/s"
        return res

    @post(_path="/v1/monitor/storage/nodeior", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_network_nodeior(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        total = client.StorageClient.get_storage_node_io_r(client, start, end)
        res = client.StorageClient.fill_black_query_vector_convert(client, start, end, total, "IOPS读")
        res["unit"] = "io/s"
        return res

    @post(_path="/v1/monitor/storage/nodeiow", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_network_nodeiow(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        total = client.StorageClient.get_storage_node_io_w(client, start, end)
        res = client.StorageClient.fill_black_query_vector_convert(client, start, end, total, "IOPS写")
        res["unit"] = "io/s"
        return res


    @post(_path="/v1/monitor/storage/capacity", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_capacity(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        total = client. StorageClient.get_storage_capacity(client, start, end)
        res =client.StorageClient.fill_black_query_vector_convert(client, start, end, total, "存储使用率")
        res["unit"] = "%"
        return res

    @post(_path="/v1/monitor/storage/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_mem(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client. StorageClient.get_storage_mem(client, start, end)
        r = client. StorageClient.query_vector_montage_mem(client, res)
        r["unit"] = "B"
        return r

    @post(_path="/v1/monitor/storage/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_storage_cpu(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client. StorageClient.get_storage_cpu(client, start, end)
        res["unit"] = "%"
        return res


# class ChartHostHandler(pyrestful.rest.RestHandler):
#     def set_default_headers(self):
#         self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
#         #self.set_header("Access-Control-Allow-Origin", "*")
#         self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
#         self.set_header('Access-Control-Allow-Credentials', 'true')
#         self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
#         #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")
#
#     @post(_path="/v1/chart/host/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
#     def thecloud_get_host_cpu(self, form):
#         """
#         ---
#         tags:
#           - 图表相关接口
#         summary: 查询主机CPU
#         responses:
#           '200':
#             description: 成功
#             content:
#               application/json:
#                 schema:
#                   $ref: '#/components/schemas/UserListModel'
#         """
#
#         host_ip = form.get("ip")
#
#         client = HostClient()
#         res = client.grafana_get_host_cpu(host_ip)
#
#         return res
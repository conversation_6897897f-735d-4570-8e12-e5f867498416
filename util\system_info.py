import subprocess

def get_system_info():
    model = ""
    vendor = ""
    try:
        # 获取系统厂商信息
        vendor_output = subprocess.check_output("dmidecode -s system-manufacturer", shell=True)
        vendor = vendor_output.decode().strip()
        
        # 获取系统型号信息
        model_output = subprocess.check_output("dmidecode -s system-product-name", shell=True)
        model = model_output.decode().strip()
        
        info = {
            "model": model,
            "vendor": vendor
        }
    except Exception as e:
        print(f"无法获取系统信息: {e}")
    
    return info
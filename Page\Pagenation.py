'''
Created on 2022年4月25日

@author: root
'''
import math

class Pagenation(object):
    '''
    classdocs
    '''


    def __init__(self,data_list,page,per_data_num):
        '''
        :param data_list: 所有数据列表
        :param page: 当前要查看的列表页
        :param per_data_num: 每页默认要显示几条
        '''
        self.data_list = data_list
        self.page = page
        self.per_data_num = per_data_num
        
    @property
    def start(self):
        '''
        计算引索的起始位置
        :return:
        '''
        return (self.page - 1) * self.per_data_num
 
    @property
    def end(self):
        '''
        计算引索的结束位置
        :return:
        '''
        return self.page * self.per_data_num
 
    def show(self):
        '''
        切片取数据,展示对应分页的结果
        :return:
        '''
        result = self.data_list[self.start:self.end]
        #for row in result:
        #    print(row)
        return result
    
    def total(self):
        '''
        切片取数据,展示对应分页的结果
        :return:
        '''
        result = math.ceil(len(self.data_list)/self.per_data_num)
        #for row in result:
        #    print(row)
        return result
    
    '''
            
    '''
            



    
    
        
        
        
        
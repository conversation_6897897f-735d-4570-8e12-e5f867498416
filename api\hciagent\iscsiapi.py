import requests


class ISCSIAgentClient:
    def __init__(self, base_url):
        """
        初始化ISCSIClient实例

        :param base_url: API服务器的基础URL地址，例如 "http://your_api_server"
        """
        self.base_url = base_url.rstrip('/')  # 确保基础URL不以斜杠结尾

    def _request(self, method, endpoint, **kwargs):
        """
        发送HTTP请求的内部方法

        :param method: HTTP方法（如 'GET', 'POST'）
        :param endpoint: API端点（如 '/v1/iscsi/discover'）
        :param kwargs: 其他传递给requests.request的参数（如 json, params）
        :return: 返回响应的JSON数据或错误信息
        """
        url = f"{self.base_url}{endpoint}"
        try:
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()  # 如果响应状态码不是200系列，则抛出异常
            return True, response.json()
        except requests.exceptions.RequestException as e:
            return False, str(e)

    def discover_targets(self, target_ip):
        """
        发现iSCSI目标

        :param target_ip: 目标IP地址，例如 "************"
        :return: 返回一个元组 (bool, list)，第一个元素表示操作是否成功，第二个元素是发现的目标IQN列表或错误信息。
        """
        payload = {
            "target_ip": target_ip
        }
        success, result = self._request('POST', '/v1/iscsi/discover', json=payload)

        if success:
            if 'data' in result and isinstance(result['data'], list):
                targets = [target['target_iqn'] for target in result['data']]
                return True, targets
            else:
                return False, result['msg']
        else:
            return False, result

    # 可以在此添加更多与iSCSI相关的API方法
    def login_target(self, form):
        """
        登录到指定的iSCSI目标

        :param target_iqn: 目标的IQN
        :return: 返回一个元组 (bool, str)，第一个元素表示操作是否成功，第二个元素是操作结果或错误信息。
        """
        target_ip = form.get("target_ip", "")
        target_iqn = form.get("target_iqn", "")
        payload = {
            "target_iqn": target_iqn,
            "target_ip": target_ip
        }
        success, result = self._request('POST', '/v1/iscsi/login', json=payload)
        return success, result.get('data') if success else result

    def logout_target(self, form):
        """
        从指定的iSCSI目标登出

        :param target_iqn: 目标的IQN
        :return: 返回一个元组 (bool, str)，第一个元素表示操作是否成功，第二个元素是操作结果或错误信息。
        """
        target_ip = form.get("target_ip", "")
        target_iqn = form.get("target_iqn", "")
        payload = {
            "target_iqn": target_iqn,
            "target_ip": target_ip
        }
        success, result = self._request('POST', '/v1/iscsi/logout', json=payload)
        return success, result.get('msg') if success else result


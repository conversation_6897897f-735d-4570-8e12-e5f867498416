import random
import unittest
from unittest.mock import patch
import libvirt
import os

# 必须用vip访问 不会自动redirct
host = "127.0.0.1"

# Patch the settings module for testing
class TestClient(unittest.TestCase):
    #@patch('host', host)
    def test_libvirt_vm(self):
        from client import Client  # Importing here to apply patches
        from api. libvirt. mod. storage import Storage as StorageClient

        def generate_mac_address():
            """生成随机MAC地址，使用libvirt默认前缀52:54:00   52:54:00:34:34:df"""

            mac = [0x52, 0x54, 0x00,
                   random.randint(0x00, 0xff),
                   random.randint(0x00, 0xff),
                   random.randint(0x00, 0xff)]
            return ':'.join(map(lambda x: "%02x" % x, mac))

        client = Client("***************")

        data = {
            "vm_name": "s-gpu",
            "memory_unit": 2097152,
            "vcpu_unit": 8,
            "disk": [
                {
                    "pool": "sheng",
                    "disk_type": "file",
                    "size": 10,
                    "format": "qcow2",
                    "path": "/var/lib/libvirt/sheng",
                    "disk_name": "s-gpu.qcow2",
                    # "source": "/var/lib/libvirt/sheng/s-gpu.qcow2"
                }
            ],
            "cdrom": [
                {
                    "isMount": "false",
                    "poolName": "",
                    "file": "/var/lib/libvirt/images/ubuntu-24.04.1-desktop-amd64.iso"
                }
            ],
            "interface": [
                {
                    "interface_type": "network",
                    'mac': generate_mac_address(),
                    'network': 'default',
                    'model': 'virtio'
                }
            ],
            "display_protocol": "spice",  #"vnc" 或者 "spice"
            "enable_gpu": False,  # 启用 GPU 支持
            "input_devices": {
                "mouse": "usb",  # 可选 "usb" "ps2"
                "keyboard": "usb"  # 可选 "usb" "ps2"
            }
        }

        disk_list = data.get("disk", [])
        for disk in disk_list:
            # 检查卷是否存在
            form1 = {
                'storage_pool_name': disk.get("pool"),
                'name': ".".join([disk.get("disk_name"), disk.get("format")]),
                'storage_type_code': disk.get('format', 'qcow2'),
                'capacity': disk.get('size'),  # 转换成字节
            }
            if not client.ensure_remote_disk_exists(client, form1):
                return {'status': 'failed', 'message': f"Failed to create volume {form1.get('name')} in pool {form1.get('storage_pool_name')}"}


        #创建虚拟机
        res = client.create_dom_vm(client, data)
        self.assertTrue(res)
        #挂载磁盘
        #res = client.attach_disk(client, "test-an")
        #print(res)

        #快照 还原
        #res = client.create_snapshot(client, "test-an","test-an_snapshot")
        #res = client.restore_snapshot(client, "test-an","test-an_snapshot")
        #assert res 不为空
        
        # self.assertEqual(client.get_alert_url(), "http://test-alert-uri")
        # self.assertEqual(client.get_query_url(), "http://test-query-uri")


if __name__ == '__main__':
    unittest.main() 
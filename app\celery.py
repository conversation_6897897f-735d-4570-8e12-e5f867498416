from celery import Celery
from celery.signals import worker_init
from utils.db import init_db, dbi
from config.settings import REDIS_URL, AMQP_URI, MQ_TYPE, DB_PASSWORD, DB_USERNAME, DB_HOSTNAME, DB_DATABASE
import os

# 定义任务列表
SERVER_TASKS = [
    'app.tasks.disk_tasks',
    'app.tasks.cdrom_tasks',
    'app.tasks.vm_tasks',
    'app.tasks.network_tasks',
    'app.tasks.storage_tasks'
]

AGENT_TASKS = [
    'app.agents.disk_tasks',
    'app.agents.network_tasks',
    'app.agents.storage_tasks',
    'app.agents.vm_tasks'
]

def create_celery_app(tasks):
    """创建 Celery 应用实例"""
    if "rabbitmq" == MQ_TYPE:
        app = Celery('hci_asyn',
                    broker=AMQP_URI,
                    # result_backend = f'db+mysql://{DB_USERNAME}:{DB_PASSWORD}@{DB_HOSTNAME}/{DB_DATABASE}',
                    result_backend = REDIS_URL,
                    include=tasks)
    else:  # redis
        app = Celery('hci_asyn',
                    broker=REDIS_URL,
                    backend=REDIS_URL,
                    include=tasks)
        app.config_from_object('config.settings')
    
    return app

# 根据环境变量选择任务列表
CELERY_MODE = os.environ.get('CELERY_MODE', 'server')
TASKS = AGENT_TASKS if CELERY_MODE == 'agent' else SERVER_TASKS

# 创建默认应用实例
app = create_celery_app(TASKS)

# app.conf.update(
#         CELERY_TASK_SERIALIZER = 'json',
#         CELERY_RESULT_SERIALIZER = 'json',
#         CELERY_ACCEPT_CONTENT=['json'],
#         CELERY_TIMEZONE = 'Europe/Oslo',
#         CELERY_ENABLE_UTC = True
# )

@worker_init.connect
def init_worker(**kwargs):
    init_db()
    
    
if __name__ == '__main__':
    app.start()
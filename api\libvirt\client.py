'''
Created on Mar 1, 2022

@author: maojj
'''

import json
#import settings
#from model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass

import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
import libvirt
import sys

    
class Client(object):
    
    host = None
    conn = None
    
    def __init__(self, host=None):

        self.host = host
        self.init_connect()
        
        package_perfix= "api.libvirt.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    #self.__dict__[attribute_name] = attribute

                    for method_name in dir(attribute):
                        method = getattr(attribute, method_name)
                        if callable(method):
                            self.__dict__[method_name] = method


    def init_connect(self):
        # 连接到 libvirtd 服务
        if self.host is None:
            conn_str = 'qemu:///system'
        else:
            conn_str = 'qemu+tcp://' + self.host + '/system'
        self.conn = libvirt.open(conn_str)
        if self.conn is None:
            print(f'Failed to open connection to qemu+tcp://{ self.host }/system', file=sys.stderr)
        else:
            print(f'Successfully connected to qemu+tcp://{ self.host }/system')


    def close_connection(self):
        # 关闭连接
        if self.conn is not None:
            self.conn.close()
            print("Connection to libvirt closed")
            self.conn = None

    def __del__(self):
        self.close_connection()
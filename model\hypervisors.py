# -*- coding: utf-8 -*-


from dataclasses import dataclass
from sqlalchemy.engine import strategies
from pygments.lexers import int_fiction



class Cluster:
    name : str
    
class ClusterEditForm:
    id : int
    name: str
    
class ClusterDeleteForm:
    id : int

class ClusterHostForm(object):
    id : int
    hostname : str

class DeleteHostCluster(object):
    name : str

@dataclass
class Hypervisor(object):
    id : int
    hypervisor_hostname : str
    state : str
    status : str


@dataclass
class HypervisorStatistics(object):
    count : int
    vcpus : int
    memory_mb : int
    local_gb : int
    vcpus_used : int
    memory_mb_used : int
    local_gb_used : int
    free_ram_mb : int
    free_disk_gb : int
    current_workload : int
    running_vms : int
    disk_available_least : int

@dataclass
class HypervisorDetail(object):
    id : int
    hypervisor_hostname : str
    state : str
    status : str
    vcpus : int
    memory_mb : int
    local_gb : int
    vcpus_used: int
    memory_mb_used: int
    local_gb_used : int
    hypervisor_type : str
    hypervisor_version : int
    free_ram_mb : int
    free_disk_gb: int
    current_workload : int
    running_vms : int
    disk_available_least : int
    host_ip: str
    cpu_info: str
    
@dataclass   
class Aggregate(object):
    id : int
    name : str
    created_at : str
    hosts : list
    
@dataclass   
class Aggregates(object):
    id : int
    name : str
    created_at : str

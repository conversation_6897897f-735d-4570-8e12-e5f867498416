# -*- coding: utf-8 -*-

from calendar import c
from unittest import result
from venv import create
import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.ovn.client import Client as OvnClient
from api.libvirt.client import Client as LibvirtClient
from util.cov import todict
from db.model.hci.compute import Domain, Host, Cluster, Pool
from db.model.hci.network import Switch, Router, RouterPort, RouterTable
from app.tasks.vm_tasks import create_vm_callback
from app.tasks.disk_tasks import create_disk
from app.tasks.network_tasks import create_network
from app.tasks.cdrom_tasks import create_cdrom
from math import ceil
from sqlalchemy import asc, desc

from api.log.log import CustomLogger
from celery import Celery, chord
import uuid

import traceback

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()



class HostVmHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    
    #这个接口是从一个主机上创建虚机
    @post(_path="/v5/vm/create/to/host", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_vm_create(self, form):
        role = self.get_cookie('username', "")
        
        vm_name = form.get("vm_name", "")
        host_ip = form.get("host_ip", "")
    
        
        domain_id = str(uuid.uuid4())
        form["domain_id"] = domain_id
        
        #查出host信息 创建虚机空壳 但是不启动 由celery任务来启动
        with self.session_scope() as session:
            
            #根据host_ip查出host信息
            host = session.query(Host).filter(Host.ip == host_ip).first()
            if not host:
                return {"status": "failed", "message": "主机不存在"}
            
            
            domain = Domain()
            domain.id =   domain_id     
            domain.name = vm_name
            domain.host_id = host.id
            domain.cluster_id = host.cluster_id
            domain.pool_id = host.pool_id
            domain.status = "creating"
            domain.vcpu = form.get("vcpu_unit", 1)
            domain.memory = form.get("memory_unit", 512)
            
            
            session.add(domain)
            
        

        
        # 异步调用多任务集合点来完成
        tasks = [create_network.s(domain_id, form),create_cdrom.s(domain_id, form), create_disk.s(domain_id, form)]
        result = chord(tasks)(create_vm_callback.s())
        
        # 异步调用单个任务
        # result_vm = create_vm.apply_async((vm_name, form),link=create_vm_callback.s())

        # 获取任务结果 这里会阻塞
        #print(result_vm.get())        
        
        return {"msg": "已开始创建虚机", "code": 200}


    @post(_path="/v5/host/vm/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_host_vm_list(self, form):
        
        # 从 form 中获取参数
        page = int(form.get('page', 1))
        per_page = int(form.get('pagecount', 10))
        search_str = form.get('search_str', '')
        order_type = form.get('order_type', 'desc').lower()
        order_by = form.get('order_by', 'created_at')
        host_filter = form.get('host_id', '')
        
        if not order_by:
            order_by = 'created_at'

        with self.session_scope() as session:
            query = session.query(Domain)
            
            # 搜索功能，根据指定字段进行搜索（这里假设搜索的字段为 name）
            if search_str:
                query = query.filter(Domain.name.ilike(f"%{search_str}%"))
            
            # 过滤 host
            if host_filter:
                query = query.filter(Domain.host_id == host_filter)

            # 排序功能
            if order_by:
                if order_type == 'asc':
                    query = query.order_by(asc(getattr(Domain, order_by)))
                else:
                    query = query.order_by(desc(getattr(Domain, order_by)))

            # 计算总记录数
            total_records = query.count()
            if total_records == '0':
                total_records = 0

            # 分页
            domains = query.limit(per_page).offset((page - 1) * per_page).all()

            # 将结果转换为字典列表
            domain_list = [Domain.to_dict(domain) for domain in domains]

            # 计算总页数
            # total_pages = ceil(total_records / per_page)

            # 返回分页信息和数据
            return {
                "msg": "获取虚机列表成功",
                "code": 200,
                "total": total_records,
                "data": domain_list
            }
            
        return {"msg": "获取虚机列表失败", "code": 500}
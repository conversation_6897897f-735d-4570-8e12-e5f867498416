# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import <PERSON><PERSON>og<PERSON>

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from util.cov import todict
from db.model.hci.network import Network, Subnet

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class NetworkHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v5/switch/", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_list(self):
        pass

    @post(_path="/v5/store/pool/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_add(self):
        pass

    @post(_path="/v5/store/pool/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_add(self):
        pass

    # Network APIs

    @get(_path="/v5/api/networks", _produces=mediatypes.APPLICATION_JSON)
    def get_networks(self):
        """
        获取所有网络列表
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                networks = session.query(Network).all()
                if networks:
                    networks_list = Network.to_dict_merge(networks)
        except Exception as e:
            # new_logger.log(f"Error getting networks: {e}")
            self.set_status(500)
            self.write({"error": str(e)})

        return {"msg": "创建完成", "code": 200, "data": networks_list}

    @get(_path="/v5/api/networks/{networkId}", _produces=mediatypes.APPLICATION_JSON)
    def get_network(self, network_id):
        """
        获取指定ID的网络详情
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                network = session.query(Network).filter(Network.id == network_id).first()
                if network:
                    network_dict = Network.to_dict_merge(network)
                else:
                    network_dict = {}
        except Exception as e:
            self.set_status(500)
            return {"msg": "获取失败", "code": 500, "error": str(e)}
        return {"msg": "获取成功", "code": 200, "data": network_dict}

    @post(_path="/v5/api/networks", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def create_network(self, form):
        """
        创建一个新的网络
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        name = form.get('name', '')
        status = form.get('status', '')
        network_type = form.get('network_type', '')
        physical_network = form.get('physical_network', ''),
        remark = form.get('remark', '')
        try:
            with self.session_scope() as session:
                network = Network()
                network.name = name,
                network.status = status,
                network.network_type = network_type,
                network.physical_network = physical_network,
                network.remark = remark
                session.add(network)
        except Exception as e:
            self.set_status(502)
            # new_logger.error(f"Error creating network: {e}")
            return {"msg": "创建失败", "code": 500, "error": str(e)}
        return {"msg": "创建成功", "code": 200}

    @put(_path="/v5/api/networks/{networkId}", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def update_network(self, network_id, form):
        """
        更新指定ID的网络信息
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        name = form.get('name', '')
        status = form.get('status', '')
        remark = form.get('remark', '')
        try:
            with self.session_scope() as session:
                network = session.query(Network).filter(Network.id == network_id).first()
                if network:
                    if name != "" and name != network.name:
                        network.name = name
                    if status != "" and name != network.status:
                        network.status = status
                    if remark != "" and remark != network.remark:
                        network.remark = remark
                else:
                    return {"msg": "更新失败", "code": 400, "error": "Network not found"}
        except Exception as e:
            self.set_status(502)
            # 日志后续加入
            return {"msg": "更新失败", "code": 500, "error": str(e)}
        return {"msg": "更新成功", "code": 200}

    @delete(_path="/v5/api/networks/{networkId}", _produces=mediatypes.APPLICATION_JSON)
    def delete_network(self, network_id):
        """
        删除指定ID的网络
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                network = session.query(Network).filter(Network.id == network_id).first()
                if network:
                    session.delete(network)
        except Exception as e:
            # new_logger.error(f"Error deleting network with ID {network_id}: {e}")
            self.set_status(502)
            return {"msg": "删除失败", "code": 500, "error": str(e)}
        return {"msg": "删除成功", "code": 200}

    # Subnet APIs
    @get(_path="/v5/api/networks/{networkId}/subnets", _produces=mediatypes.APPLICATION_JSON)
    def get_subnets(self, network_id):
        """
        获取指定网络下的所有子网列表
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                subnets = session.query(Subnet).filter(Subnet.network_id == network_id).all()
                if subnets:
                    subnets_list = [subnet.to_dict() for subnet in subnets]
                else:
                    subnets_list = []
        except Exception as e:
            self.set_status(500)
            return {"msg": "获取失败", "code": 500, "error": str(e)}
        return {"msg": "获取成功", "code": 200, "data": subnets_list}

    @get(_path="/v5/api/subnets/{subnetId}", _produces=mediatypes.APPLICATION_JSON)
    def get_subnet(self, subnet_id):
        """
        获取指定ID的子网详情
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                subnet = session.query(Subnet).filter(Subnet.id == subnet_id).first()
                if subnet:
                    subnet_dict = subnet.to_dict()
                else:
                    subnet_dict = {}
        except Exception as e:
            self.set_status(500)
            return {"msg": "获取失败", "code": 500, "error": str(e)}
        return {"msg": "获取成功", "code": 200, "data": subnet_dict}

    @post(_path="/v5/api/networks/subnets", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def create_subnet(self, form):
        """
        在指定网络中创建新的子网
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        name = form.get('name', "")
        cidr = form.get('cidr', '')
        network_type = form.get('network_type')
        gateway_ip = form.get('gateway_ip', None)
        allocation_pools = form.get('allocation_pools', [])
        enable_dhcp = form.get('enable_dhcp', False)
        dns_nameservers = form.get('dns_nameservers', [])
        host_routes = form.get('host_routes', [])
        subnet_status = form.get('status', "active")
        try:
            with self.session_scope() as session:
                network = session.query(Network).filter(Network.name == name).first()
                if not network:
                    network = Network()
                    network.name = name
                    network.status = "active"
                    network.network_type = network_type
                    session.add(network)

                subnet = Subnet()
                subnet.network_id = network.id
                subnet.status = subnet_status
                subnet.cidr = cidr
                subnet.gateway_ip = gateway_ip
                subnet.allocation_pools = allocation_pools
                subnet.enable_dhcp = enable_dhcp
                subnet.dns_nameservers = dns_nameservers
                subnet.host_routes = host_routes
                session.add(subnet)
        except Exception as e:
            self.set_status(502)
            return {"msg": "创建失败", "code": 500, "error": str(e)}
        return {"msg": "创建成功", "code": 200}

    @put(_path="/v5/api/subnets/{subnetId}", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def update_subnet(self, subnet_id, form):
        """
        更新指定ID的子网信息
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        cidr = form.get('cidr', "")
        gateway_ip = form.get('gateway_ip', "")
        allocation_pools = form.get('allocation_pools', "")
        enable_dhcp = form.get('enable_dhcp', "")
        dns_nameservers = form.get('dns_nameservers', "")
        host_routes = form.get('host_routes', "")
        try:
            with self.session_scope() as session:
                subnet = session.query(Subnet).filter(Subnet.id == subnet_id).first()
                if subnet:
                    if cidr != "":
                        subnet.cidr = cidr
                    if gateway_ip != "":
                        subnet.gateway_ip = gateway_ip
                    if allocation_pools != "":
                        subnet.allocation_pools = allocation_pools
                    if enable_dhcp != "":
                        subnet.enable_dhcp = enable_dhcp
                    if dns_nameservers != "":
                        subnet.dns_nameservers = dns_nameservers
                    if host_routes != "":
                        subnet.host_routes = host_routes
                else:
                    return {"msg": "更新失败", "code": 400, "error": "Subnet not found"}
        except Exception as e:
            self.set_status(502)
            return {"msg": "更新失败", "code": 500, "error": str(e)}
        return {"msg": "更新成功", "code": 200}

    @delete(_path="/v5/api/subnets/{subnetId}", _produces=mediatypes.APPLICATION_JSON)
    def delete_subnet(self, subnet_id):
        """
        删除指定ID的子网
        """
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        try:
            with self.session_scope() as session:
                subnet = session.query(Subnet).filter(Subnet.id == subnet_id).first()
                if subnet:
                    session.delete(subnet)
                else:
                    return {"msg": "删除失败", "code": 400, "error": "Subnet not found"}
        except Exception as e:
            self.set_status(502)
            return {"msg": "删除失败", "code": 500, "error": str(e)}
        return {"msg": "删除成功", "code": 200}

    # Port Group APIs
    @get(_path="/v5/api/portgroups", _produces=mediatypes.APPLICATION_JSON)
    def get_portgroups(self):
        # 获取所有端口组列表
        pass

    @get(_path="/v5/api/portgroups/{portGroupId}", _produces=mediatypes.APPLICATION_JSON)
    def get_port_group(self, port_group_id):
        # 获取指定ID的端口组详情
        pass

    @post(_path="/v5/api/portgroups", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def create_port_group(self):
        # 创建新的端口组
        pass

    @put(_path="/v5/api/portgroups/{portGroupId}", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def update_port_group(self, port_group_id):
        # 更新指定ID的端口组信息
        pass

    @delete(_path="/v5/api/portgroups/{portGroupId}", _produces=mediatypes.APPLICATION_JSON)
    def delete_port_group(self, port_group_id):
        # 删除指定ID的端口组
        pass

    # DHCP APIs
    @get(_path="/v5/api/dhcp", _produces=mediatypes.APPLICATION_JSON)
    def get_dhcp_config(self):
        # 获取DHCP配置
        pass

    @post(_path="/v5/api/dhcp", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def set_dhcp_config(self):
        # 设置全局DHCP配置（如果适用）
        pass

    @post(_path="/v5/api/subnets/{subnetId}/dhcp", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def set_subnet_dhcp_config(self, subnet_id):
        # 为特定子网启用或配置DHCP
        pass

    # IP 地址管理
    @get(_path="/v5/api/subnets/{subnetId}/ips", _produces=mediatypes.APPLICATION_JSON)
    def get_ips(self, subnet_id):
        # 获取子网内的所有IP地址
        pass

    @post(_path="/v5/api/subnets/{subnetId}/ips", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def allocate_ip(self, subnet_id):
        # 分配新IP地址
        pass

    @delete(_path="/v5/api/ips/{ipId}", _produces=mediatypes.APPLICATION_JSON)
    def release_ip(self, ip_id):
        # 释放指定ID的IP地址
        pass

    # DNS 配置
    @get(_path="/v5/api/dns", _produces=mediatypes.APPLICATION_JSON)
    def get_dns_config(self):
        # 获取DNS配置
        pass

    @post(_path="/v5/api/dns", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def set_dns_config(self):
        # 更新DNS配置
        pass

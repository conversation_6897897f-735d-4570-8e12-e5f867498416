import requests
import json
import settings
from api.grafana.client import Client
import time
import numpy as np

class VmClient(Client):


    def grafana_get_vm_cpu(self, vmid):

        query = """100 - (avg by (instance) (irate(windows_cpu_time_total{instance_id=~"%s",mode="idle"}[2m])) * 100)""" % vmid
        res = self.query_pormQL_range(query)
        #data = json.loads(res)
        if len(res["data"]["result"]) == 0:
            return {"t":[], "u": [], "d":[]}
        
        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]
        
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        #t = [d[0] for d in data]
        
        v = np.around(v, 2)
        return {"v":v.tolist(), "t":t}

    def grafana_get_vm_linux_cpu(self, vmid):

        query = """100 - (avg by (instance) (irate(node_cpu_seconds_total{instance_id=~"%s",mode="idle"}[2m])) * 100)""" % vmid
        res = self.query_pormQL_range(query)
        #data = json.loads(res)
        if len(res["data"]["result"]) == 0:
            return {"t":[], "u": [], "d":[]}

        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]

        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        #t = [d[0] for d in data]

        v = np.around(v, 2)
        return {"v":v.tolist(), "t":t}
    
        
    def grafana_get_vm_mem(self, vmid):
        query = """100.0 - 100 * windows_os_physical_memory_free_bytes{instance_id=~"%s"} / windows_cs_physical_memory_bytes{instance_id=~"%s"}""" % (vmid, vmid)
        #query = """avg(1 - avg(rate(node_cpu_seconds_total{instance="%s:9100",mode="idle"}[30])) by (instance)) * 100""" % ip
        
        res = self.query_pormQL_range(query)

        if len(res["data"]["result"]) == 0:
            return {"t":[], "u": [], "d":[]}        
        
        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]
        
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        v = np.around(v, 2)
        return {"v":v.tolist(), "t":t}

    def grafana_get_vm_linux_mem(self, vmid):
        query = """100.0 - 100 * node_memory_MemAvailable_bytes{instance_id=~"%s"} / node_memory_MemTotal_bytes{instance_id=~"%s"}""" % (vmid, vmid)
        #query = """avg(1 - avg(rate(node_cpu_seconds_total{instance="%s:9100",mode="idle"}[30])) by (instance)) * 100""" % ip

        res = self.query_pormQL_range(query)

        if len(res["data"]["result"]) == 0:
            return {"t":[], "u": [], "d":[]}

        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]

        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        v = np.around(v, 2)
        return {"v":v.tolist(), "t":t}

    def grafana_get_vm_network(self, vmid):
        query = """max by (instance) (irate(windows_net_bytes_received_total{instance_id=~"%s",nic!~'isatap.*|VPN.*'}[2m]))*8""" % vmid
        res = self.query_pormQL_range(query)
        
        if len(res["data"]["result"]) == 0:
            return {"t":[], "u": [], "d":[]}
        
        data = res["data"]["result"][0]["values"]
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        dd = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1])/1000.0/1000.0 for d in d["values"]]
            if dd.tolist():
                dd = dd + np.array(tmp)
            else:
                dd = np.array(tmp)
        
        query = """max by (instance) (irate(windows_net_bytes_sent_total{instance_id=~"%s",nic!~'isatap.*|VPN.*'}[2m]))*8""" % vmid
        res = self.query_pormQL_range(query)
        uu = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1])/1000.0/1000.0 for d in d["values"]]
            if uu.tolist():
                uu = uu + np.array(tmp)
            else:
                uu = np.array(tmp)        
        
        uu = np.around(uu, 2) * - 1
        dd = np.around(dd, 2)        
        return {"t":t, "u": uu.tolist(), "d":dd.tolist()}

    def grafana_get_vm_linux_network(self, vmid):
        query = """max by (instance) (irate(node_network_receive_bytes_total{instance_id=~"%s",nic!~'isatap.*|VPN.*'}[2m]))*8""" % vmid
        res = self.query_pormQL_range(query)

        if len(res["data"]["result"]) == 0:
            return {"t":[], "u": [], "d":[]}

        data = res["data"]["result"][0]["values"]
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        dd = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1])/1000.0/1000.0 for d in d["values"]]
            if dd.tolist():
                dd = dd + np.array(tmp)
            else:
                dd = np.array(tmp)

        query = """max by (instance) (irate(node_network_transmit_bytes_total{instance_id=~"%s",nic!~'isatap.*|VPN.*'}[2m]))*8""" % vmid
        res = self.query_pormQL_range(query)
        uu = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1])/1000.0/1000.0 for d in d["values"]]
            if uu.tolist():
                uu = uu + np.array(tmp)
            else:
                uu = np.array(tmp)

        uu = np.around(uu, 2) * -1
        dd = np.around(dd, 2)
        return {"t":t, "u": uu.tolist(), "d":dd.tolist()}


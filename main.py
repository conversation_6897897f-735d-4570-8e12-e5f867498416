# -*- coding: utf-8 -*-
import sys
import tornado.ioloop
import pyrestful.rest
import tornado

from handler.alerts_rules_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AlertRuleGroupsHandler
from handler.monitor_handler import <PERSON><PERSON>andler
from handler.v2.clone import <PERSON><PERSON><PERSON><PERSON><PERSON>
from handler.v5.template_handler import Temp<PERSON><PERSON><PERSON><PERSON>
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from handler.cluster_handler import <PERSON>lusterHand<PERSON>, ClusterHostHandler
from handler.instances_handler import InstancesHandler
from handler.networks_handler import  NetworksHandler
from handler.images_handler import <PERSON>Handler, ImagesV2Handler
from handler.volumes_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,PassthroughHandler
from handler.task_handler import TaskHandler
from handler.flavors_handler import <PERSON><PERSON><PERSON><PERSON>and<PERSON>, NewFlavorsHandler
from handler.groups_handler import GroupsHandler
from handler.event_handler import EventHandler
from handler.ceph_handler import <PERSON><PERSON><PERSON>andler
from handler.grant_handler import <PERSON><PERSON><PERSON><PERSON>
from handler.chart_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
#from handler.container_handler import Container<PERSON><PERSON><PERSON>
from handler.alerts_handler import AlertsHandler, AlertManagerHandler, AlertCephHandler
from handler.health_handler import HealthHandler
from handler.flow_handler import FlowHandler
from handler.thesc_handler import ThescHandler
from handler.securitygroups_handler import Securitygroupshandler
from handler.config_handler import ConfigHandler
from handler.monitor_host_handler import MonitorHostHandler, MonitorVMHandler


from handler.v5.switch_handler import SwitchHandler
from handler.v5.switch_port_handler import SwitchPortHandler
from handler.v5.store_handler import StorageHandler
from handler.v5.vm_handler import VmHandler
from handler.v5.host_vm_handler import HostVmHandler
from handler.v5.cluster_vm_handler import ClusterVmHandler
from handler.v5.pool_handler import PoolHandler
from handler.v5.cluster_handler import ClusterHandler as HCIClusterHandler
from handler.v5.host_handler import HostHandler
from handler.v5.network_handler import NetworkHandler
from handler.v5.service_handler import ServiceHandler

from handler.v5.ctl.pool import PoolCtlHandler



import tornado.log
import tornado.options
import logging.config
import os
import settings as se

from datetime import datetime
from pytz import timezone
import logging

def timetz(*args):
    return datetime.now(tz).timetuple()

tz = timezone('Asia/Shanghai') # UTC, Asia/Shanghai, Europe/Berlin

logger = logging.getLogger(__name__)

tornado.options.define("access_to_stdout", default=True, help="Log tornado.access to stdout")

"""
logging.config.dictConfig({
    'version': 1,
    'formatters': {
        'default': {
            'class': 'tornado.log.LogFormatter',
            'format': '[%(levelname)1.1s %(asctime)s %(module)s:%(lineno)d] %(message)s',
        },
    },
    'handlers': {
        'default': {
            'class': 'logging.StreamHandler',
            'formatter': 'default',
        },
    },
    'root': {   # settings of root logger.
        'level': 'DEBUG',
        'handlers': ['default'],
        'propagate': False,
    },
})
"""


def init_logging(access_to_stdout=False):
    if access_to_stdout:
        access_log = logging.getLogger()
        #access_log = logging.getLogger('tornado.access')
        #access_log.propagate = False
        # make sure access log is enabled even if error level is WARNING|ERROR
        access_log.setLevel(logging.INFO)
        """
        stdout_handler = logging.StreamHandler(sys.stdout)
        
        my_log_format = '%(color)s::: %(levelname)s %(name)s %(asctime)s ::: %(module)s:%(lineno)d in %(funcName)s :::%(end_color)s\
                 \n %(message)s\n'
        log_formatter = tornado.log.LogFormatter(fmt=my_log_format, color=True)
        formatter = logging.Formatter(
            '[%(levelname)1.1s %(asctime)s.%(msecs)d '
            '%(module)s:%(lineno)d] %(message)s',
            "%Y-%m-%d %H:%M:%S"
        )
                
        stdout_handler.setFormatter(log_formatter)
        access_log.setLevel(logging.INFO)
        access_log.addHandler(stdout_handler)
        """
        
        log_file = se.LOG_PATH
        file_handler = logging.handlers.TimedRotatingFileHandler(log_file, when="d", interval=1, backupCount=30)
        
        logging.Formatter.converter = timetz
        
        log_formatter = logging.Formatter(
            "%(asctime)s;%(module)s;%(levelname)s;%(lineno)d;%(message)s",datefmt='%Y/%m/%d %H:%M:%S',
        )
        file_handler.setFormatter(log_formatter)
        # 将处理器附加到根logger
        #root_logger = logging.getLogger()
        access_log.addHandler(file_handler)
        

def bootstrap():
    tornado.options.parse_command_line(final=True)
    init_logging(tornado.options.options.access_to_stdout)

if __name__ == '__main__':
    try:
        print("Start the service")
        
        bootstrap()
        
        mysettings = dict(
            cookie_secret="some_long_secret_and_other_settins",
            DRIVER = se.DRIVER,
            DB_USERNAME = se.DB_USERNAME,
            DB_PASSWORD = se.DB_PASSWORD,
            DB_HOSTNAME = se.DB_HOSTNAME,
            DB_PORT = se.DB_PORT,
            DB_DATABASE = se.DB_DATABASE
        )
            
        app = pyrestful.rest.RestService([ClusterHandler,
                                          ClusterHostHandler,
                                          InstancesHandler, 
                                          NetworksHandler,
                                          ImagesHandler,
                                          ImagesV2Handler,
                                          VolumesHandler,
                                          SnapshotHandler,
                                          FlavorsHandler,
                                          NewFlavorsHandler,
                                          EventHandler,
                                          GrantHandler,
                                          ChartHostHandler,
                                          ChartVmHandler,
                                          AlertRulesHandler,
                                          AlertRuleGroupsHandler,
                                          ChartCephHandler,
                                          PassthroughHandler,
                                          CephHandler,
                                          GroupsHandler,
                                          AlertsHandler,
                                          ConfigHandler,
                                          MonitorHandler,
                                          MonitorHostHandler,
                                          MonitorVMHandler,
                                          AlertManagerHandler,
                                          AlertCephHandler,
                                          ThescHandler,
                                          HealthHandler,
                                          FlowHandler,
                                          TaskHandler,
                                          Securitygroupshandler,
                                          SwitchHandler,
                                          SwitchPortHandler,
                                          StorageHandler,
                                          TemplateHandler,
                                          VmHandler,
                                          HostVmHandler,
                                          ClusterVmHandler,
                                          PoolHandler,
                                          HCIClusterHandler,
                                          HostHandler,
                                          CloneHandler,
                                          ServiceHandler,
                                          PoolCtlHandler,
                                          NetworkHandler], debug=False, **mysettings)
        '''
        if len(sys.argv) > 1:
            app.listen(sys.argv[1])
        else:
            app.listen(8080)
            
        tornado.ioloop.IOLoop.instance().start()
        '''
        """
        tornado.options.options.logging = "error" # 设置日志登记 
        tornado.options.define('log_file_prefix', default='./logs/DMS.log')  # 存储路径
        tornado.options.define('log_rotate_mode', default='time')  # 切割方式：按时间
        tornado.options.define('log_rotate_when', default='D')     # 切割单位：天
        tornado.options.define('log_rotate_interval', default=1)   # 间隔值：1天
        
        print(tornado.options.options.logging, '打印出为 error -------------------')
        """


        
        http_server = tornado.httpserver.HTTPServer(app)
        http_server.bind(8006)
        http_server.start(5)
            
        io_loop = tornado.ioloop.IOLoop.instance().start()
    except KeyboardInterrupt:
        print("\nStop the service")

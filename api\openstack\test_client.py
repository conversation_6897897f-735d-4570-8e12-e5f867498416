import unittest
from unittest.mock import patch
import settings



# Patch the settings module for testing
class TestClient(unittest.TestCase):
    @patch('settings.AUTH_URI', "http://***************:5000/v3")
    @patch('settings.AUTH_TYPE', "password")
    @patch('settings.PROJECT_DOMAIN_ID', "Default")
    @patch('settings.USER_DOMAIN_ID', "Default")
    @patch('settings.PROJECT_NAME', "admin")
    @patch('settings.USERNAME', "default")
    @patch('settings.PASSWORD', "thecloud2015.1")
    def test_ceph_get_ceph_host(self):
        from client import Client  # Importing here to apply patches

        client = Client()
        res = client.NeutronClient.openstack_get_all_filter_ports(client,"mac_address=fa:16:3e:2b:3a:ba")
        print(res)
        
        #assert res 不为空
        self.assertTrue(res)





if __name__ == '__main__':
    unittest.main()
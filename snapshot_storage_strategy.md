# libvirt虚拟机快照存储策略说明

## 概述

根据libvirt快照的不同类型和存储方式，我们的数据表设计采用了灵活的存储策略，只在需要时创建详细记录，避免数据冗余。

## 快照类型分类

### 1. 按功能分类

- **仅磁盘快照 (disk_only)**：只保存磁盘状态
- **仅内存快照 (memory_only)**：只保存内存状态  
- **完整系统快照 (full_system)**：保存磁盘+内存状态

### 2. 按存储方式分类

- **内部快照 (internal)**：快照数据存储在原始文件中
- **外部快照 (external)**：快照数据存储在独立文件中
- **混合快照 (mixed)**：部分内部，部分外部

## 数据存储策略

### 内部快照 (Internal Snapshot)

```
存储位置：原始磁盘文件内部（如qcow2格式）
数据表记录：
├── Snapshot (主表) ✓ - 记录基本信息
├── SnapshotDisk ✗ - 不需要，无独立文件
└── SnapshotMemory ✗ - 不需要，无独立文件
```

**特点：**
- 快照数据直接存储在qcow2等支持快照的磁盘格式中
- 只需要在主表记录快照的基本信息
- 不需要额外的磁盘或内存记录表
- 管理简单，但性能可能受影响

### 外部磁盘快照 (External Disk Snapshot)

```
存储位置：独立的快照文件
数据表记录：
├── Snapshot (主表) ✓ - 记录基本信息
├── SnapshotDisk ✓ - 记录磁盘快照详细信息
└── SnapshotMemory ✗ - 不涉及内存
```

**特点：**
- 原始磁盘文件变为只读
- 新的写入操作保存到新的快照文件中
- 需要记录快照文件路径、大小等详细信息
- 性能好，但文件管理复杂

### 外部内存快照 (External Memory Snapshot)

```
存储位置：独立的内存状态文件
数据表记录：
├── Snapshot (主表) ✓ - 记录基本信息
├── SnapshotDisk ✗ - 不涉及磁盘
└── SnapshotMemory ✓ - 记录内存快照详细信息
```

**特点：**
- 虚拟机内存状态保存到独立文件
- 需要记录内存文件路径、大小等信息
- 通常与磁盘快照结合使用

### 外部完整系统快照 (External Full System Snapshot)

```
存储位置：磁盘快照文件 + 内存状态文件
数据表记录：
├── Snapshot (主表) ✓ - 记录基本信息
├── SnapshotDisk ✓ - 记录磁盘快照详细信息
└── SnapshotMemory ✓ - 记录内存快照详细信息
```

**特点：**
- 同时创建磁盘和内存的外部快照
- 需要管理多个快照文件
- 提供完整的系统状态保存

## 数据表设计优势

### 1. 避免数据冗余
- 内部快照不创建不必要的详细记录
- 只在外部快照时才记录文件路径等信息

### 2. 灵活的查询策略
```python
# 查询所有快照基本信息
snapshots = Snapshot.objects.filter(virtual_machine=vm)

# 查询外部快照的详细信息
external_snapshots = Snapshot.objects.filter(
    virtual_machine=vm, 
    storage_type__in=['external', 'mixed']
).prefetch_related('disks', 'memory')

# 查询内部快照（无需关联查询）
internal_snapshots = Snapshot.objects.filter(
    virtual_machine=vm, 
    storage_type='internal'
)
```

### 3. 存储空间优化
- 内部快照：主表记录 ~1KB
- 外部磁盘快照：主表 + 磁盘表记录 ~2KB  
- 外部完整快照：主表 + 磁盘表 + 内存表记录 ~3KB

## 实际使用场景

### 场景1：开发测试环境
```python
# 创建内部快照，快速回滚
snapshot_service.create_snapshot(
    vm_id=vm.id,
    snapshot_name='dev-checkpoint-1',
    snapshot_type='full_system',
    storage_type='internal'  # 简单快速
)
```

### 场景2：生产环境备份
```python
# 创建外部快照，不影响性能
snapshot_service.create_snapshot(
    vm_id=vm.id,
    snapshot_name='prod-backup-1',
    snapshot_type='disk_only',
    storage_type='external',
    disk_configs=[{
        'name': 'vda',
        'mode': 'external',
        'source_file': '/backup/prod-vm-backup-1.qcow2'
    }]
)
```

### 场景3：系统升级前保护
```python
# 创建外部完整快照，完整保护
snapshot_service.create_snapshot(
    vm_id=vm.id,
    snapshot_name='pre-upgrade-snapshot',
    snapshot_type='full_system',
    storage_type='external',
    disk_configs=disk_configs,
    memory_config={
        'mode': 'external',
        'file': '/backup/pre-upgrade-memory.save'
    }
)
```

## 性能考虑

### 内部快照
- **优点**：管理简单，文件数量少
- **缺点**：可能影响虚拟机I/O性能
- **适用**：开发测试环境，临时快照

### 外部快照  
- **优点**：性能影响小，便于备份管理
- **缺点**：文件管理复杂，占用更多存储空间
- **适用**：生产环境，长期备份

## 总结

通过区分内部和外部快照，我们的数据表设计实现了：

1. **数据精简**：内部快照只保存必要信息
2. **信息完整**：外部快照记录详细的文件信息
3. **查询高效**：根据存储类型优化查询策略
4. **管理灵活**：支持各种快照使用场景

这种设计既满足了不同场景的需求，又保持了数据库的高效性和可维护性。

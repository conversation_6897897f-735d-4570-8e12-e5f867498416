import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class HostClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_list(self):
        method = "/os-hosts"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["hosts"]:
            ins = from_dict(data_class=Host, data=ins_dict)
            if ins.service == "compute":
                res.append(ins.__dict__)
        return res
    
        
    def openstack_get_host_detail(self, id):
        method = "/flavors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        ins = from_dict(data_class=FlavorDetail, data=d["flavor"])
        return ins.__dict__
    

        
        
# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from model.user import UserForm, UserStatusForm
from util.cov import todict, serialize, theevent
from api.grafana.mod.host import HostClient, CephClient
from api.grafana.mod.vm import VmClient
from db.model.user import User
from db.model.event import Event
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

class ChartCephHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/chart/ceph/iops", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_ceph_ipos(self, form):

        client = CephClient()
        res = client.grafana_get_ceph_iops()

        return res

    @post(_path="/v1/chart/ceph/throughput", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_ceph_throughput(self, form):

        client = CephClient()
        res = client.grafana_get_ceph_throughput()

        return res


class ChartHostHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/chart/host/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_host_cpu(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询主机CPU
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        host_ip = form.get("ip")
        
        client = HostClient()
        res = client.grafana_get_host_cpu(host_ip)

        return res

    @post(_path="/v1/chart/host/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_host_mem(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询主机内存
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        host_ip = form.get("ip")
        
        client = HostClient()
        res = client.grafana_get_host_mem(host_ip)

        return res
    
    @post(_path="/v2/chart/host/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_host_mem_v2(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询主机内存
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        host_ip = form.get("ip")
        
        client = HostClient()
        res = client.grafana_get_host_mem_v2(host_ip)

        return res

    @post(_path="/v1/chart/host/net", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_host_net(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询主机网络
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        host_ip = form.get("ip")
        
        client = HostClient()
        devices = client.grafana_get_host_device(host_ip)
        
        res = client.grafana_get_host_network(host_ip, devices)

        return res

class ChartVmHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/chart/vm/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_vm_cpu(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询虚机CPU
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        vmid = form.get("vmid")
        os_type = form.get("os_type")
        if os_type == "linux":
            client = VmClient()
            res = client.grafana_get_vm_linux_cpu(vmid)
        else:
            client = VmClient()
            res = client.grafana_get_vm_cpu(vmid)

        return res

    @post(_path="/v1/chart/vm/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_host_mem(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询虚机内存
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        vmid = form.get("vmid")
        os_type = form.get("os_type")
        if os_type == "linux":
            client = VmClient()
            res = client.grafana_get_vm_linux_mem(vmid)
        else:
            client = VmClient()
            res = client.grafana_get_vm_mem(vmid)

        return res

    @post(_path="/v1/chart/vm/net", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_vm_net(self, form):
        """
        ---
        tags:
          - 图表相关接口
        summary: 查询虚机网络
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserListModel'
        """
        
        vmid = form.get("vmid")
        os_type = form.get("os_type")
        if os_type == "linux":
            client = VmClient()
            res = client.grafana_get_vm_linux_network(vmid)
        else:
            client = VmClient()
            res = client.grafana_get_vm_network(vmid)

        return res

import time
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.open_vswitch import impl_idl
from ovsdbapp.backend.ovs_idl import connection
from ovsdbapp.schema.ovn_northbound import impl_idl as nb_impl_idl
from sqlalchemy.orm import remote

from api.ovs.client import ClientV2
from client import Client


# TCP 连接字符串
# nb_ip = "************"
# nb_port = 6641
# nb_connection_str = "tcp:{}:{}".format(nb_ip, nb_port)
#
# # 连接到 OVN Northbound 数据库
# conn_nb = connection.Connection(
#     idl=connection.OvsdbIdl.from_server(nb_connection_str, "OVN_Northbound"),
#     timeout=10
# )
# nb_api = nb_impl_idl.OvnNbApiIdlImpl(conn_nb)
#
# # 示例：创建一个逻辑交换机
# def create_logical_switch(switch_name):
#     with nb_api.transaction(check_error=True) as txn:
#         txn.add(nb_api.ls_add(switch_name))
#         print(f"Logical switch {switch_name} created")
#
# # 示例：列出所有逻辑交换机
# def list_logical_switches():
#     switches = nb_api.ls_list().execute(check_error=True)
#     for switch in switches:
#         print(f"Logical switch: {switch.name}")
#
# # 示例：删除一个逻辑交换机
# def delete_logical_switch(switch_name):
#     with nb_api.transaction(check_error=True) as txn:
#         txn.add(nb_api.ls_del(switch_name))
#         print(f"Logical switch {switch_name} deleted")

# def create_switch(ip, name):
#     client = Client(ip)
#     client.create_ovs_bridge(client, name)
#
# def delete_ovs_bridge(ip, name):
#     client = Client(ip)
#     client.delete_ovs_bridge(client, name)
#
def create_port_on_bridge(ip, name, port_name):
    form = {
        "bridge_name": name,
        "tap_name": port_name,
        "vlan_tag": "10",
        "trunk_vlans": ""
    }
    client = Client()
    client.create_tap_interface(client, form)
#
# def del_port_from_bridge(ip, name, port_name):
#     client = Client(ip)
#     client.delete_tap_interface(client, name, port_name)
#
# def get_port_info(ip, form):
#     client = Client(ip)
#     client.get_port_info(client, form)

def create_fenbu(ip, form):
    client = Client(ip)
    print(client.host_ip, ip, client.ovs_api)
    remote_ip = form["remote_ip"]
    client.add_geneve_tunnel(client, "fenbu", remote_ip)

def create_fenbu_2(ip, form):
    client = ClientV2(ip)
    print(client.host_ip, ip, client.ovs_api)
    remote_ip = form["remote_ip"]
    client.add_geneve_tunnel(client, "fenbu", remote_ip)


ip = "**************"
name = "tt"
port_name = "tt-fe026b81"
# form = {
#     "bridge_name": name,
#     "port_name": port_name
# }

# create_switch(ip, "t1")
# delete_ovs_bridge(ip, name)
create_port_on_bridge(ip, name, port_name)
# del_port_from_bridge(ip, name, port_name)
# get_port_info(ip, form)
# get_port_info("**************", form)

# create_fenbu_2("**************", form= {"remote_ip":"**************"})

# create_fenbu("**************", form= {"remote_ip":"**************"})


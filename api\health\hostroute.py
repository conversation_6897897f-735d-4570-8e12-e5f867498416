from api.prometheus.client import Client
import time

name = "主机路由"
weigth = 20

def check():
    
    client = Client()
    res = client.query_vector_by_query("default_gateway_device")
    
    
    for info in res:
        print(info)
    result_status = "ok"

    columns = [
            {
                "title":"主机",
                "key":"instance",
                "tooltip":True
            },
            {
                "title":"网络设备名称",
                "key":"device",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"网格设备状态",
                "key":"state",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"网格设备速率",
                "key":"speed",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"时间",
                "key":"date",
                "tooltip":True,
                "align":"center"
            }
        ]
    data = []
        
    for info in res:
        print(info)
        tmp = {}
        tmp["instance"] = info["metric"]["instance"]
        tmp["device"] = info["metric"]["device"]
        tmp["speed"] = info["metric"]["speed"]
        ip = info["metric"]["instance"].split(":")[0]
        """
        parm = 'node_network_up{ hypervisor_state="up", hypervisor_host_ip="%s", device="%s"}' % (ip, info["metric"]["device"])
        my_status = client.query_vector_by_query(parm)
        if len(my_status) == 1:
            if my_status[0]["value"][1] == "1":
                state = "up"
            else:
                state = "down"
        else:
            state = "down"
        tmp["state"] = state
        """
        tmp["state"] = "up"
        tmp["date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info["value"][0]))
        data.append(tmp)
            
    result = {
        "key": "hostroute",
        "name": "主机路由",
        "weigth": 20,
        "result":"ok",    # ok (V), warning (!) ,error (X)
        "columns": columns,
        "data": data
    }
    return result
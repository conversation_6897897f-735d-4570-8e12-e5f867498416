from api.prometheus.client_alert import AlertClient

name = "告警"
weigth = 20

def check():
    
    client = AlertClient()
    res = client.get_alert_list()
    
    if len(res) > 0:
        result_status = "warning"
    else:
        result_status = "ok"
    
    columns = [
            {
                "title":"标题",
                "key":"summary",
                "tooltip":True
                
            },
            {
                "title":"主机",
                "key":"instance",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"描述",
                "key":"description",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"状态",
                "key":"state",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"时间",
                "key":"date",
                "tooltip":True,
                "align":"center"
            }
        ]
    data = []
    
    
    for info in res:
        print(info)
        tmp = {}
        tmp["summary"] = info["annotations"]["summary"]
        tmp["description"] = info["annotations"]["description"]
        tmp["instance"] = info["labels"]["instance"]
        tmp["state"] = info["state"]
        tmp["date"] = info["activeAt"]
        data.append(tmp)
    
    result = {
        "key": "alert",
        "name": "平台告警状态",
        "weigth": 20,
        "columns": columns,
        "result":result_status,    # ok (V), warning (!) ,error (X)
        "data": data
    }
    return result
# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.container import ContainerCreateFrom
from util.cov import todict
from api.zun.client import ZunClient
from Page.Pagenation import  Pagenation

import logging
logger = logging.getLogger(__name__)

class ContainerHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/containers", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_containers(self,form):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 查询全部云主机类型
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
    
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
         
        client = ZunClient()
        res = client.ZunClient.openstack_get_all_container_list(client)
        data = []
        
        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res
            
        if order_type == "desc" and order_by:
            #data = sorted(data, key = lambda x:-x[order_by])
            data = sorted(data, key = lambda x:x[order_by], reverse=True)
            
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by])  
            
        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        container_list = obj.show()
        
        for container in res:
            ips = []
            if container["addresses"]:
                for key in container["addresses"]:
                    for address in container["addresses"][key]:
                        ips.append(address["addr"])
        
            container["ip"] = ips
            
        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": container_list
            }
              
        return r
    
    @get(_path="/v1/containers/images", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_images(self):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 查询全部镜像
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
        client = ZunClient()
        res = client.ImageClient.openstack_get_all_image_list(client)
        return res  
    
    @get(_path="/v1/containers/networks",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_networks(self):
        """
        ---
        tags:
          - 网络相关接口
        summary: 查询全部网络  
        responses:
          '200':
            description: 查询成功
            content: {}
        """
        client = ZunClient()
        res = client.NeutronClient.openstack_get_all_list(client)
        for network in res:
            network["cidr"] = ""
            for subnet in network["subnets"]:
                subnet_detail = client.NeutronClient.openstack_get_subnet_detail(client, subnet)
                
                print(subnet_detail["name"])
                print(subnet_detail["cidr"])
                network["cidr"] = "%s%s:%s," % (network["cidr"] ,subnet_detail["name"],subnet_detail["cidr"])
            network["cidr"] = network["cidr"].rstrip(',')
        return res
    
    @post(_path="/v1/containers/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_containers(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 创建容器
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        
        client = ZunClient()
        
        create_form = ContainerCreateFrom()
        create_form.name = form.get("name", "")
        create_form.vcpus = form.get("vcpus", "")
        create_form.ram = form.get("ram", "")
        create_form.imagename = form.get("image_name", "")
        create_form.networkid = form.get("network_id", "")
        
        container = client.ZunClient.openstack_create_container(client,create_form)
        
        return container
    
    @post(_path="/v1/containers/action",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_action(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 容器动作  
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ImageEditModel'
        """
        client = ZunClient()

        if form["action"] == "stop":
            res = client.ZunClient.openstack_container_stop(client,form["id"])
        elif form["action"] == "start":
            res = client.ZunClient.openstack_container_start(client,form["id"])
        elif form["action"] == "restart":
            res = client.ZunClient.openstack_container_restart(client,form["id"])
        elif form["action"] == "edit":
            res = client.ZunClient.openstack_container_rename(client,form["id"],form["data"])
        elif form["action"] == "resize":
            cpu = form["data"].split("@")[0]
            ram = form["data"].split("@")[1]
            res = client.ZunClient.openstack_container_resize(client,form["id"],cpu,ram)
        return res
    
    @post(_path="/v1/containers/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_delete(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 删除容器 
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ImageEditModel'
        """
        client = ZunClient()
        for id in form["ids"]:
            res = client.ZunClient.openstack_container_delete(client,id)
        return {"msg":"ok"}
    
    @post(_path="/v1/containers/logs",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_logs(self,form):

        client = ZunClient()
        res = client.ZunClient.openstack_container_logs(client,form["id"])

        return res


# -*- coding: utf-8 -*-
import uuid
from datetime import datetime
from datetime import datetime
import tornado.ioloop
import random
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import or_, and_

import pyrestful.rest
from api.hciagent.iscsiapi import ISCSIAgentClient
from api.iscsi.iscsi_client import ISCSIManager

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from settings import QUEUE_NAME

from model.instances import Clusters, Instances
from model.hypervisors import Cluster, ClusterHostForm, ClusterEditForm, ClusterDeleteForm
import settings
from util.cov import todict
# from api.prometheus.client import PClient

from db.model.hci.storage import (StoragePool, StorageVolume, Dictionary, StorageDevice, IPSANDevice,
                                  IPSANTarget, HostStorageDeviceMapping, HostStoragePoolMapping,
                                  StoragePoolGroup, StoragePoolGroupMapping)
from db.model.hci.compute import Domain, Host, Cluster, Pool, DomainDisk
from util.decorators import error_decorator
from api.libvirt.client import Client
from sqlalchemy import desc, asc, or_, tuple_
from app.tasks.storage_tasks import (
    # allocate_storage,
    allocate_storage_call_back,
    # update_storage,
    update_storage_call_back,
    # delete_storage,
    delete_storage_call_back,
    discover_iscsi_call_back,
    iscsi_create_pool_callback,
    iscsi_del_pool_callback,
    create_local_pool_call_back,
    put_local_pool_callback,
    delete_local_pool_callback,
)

from app.agents.storage_tasks import (
    allocate_storage,
    update_storage,
    delete_storage,
    discover_iscsi,
    iscsi_get_iqn,
    iscsi_logout,
    iscsi_create_pool,
    iscsi_del_pool,
    create_local_pool,
    put_local_pool,
    delete_local_pool,
)

from app.tasks.cdrom_tasks import add_tasks, callback_task, task_1, task_2, task_3
from celery import Celery, chord, group, chain, current_app
from util.system_info import get_system_info

time_format = '%Y-%m-%d %H:%M:%S'


class StorageHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers",
                        "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods',
                        'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @staticmethod
    def get_storage_type(self):
        with self.session_scope() as session:
            r = session.query(Dictionary).filter(
                Dictionary.type_code == "storage").all()
            s = [Dictionary.to_dict(d) for d in r]
            # print(s)
        return s

    @staticmethod
    def get_storage_type_by_id(self, m):
        """
        根据id返回指定的类型
        """
        data = self.get_storage_type(self)
        return next((item for item in data if int(item['id']) == int(m)), None)

    @staticmethod
    def get_local_storage_volume_format(self):
        with self.session_scope() as session:
            r = session.query(Dictionary).filter(
                Dictionary.type_code == "volume").all()
            s = [Dictionary.to_dict(d) for d in r]
            # print(s)

        return s

    @staticmethod
    def get_local_storage_volume_by_type(self, t):
        data = self.get_local_storage_volume_format(self)

        return next((item['code'] for item in data if int(item['id']) == int(t)), None)

    @get(_path="/v5/store/pool/type/list", _produces=mediatypes.APPLICATION_JSON)
    def hci_gets_storage_type(self):
        """
        获取存储池类型
        """

        return self.get_storage_type(self)

    @get(_path="/v5/store/volume/type/list", _produces=mediatypes.APPLICATION_JSON)
    def hci_gets_volume_type(self):
        """
        获取存储卷类型
        """

        return self.get_local_storage_volume_format(self)

    @post(_path="/v5/store/device/add", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_device_add(self, form):
        """
        添加存储设备
        """
        role = self.get_cookie('username', "")
        name = form.get("name", "")
        ip = form.get("ip", "")
        storage_type = form.get("storage_type", "")
        capacity = form.get("capacity")
        remark = form.get("remark")
        with self.session_scope() as session:
            device = session.query(StorageDevice).filter(
                StorageDevice.ip_mgmt == ip).first()
            if not device:
                # 创建存储设备
                # device_dict = {"ip": host.ip}
                # new_device = StorageDevice.from_dict(device_dict)
                new_device = StorageDevice()
                new_device.device_type = storage_type
                new_device.device_name = name
                new_device.ip_mgmt = ip
                new_device.total_capacity = capacity
                new_device.remark = remark
                # 将新设备添加到会话中
                session.add(new_device)
        return {"code": 200, "msg": "ok"}

    @post(_path="/v5/ipsan/docking", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_docking(self, form):
        """
        接入ipsan存储设备
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        name = form.get("name", "")
        ip = form.get("ip", "")
        port = form.get("port", "")
        share = form.get("share", False)
        user = form.get("user", "")
        password = form.get("password", "")
        host = form.get("host", [])

        with self.session_scope() as session:
            device = StorageDevice()
            if share:
                device.username = user
                device.password = password
            device.device_name = name
            device.ip_mgmt = ip
            device.model = ""
            device.vendor = ""
            device.device_type = "IP_SAN"
            session.add(device)
            session.flush()

            device_id = device.id

            for h in host:
                host_info = session.query(Host).filter(
                    Host.ip == h["ip"]).first()
                if host_info:
                    host_storage_device_mapping = HostStorageDeviceMapping()
                    host_storage_device_mapping.host_id = host_info.id
                    host_storage_device_mapping.storage_device_id = device_id
                    session.add(host_storage_device_mapping)

        return {"code": 200, "msg": "ok"}

    @post(_path="/v5/ipsan/edit", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_edit(self, form):
        """
        修改ipsan存储设备
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取参数
        id = form.get("id", None)  # 存储设备的 ID，必须提供
        name = form.get("name", None)  # 存储设备名称
        ip = form.get("ip", None)  # IP 地址
        port = form.get("port", None)  # 端口号
        share = form.get("share", None)  # 是否共享
        user = form.get("user", None)  # 用户名
        password = form.get("password", None)  # 密码
        host = form.get("host", [])  # 主机列表

        # 参数校验
        if not id:
            return {
                "code": 400,
                "msg": "参数错误：id 不能为空"
            }

        with self.session_scope() as session:
            # 查询需要修改的 StorageDevice
            device = session.query(StorageDevice).filter(
                StorageDevice.id == id).first()

            if not device:
                return {
                    "code": 404,
                    "msg": "未找到指定的 StorageDevice"
                }

            # 更新 StorageDevice 的字段
            if name is not None:
                device.device_name = name
            if ip is not None:
                device.ip_mgmt = ip
            if port is not None:
                device.port = str(port)  # 确保端口号是字符串类型
            if share is not None:
                device.share = share
                if share:  # 如果启用共享，更新用户名和密码
                    device.username = user if user is not None else device.username
                    device.password = password if password is not None else device.password
                else:  # 如果禁用共享，清空用户名和密码
                    device.username = None
                    device.password = None

            # 更新与主机的关联关系
            if host:
                # 查询所有现有的主机映射关系并删除
                session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.storage_device_id == id
                ).delete(synchronize_session=False)

                # 添加新的主机映射关系
                for h in host:
                    host_info = session.query(Host).filter(
                        Host.ip == h["ip"]).first()
                    if host_info:
                        host_storage_device_mapping = HostStorageDeviceMapping()
                        host_storage_device_mapping.host_id = host_info.id
                        host_storage_device_mapping.storage_device_id = id
                        session.add(host_storage_device_mapping)

            # 提交事务
            session.commit()
        return {
            "code": 200,
            "msg": "ok"
        }

    @post(_path="/v5/ipsan/scan", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_scan(self, form):
        """
        ipsan设备扫描
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        device_id = form.get("id", "")

        name = form.get("name", "")
        host = form.get("host", [])

        with self.session_scope() as session:
            device = (session.query(StorageDevice)
                      # 加载关联的 Host 数据
                      .options(joinedload(StorageDevice.hosts))
                      .filter(StorageDevice.id == device_id)
                      .first())

            if device:
                hosts = []
                queue_list = []

                for host in device.hosts:
                    # 获取队列名称
                    queue = "queue_" + host.ip
                    queue_list.append(queue)
                    # TODO 需要通过异步celery获取数据
                    iscsi_ip_address, iscsi_port = device.ip_mgmt.split(":")
                    iscsi_form = {
                        "iscsi_host": iscsi_ip_address,
                        "iscsi_port": iscsi_port,
                    }
                    result = discover_iscsi.apply_async(
                        args=[iscsi_form], queue=queue)
                    actual_result = result.get(timeout=10)
                    print(actual_result)
                    targets = actual_result["targets"]
                    if len(targets) == 0:
                        # return {"code": 500, "msg": targets}
                        print(f"{host.name} 无法连接iscsi设备")
                        scan_result = False
                    else:
                        scan_result = True
                        for target_iqn in targets:
                            target = (
                                session.query(IPSANTarget)
                                .filter_by(target_name=target_iqn)
                                .first()
                            )
                            if not target:
                                # 如果 Target 不存在，则创建新记录
                                target = IPSANTarget()
                                target.ip_address, target.port = device.ip_mgmt.split(
                                    ":")
                                target.target_name = target_iqn
                                target.device_id = device.id
                                target.status = "inactive"
                                if device.username and device.password:
                                    target.authentication_enabled = True
                                    target.username = device.username
                                    target.password = device.password
                                session.add(target)
                                session.flush()  # 确保生成主键值

                    h = host.to_dict_merge()
                    h["scan_result"] = scan_result
                    hosts.append(h)

            ret = device.to_dict_merge()
            ret["hosts"] = hosts
        return {"code": 200, "msg": "ok", "data": ret}

    @post(_path="/v5/ipsan/target/login", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_target_login(self, form):
        """
        登录指定的target
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        target_id = form.get("id", "")

        with self.session_scope() as session:
            # target = session.query(IPSANTarget).filter(IPSANTarget.id == target_id).first()
            # device = (session.query(StorageDevice)
            #           .options(joinedload(StorageDevice.hosts))  # 加载关联的 Host 数据
            #           .filter(StorageDevice.id == target.device_id)
            #           .first())
            target = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device)  # 预加载 StorageDevice
                .joinedload(StorageDevice.hosts)  # 预加载 StorageDevice 的 Host 数据
            ).filter(IPSANTarget.id == target_id).first()
            # 使用target_name进行登录
            device = target.storage_device.to_dict()
            hosts = target.storage_device.hosts
            hosts_info = []
            for host in hosts:
                # TODO 需要通过异步celery获取数据
                queue = "queue_" + host.ip
                iscsi_ip_address, iscsi_port = device["ip_mgmt"].split(":")
                iscsi_form = {
                    "iscsi_host": iscsi_ip_address,
                    "iscsi_port": iscsi_port,
                    "target_name": target.target_name
                }
                result = iscsi_get_iqn.apply_async(
                    args=[iscsi_form], queue=queue)
                actual_result = result.get(timeout=10)
                print(actual_result)

                target_lun = actual_result["target_lun"]
                self.save_target_luns_to_db(
                    session, device, target, target_lun)
                print(target_lun)

                hosts_info.append(host.to_dict())

        return {"code": 200, "msg": "ok", "data": target_lun}

    @staticmethod
    def save_target_luns_to_db(session, device, target, target_lun_data):
        """
        将 iSCSI Target 上的 LUN 信息保存到数据库中。

        :param session: SQLAlchemy 数据库会话
        :param device: 设备对象，包含设备管理 IP 等信息
        :param target: iSCSI Target 对象，包含 Target 名称等信息
        :param target_lun_data: 登录 Target 后返回的 LUN 数据
        """
        try:
            # 提取所有需要检查的 (iscsi_iqn, lun_number) 组合
            iscsi_iqn = target_lun_data[0].get("iqn", "")
            lun_devices = target_lun_data
            lun_keys = [(iscsi_iqn, lun_info.get("lun_id", ""))
                        for lun_info in lun_devices]

            # 批量查询已存在的记录
            existing_records = (
                session.query(IPSANDevice)
                .filter(tuple_(IPSANDevice.iscsi_iqn, IPSANDevice.lun_number).in_(lun_keys))
                .all()
            )

            # 构建一个字典，方便快速查找
            existing_record_map = {
                (record.iscsi_iqn, record.lun_number): record for record in existing_records}

            # 遍历目标数据并决定插入或更新
            for lun_info in lun_devices:
                # 提取 LUN 信息
                lun_number = lun_info.get("lun_id", "")  # 逻辑单元号
                device_path = lun_info.get("device_path", "")  # 设备路径
                fs_type = lun_info.get("fs_type", "")  # 文件系统类型
                scsi_address = lun_info.get("scsi_address", "")  # SCSI 地址
                vendor = lun_info.get("vendor", "")  # 厂商
                model = lun_info.get("model", "")  # 型号
                revision = lun_info.get("revision", "")  # 固件版本
                state = lun_info.get("state", "")  # 状态
                lun_status = state  # LUN 状态
                host_number = lun_info.get("host_number", "")  # 主机适配器编号
                channel = lun_info.get("channel", "")  # 通道编号
                capacity = "Unknown"  # 容量（假设未提供，默认为 Unknown）

                # 判断认证类型
                authentication_type = "chap" if device.get('username') else ""

                # 检查是否已存在相同的记录
                key = (iscsi_iqn, str(lun_number))
                if key in existing_record_map:
                    # 更新现有记录
                    existing_record = existing_record_map[key]
                    existing_record.ip_san_target_id = target.id
                    existing_record.authentication_type = authentication_type
                    existing_record.device_path = device_path
                    existing_record.scsi_address = scsi_address
                    existing_record.vendor = vendor
                    existing_record.model = model
                    existing_record.revision = revision
                    existing_record.state = state
                    existing_record.fs_type = fs_type
                    existing_record.host_number = host_number
                    existing_record.channel = channel
                    existing_record.capacity = capacity
                    existing_record.lun_status = lun_status
                    existing_record.updated_at = datetime.now()  # 更新时间
                else:
                    # 插入新记录
                    new_lun_record = IPSANDevice(
                        id=str(uuid.uuid4()),  # 生成唯一主键
                        ip_san_target_id=target.id,  # 关联的 Target ID
                        iscsi_iqn=iscsi_iqn,  # iSCSI IQN
                        authentication_type=authentication_type,
                        lun_number=lun_number,
                        device_path=device_path,
                        scsi_address=scsi_address,
                        vendor=vendor,
                        model=model,
                        revision=revision,
                        state=state,
                        fs_type=fs_type,
                        host_number=host_number,
                        channel=channel,
                        capacity=capacity,
                        lun_status=lun_status,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                    )
                    session.add(new_lun_record)

            # 提交事务
            session.commit()

        except IntegrityError as e:
            session.rollback()
            print(f"违反唯一性约束: {str(e)}")
        except Exception as e:
            session.rollback()
            print(f"操作失败: {str(e)}")

    @post(_path="/v5/ipsan/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_list(self, form):
        """
        ipsan查询
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        page = int(form.get('page', 1))
        page_size = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'asc')
        order_by = form.get('order_by', 'created_at')
        search_str = form.get('search_str', '')

        with self.session_scope() as session:
            # 查询 StorageDevice，并关联 IPSANDevice 和 IPSANTarget
            # 从 StorageDevice 开始，通过 IPSANDevice 关联到 IPSANTarget
            query = session.query(
                StorageDevice,
            ).filter(StorageDevice.device_type == "IP_SAN")

            # 搜索过滤
            if search_str:
                search_filter = or_(
                    StorageDevice.device_id.ilike(f"%{search_str}%"),
                    StorageDevice.device_name.ilike(f"%{search_str}%"),
                    StorageDevice.model.ilike(f"%{search_str}%"),
                    StorageDevice.vendor.ilike(f"%{search_str}%"),
                    # IPSANDevice.device_id.ilike(f"%{search_str}%"),
                    # IPSANDevice.iscsi_iqn.ilike(f"%{search_str}%"),
                    # IPSANTarget.target_name.ilike(f"%{search_str}%"),
                    # IPSANTarget.ip_address.ilike(f"%{search_str}%"),
                    # IPSANTarget.target_alias.ilike(f"%{search_str}%")
                )
                query = query.filter(search_filter)

            # 排序
            if order_by == 'created_at':
                if order_type.lower() == 'desc':
                    query = query.order_by(desc(StorageDevice.created_at))
                else:
                    query = query.order_by(asc(StorageDevice.created_at))
            elif order_by == 'device_id':
                if order_type.lower() == 'desc':
                    query = query.order_by(desc(StorageDevice.device_id))
                else:
                    query = query.order_by(asc(StorageDevice.device_id))
            elif order_by == 'device_name':
                if order_type.lower() == 'desc':
                    query = query.order_by(desc(StorageDevice.device_name))
                else:
                    query = query.order_by(asc(StorageDevice.device_name))

            # 总数
            total = query.distinct().count()

            # 分页
            storage_devices = query.limit(page_size).offset(
                (page - 1) * page_size).all()

            # 构造返回数据，包含关联的 IPSANTarget 和 IPSANDevice
            storage_list = []
            for storage in storage_devices:  # 遍历每个 StorageDevice
                storage_dict = storage.to_dict_merge()  # 将 StorageDevice 转换为字典

                if len(storage_dict["storage_pools"]) > 0:
                    hosts = storage_dict["storage_pools"][0]["hosts"]
                    storage_dict["hosts"] = hosts

                # 获取关联的 IPSANDevice 数据
                # ipsan_target_list = []
                # for target in storage.targets:  # 遍历每个关联的 IPSANTarget
                #     target_dict = target.to_dict_merge()  # 将 IPSANTarget 转换为字典

                # 获取关联的 IPSANDevice 数据
                #     ip_san_device_list = []
                #     if target.ip_san_devices:  # 如果存在关联的 IPSANDevice
                #         ip_san_device_list = [device.to_dict_merge() for device in target.ip_san_devices]
                #
                #     # 将 IPSANDevice 数据添加到 IPSANTarget 字典中
                #     target_dict['ip_san_devices'] = ip_san_device_list
                #
                #     # 将 IPSANTarget 字典添加到列表中
                #     ipsan_target_list.append(target_dict)
                #
                # # 将 IPSANTarget 列表添加到 StorageDevice 字典中
                # storage_dict['ipsan_targets'] = ipsan_target_list

                # 将 StorageDevice 字典添加到最终结果列表中
                storage_list.append(storage_dict)

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": storage_list
        }

    @post(_path="/v5/ipsan/target/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_target_list(self, form):
        """
        ipsan target 查询
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        device_id = form.get("id", "")
        page = int(form.get('page', 1))
        page_size = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'asc')
        order_by = form.get('order_by', 'created_at')
        search_str = form.get('search_str', '')

        with self.session_scope() as session:
            # 基础查询
            query = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device))

            # 如果提供了 device_id，则过滤设备 ID
            query = query.filter(IPSANTarget.device_id == device_id)

            # 如果提供了搜索关键字，则模糊匹配目标名称、别名、IP 地址等字段
            if search_str:
                query = query.filter(
                    or_(
                        IPSANTarget.target_name.ilike(f"%{search_str}%"),
                        IPSANTarget.target_alias.ilike(f"%{search_str}%"),
                        IPSANTarget.ip_address.ilike(f"%{search_str}%")
                    )
                )

            # 排序逻辑
            # 定义允许排序的字段列表
            ALLOWED_ORDER_FIELDS = ['id', 'created_at', 'updated_at']

            # 校验 order_by 是否合法
            if order_by not in ALLOWED_ORDER_FIELDS:
                order_by = 'created_at'

            # 获取排序字段
            order_field = getattr(IPSANTarget, order_by,
                                  IPSANTarget.created_at)

            # 根据 order_type 决定排序方式
            if order_type.lower() == 'desc':
                order_by_field = order_field.desc()
            else:
                order_by_field = order_field.asc()

            query = query.order_by(order_by_field)
            total_count = query.distinct().count()

            # 分页逻辑
            offset = (page - 1) * page_size
            paginated_query = query.offset(offset).limit(page_size)
            # 查询结果
            targets = paginated_query.all()
            data = []
            for t in targets:
                l = []
                luns = t.ip_san_devices
                for lun in luns:
                    l.append(lun.to_dict_merge())

                ipsan_target = t.to_dict_merge()
                ipsan_target['ip_san_devices'] = l
                data.append(ipsan_target)

            # 构建返回结果
            result = {
                "code": 200,
                "message": "Success",
                "total": total_count,  # 总记录数
                "page": page,  # 当前页码
                "page_size": page_size,  # 每页记录数
                "data": data  # 当前页的数据
            }

        return result

    @delete(_path="/v5/ipsan/target/deleted", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_target_deleted(self, form):
        """
        删除 IP SAN Target 接口
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取参数
        names = form.get('names', [])
        ids = form.get('ids', [])
        with self.session_scope() as session:
            # 在对应的主机上调用登出逻辑
            targets = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device)  # 预加载 StorageDevice
                .joinedload(StorageDevice.hosts)  # 预加载 StorageDevice 的 Host 数据
            ).filter(IPSANTarget.id.in_(ids)).all()

            # 初始化扫描结果
            scan_result = True

            # 遍历查询到的所有 target
            for target in targets:
                # 获取 StorageDevice 和 Hosts 数据
                device = target.storage_device.to_dict()
                hosts = target.storage_device.hosts
                hosts_info = []

                for host in hosts:
                    # TODO 通过异步celery进行删除

                    queue = "queue_" + host.ip
                    iscsi_ip_address, iscsi_port = device["ip_mgmt"].split(":")
                    iscsi_form = {
                        "iscsi_host": iscsi_ip_address,
                        "iscsi_port": iscsi_port,
                        "target_name": target.target_name
                    }
                    result = iscsi_logout.apply_async(
                        args=[iscsi_form], queue=queue)
                    # actual_result = result.get(timeout=10)

                    # is_logout = actual_result["is_logout"]
                    # if is_logout:
                    #     print(f"{host.name} 登出 iSCSI 设备失败")
                    # else:
                    #     # 如果需要保存成功登录的信息，可以在此记录
                    #     print(f"{host.name} 登出 iSCSI 设备成功")

            # 先删除关联的 IPSANDevice 记录
            session.query(IPSANDevice).filter(
                IPSANDevice.ip_san_target_id.in_(ids)).delete(synchronize_session=False)

            session.query(IPSANTarget).filter(
                IPSANTarget.id.in_(ids)).delete(synchronize_session=False)

            # 提交事务
            session.commit()

            # 返回成功响应
        return {
            "code": 200,
            "msg": "ok",
        }

    @delete(_path="/v5/ipsan/deleted", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_deleted(self, form):
        """
        删除 IP SAN Device 接口
        支持通过 names 或 ids 批量删除，自动删除关联的 IPSANTarget
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取参数
        names = form.get('names', [])
        ids = form.get('ids', [])

        if not names and not ids:
            return {
                "code": 400,
                "msg": "参数错误：names 和 ids 不能同时为空"
            }

        with self.session_scope() as session:
            query = session.query(StorageDevice).filter(
                StorageDevice.id.in_(ids))

            # 查询要删除的记录
            storage_devices = query.all()

            if not storage_devices:
                return {
                    "code": 200,
                    "msg": "未找到符合条件的 StorageDevice"
                }

            # 获取要删除的 StorageDevice 的 ID 列表
            storage_device_ids = [storage.id for storage in storage_devices]

            target_count = session.query(IPSANTarget).filter(
                IPSANTarget.device_id.in_(storage_device_ids)).count()
            if target_count != 0:
                return {
                    "code": 200,
                    "msg": "当前存储设备下面存在target设备，无法删除"
                }

            # 手动删除中间表 host_storage_device_mapping 中的相关记录
            session.query(HostStorageDeviceMapping).filter(
                HostStorageDeviceMapping.storage_device_id.in_(
                    storage_device_ids)
            ).delete(synchronize_session=False)

            # 批量删除 StorageDevice
            session.query(StorageDevice).filter(StorageDevice.id.in_(storage_device_ids)).delete(
                synchronize_session=False)

            # 提交事务
            session.commit()
            return {
                "code": 200,
                "msg": "ok"
            }

    @post(_path="/v5/ipsan/create", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_create(self, form):
        """
        创建存储池
        """
        # print("参数：",form)
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        target_id = form.get("target_id")
        pool_name = form.get("name")
        target_device_path = form.get("device_path")
        storage_type = form.get("storage_type", "share")
        hosts_info = form.get("hosts")
        with self.session_scope() as session:
            # 基础查询
            query = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device).joinedload(
                    StorageDevice.hosts),
                joinedload(IPSANTarget.storage_device).joinedload(
                    StorageDevice.storage_pools).joinedload(StoragePool.hosts),
            )

            # 如果提供了 device_id，则过滤设备 ID
            query = query.filter(IPSANTarget.id == target_id)
            target = query.first()
            hosts_info = target.storage_device.hosts
            hosts = [host.to_dict_merge() for host in hosts_info]

            device = target.storage_device.to_dict_merge()
            # print(len(device["hosts"]), len(device["storage_pools"][0]["hosts"]))
            target_info = target.to_dict_merge()

            # # 查询关联的主机
            # hosts = session.query(Host).join(Host.storage_devices).filter(StorageDevice.id == device["id"]).all()
            # hosts_info = [host.to_dict_merge() for host in hosts]

            # 创建存储池数据库信息
            pool_info = {
                "name": pool_name,
                "type_code": "IP-SAN",
                "type_code_display": "iscsi+ocfs2存储",
                "storage_device_id": device["id"],
                "status": 1,
                "storage_local_dir": "",
            }

            storagePool = StoragePool.from_dict(pool_info)
            session.add(storagePool)
            session.commit()
            storage_pool_id = storagePool.id

            # 关联表绑定
            for host in hosts:
                host_storage_pool_mapping = HostStoragePoolMapping()
                host_storage_pool_mapping.host_id = host["id"]
                host_storage_pool_mapping.storage_pool_id = storage_pool_id
                session.add(host_storage_pool_mapping)

            # 随机选择一个主机执行创建操作
            
            # 获取可用的队列
            i = current_app.control.inspect()
            active_queues = i.active_queues()
            if not active_queues:
                return {"code": "200", "msg": "没有可用的工作节点"}

            # 过滤出可用的主机
            available_hosts = []
            for host in hosts:
                queue_name = f"queue_{host['ip']}"
                if any(queue['name'] == queue_name for worker_queues in active_queues.values() for queue in worker_queues):
                    available_hosts.append(host)

            if not available_hosts:
                return {"code": "200", "msg": "没有可用的工作节点"}

            # 从可用主机中随机选择一个
            selected_host = random.choice(available_hosts)
            queue = "queue_" + selected_host['ip']

            iscsi_ip_address, iscsi_port = device["ip_mgmt"].split(":")
            iscsi_form = {
                "iscsi_host": iscsi_ip_address,
                "iscsi_port": iscsi_port,
                "target_name": target.target_name,
                "device_path": target_device_path,
                "storage_type": storage_type,
                "pool_name": pool_name,
                "storage_pool_id": storage_pool_id,
            }
            result = (iscsi_create_pool.apply_async(args=[iscsi_form],
                                                    queue=queue,
                                                    link=iscsi_create_pool_callback.s().set(queue=settings.QUEUE_NAME)))

            session.commit()
            return {"code": "200", "msg": "ok","data": "iscsi存储池开始创建"}

    # ==========================================分布式存储池=========================================================

    @error_decorator("/v5/store/pool/distributed/list")
    @post(_path="/v5/store/pool/distributed/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_distributed_list(self, form):
        """
        分布式存储池列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)

        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "time")
        search_str = form.get("search_str", "")
        _id = form.get("_id", "")
        _type = form.get("type", "")
        group_id = form.get("group_id", 0)  # 新增

        with self.session_scope() as session:
            # query = session.query(StoragePool).options(joinedload(StoragePool.storage_volumes))

            query = session.query(StoragePool).options(
                joinedload(StoragePool.hosts),
                joinedload(StoragePool.storage_volumes)
            ).filter(StoragePool.type_code != "local")
            # 如果有 group_id，只查该组下的存储池
            if group_id and group_id != 0 and group_id != 1:
                pool_ids = session.query(StoragePoolGroupMapping.pool_id).filter(
                    StoragePoolGroupMapping.storage_pool_group_id == group_id
                ).all()
                pool_ids = [pid[0] for pid in pool_ids]
                if pool_ids:
                    query = query.filter(StoragePool.id.in_(pool_ids))
                else:
                    # 组下没有池，直接返回空
                    return {
                        "code": "200",
                        "msg": "ok",
                        "page": page,
                        "pages": pagecount,
                        "total": 0,
                        "data": []
                    }

            if search_str != "":
                query = query.filter(StoragePool.name.like(f'%{search_str}%'))

            # 获取总记录数
            total_count = query.count()

            if order_by:
                if order_type == "desc":
                    query = query.order_by(
                        desc(getattr(StoragePool, order_by)))
                else:
                    query = query.order_by(asc(getattr(StoragePool, order_by)))

            if page == 0:
                pools = query.all()
                data = [StoragePool.to_dict_merge(pool) for pool in pools]
                return {
                    "code": "200",
                    "msg": "ok",
                    "page": page,
                    "pages": pagecount,
                    "total": int(total_count),
                    "data": data
                }

            # 添加分页
            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            # 执行查询
            pools = query.all()

            # data = [StoragePool.to_dict_merge(pool) for pool in pools]
            data = []
            for pool in pools:
                device = pool.storage_device
                targets = device.targets
                hosts = pool.hosts
                hosts_info = [Host.to_dict_merge(host) for host in hosts]
                device_info = StorageDevice.to_dict_merge(device)
                targets_info = [IPSANTarget.to_dict_merge(target) for target in targets]
                storage_pool_info = StoragePool.to_dict(pool)
                storage_pool_info["hosts"] = hosts_info
                storage_pool_info["storage_device"] = device_info
                storage_pool_info["targets"] = targets_info
                data.append(storage_pool_info)
                
        r = {
            "code": "200",
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": total_count,
            "data": data
        }
        return r

    @error_decorator("/v5/store/pool/distributed/update")
    @post(_path="/v5/store/pool/distributed/update", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_distributed_update(self, form):
        """
        修改存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        pool_id = form.get("pool_id", "")
        name = form.get("name", "")

        with self.session_scope() as session:
            pool = session.query(StoragePool).filter(
                StoragePool.id == pool_id).first()
            if not pool:
                return {"code": "200", "msg": "存储池不存在"}

            # 更新字段
            if name != "":
                pool.name = name

            session.commit()
            return {"code": "200", "msg": "ok"}

    @error_decorator("/v5/store/pool/distributed/delete")
    @delete(_path="/v5/store/pool/distributed/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_distributed_delete(self, form):
        """
        批量删除存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        ids = form.get("ids", [])
        names = form.get("names", [])

        if len(ids) == 0:
            return {"code": 200, "msg": "ok", "data": "参数为空"}

        with self.session_scope() as session:
            # 检查所有存储池是否存在
            pools = session.query(StoragePool).filter(
                StoragePool.id.in_(ids)).all()

            # 检查存储池是否有关联的存储卷
            for pool in pools:
                if pool.storage_volumes:
                    print(f"存储池 {pool.name} 存在关联的存储卷，不做删除")
                else:
                    hosts = [host.to_dict_merge() for host in pool.hosts]
                    # 随机选择一个主机
                    import random
                    selected_host = random.choice(hosts)
                    queue = "queue_" + selected_host['ip']

                    # 异步删除底层存储池目录
                    print(f"异步删除底层存储池目录: {pool.name}")
                    iscsi_form = {
                        "pool_name": pool.name,
                        "storage_local_dir": pool.storage_local_dir,
                        "storage_pool_id": pool.id  # 添加存储池ID，用于回调时删除数据库记录
                    }
                    # 使用回调函数处理数据库删除
                    iscsi_del_pool.apply_async(
                        args=[iscsi_form],
                        queue=queue,
                        link=iscsi_del_pool_callback.s().set(queue=settings.QUEUE_NAME)
                    )

            return {"code": "200", "msg": "ok"}

    # ==========================================本地存储池=========================================================
    @error_decorator("/v5/store/pool/local/list")
    @post(_path="/v5/store/pool/local/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_list(self, form):
        """
        本地存储池列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)

        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "time")
        search_str = form.get("search_str", "")
        _id = form.get("_id", "")
        type = form.get("type", "")

        with self.session_scope() as session:
            # query = session.query(StoragePool).options(joinedload(StoragePool.storage_volumes))

            query1 = session.query(Host)
            if type == 'pool':
                query1 = query1.join(Cluster).join(Pool).filter(Pool.id == _id)

            elif type == 'cluster':
                query1 = query1.join(Cluster).filter(Cluster.id == _id)

            elif type == 'host':
                query1 = query1.filter(Host.id == _id)
            hosts = query1.all()

            query = session.query(StoragePool)

            if hosts:
                query = query.outerjoin(StorageDevice).outerjoin(HostStoragePoolMapping).filter(
                    or_(
                        StorageDevice.ip_mgmt.in_([h.ip for h in hosts]),
                        and_(
                            HostStoragePoolMapping.host_id.in_([h.id for h in hosts]),
                            HostStoragePoolMapping.storage_pool_id == StoragePool.id
                        )
                    ))

            if search_str != "":
                query = query.filter(StoragePool.name.like(f'%{search_str}%'))

            # 获取总记录数
            total_count = query.count()

            if order_by:
                if order_type == "desc":
                    query = query.order_by(
                        desc(getattr(StoragePool, order_by)))
                else:
                    query = query.order_by(asc(getattr(StoragePool, order_by)))

            if page == 0:
                pools = query.all()
                data = [StoragePool.to_dict(pool) for pool in pools]
                return {
                    "code": "200",
                    "msg": "ok",
                    "page": page,
                    "pages": pagecount,
                    "total": int(total_count),
                    "data": data
                }

            # 添加分页
            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            # 执行查询
            pools = query.all()

            # libvirtClient = Client(host="***************")
            # r = libvirtClient.get_storage_pool_names(libvirtClient)

            data = [StoragePool.to_dict(pool) for pool in pools]

        r = {
            "code": "200",
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": total_count,
            "data": data
        }
        return r

    @error_decorator("/v5/store/pool/local/detail/{pool_name}")
    @get(_path="/v5/store/pool/local/detail/{pool_name}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_info(self, pool_name):
        """
        本地存储池详情
        """

        host = self.get_argument("host")
        libvirtClient = Client(host=host)
        r = libvirtClient.get_storage_pool_info(libvirtClient, pool_name)

        return {"code": 200, "msg": "ok", "data": r}

    @error_decorator(error_message="/v5/store/pool/local/add", ob="本地存储池", action="创建本地存储池",
                     desc="创建本地存储池")
    @post(_path="/v5/store/pool/local/add", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_local_add(self, form):
        """
        新建本地存储池，处理步骤：
        1. 创建/更新存储设备记录
        2. 建立存储设备和主机的关联
        3. 创建存储池记录
        4. 建立存储池和主机的关联
        5. 启动异步任务创建实际存储池
        """
        try:
            name = form.get("name", "")
            storage_local_dir = form.get("storage_local_dir", "")
            host_ip = form.get("host", "")
            type_id = form.get("type", 1)
            type_info = self.get_storage_type_by_id(self, type_id)

            with self.session_scope() as session:
                # 1. 查询主机
                host = session.query(Host).filter(Host.ip == host_ip).first()
                if not host:
                    raise Exception(f"主机 {host_ip} 不存在")

                # 2. 创建或更新存储设备
                device = session.query(StorageDevice).filter(
                    StorageDevice.ip_mgmt == host.ip).first()
                if not device:
                    device = StorageDevice(
                        ip_mgmt=host.ip,
                        device_type=type_info["code"],
                        device_name=host.name,
                        total_capacity=host.storage
                    )
                    # system_info = get_system_info()
                    # 假数据，数据表设计字段不能为空，在存储池回调函数时进行更新
                    device.model = "Unknown"
                    device.vendor = "Unknown"
                    session.add(device)
                    session.flush()  # 获取设备ID

                # 3. 创建存储设备和主机的关联
                device_mapping = HostStorageDeviceMapping(
                    host_id=host.id,
                    storage_device_id=device.id
                )
                session.add(device_mapping)

                # 4. 创建存储池记录（初始状态）
                storage_pool = StoragePool(
                    name=name,
                    type_code=type_info["code"],
                    type_code_display=type_info["name"],
                    storage_device_id=device.id,
                    storage_local_dir=storage_local_dir,
                    status=1  # 初始状态，等待创建
                )
                session.add(storage_pool)
                session.flush()  # 获取存储池ID

                # 5. 创建存储池和主机的关联
                pool_mapping = HostStoragePoolMapping(
                    host_id=host.id,
                    storage_pool_id=storage_pool.id
                )
                session.add(pool_mapping)

                # 提交所有更改
                session.commit()

                # 6. 启动异步任务
                task_form = {
                    "host_ip": host_ip,
                    "pool_name": name,
                    "storage_local_dir": storage_local_dir,
                    "device_id": str(device.id),
                    "pool_id": str(storage_pool.id),
                    "type_info": type_info,
                    "storage_device_id": str(device.id),
                    "storage_pool_id": str(storage_pool.id)
                }

                # 创建异步任务
                result = create_local_pool.apply_async(
                    args=(task_form,),  # 参数必须是元组，单个参数需要加逗号
                    queue=f"queue_{host_ip}",
                    link=create_local_pool_call_back.s().set(queue=QUEUE_NAME),  # 成功回调
                )

                return {
                    "msg": "ok",
                    "data": "开始创建本地存储池",
                    "code": 200,
                }

        except Exception as e:
            print(f"创建本地存储池失败: {str(e)}")
            return {"msg": f"创建本地存储池失败: {str(e)}", "code": 500}

    @post(_path="/v5/store/pool/local/add/test", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_local_add_test(self, form):
        """
        新建本地存储池测试
        """

        role = self.get_cookie('username', "")

        name = form.get("name", "")
        storage_local_dir = form.get("storage_local_dir", "")
        host_ip = form.get("host", "")
        # host_id = form.get("host_id", "")
        type = form.get("type", 1)
        type_info = self.get_storage_type_by_id(self, type)

        with self.session_scope() as session:
            # host = session.query(Host).filter(Host.ip == host_ip).first()
            # 可选：如果你需要获取新创建的设备的ID或其他信息，可以在提交后查询
            # device = session.query(StorageDevice).filter(StorageDevice.ip == host.ip).first()

            # device_id = device.id
            # 调用libvirt创建存储池
            libvirtClient = Client(host=host_ip)
            form["target_path"] = storage_local_dir
            r = libvirtClient.test_create_storage_pool(libvirtClient, form)
            # pool = libvirtClient.get_storage_pool_info(libvirtClient, name)
            # pool["type_code"] = type_info["code"]
            # pool["type_code_display"] = type_info["name"]
            # pool["storage_device_id"] = device_id
            # storagePool = StoragePool.from_dict(pool)
            # session.add(storagePool)

        return {"code": 200, "msg": "ok", "data": r}

    @error_decorator("/v5/store/pool/local/put", ob="本地存储池", action="修改本地存储池", desc="修改本地存储池")
    @put(_path="/v5/store/pool/local/put", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_put(self, form):
        """
        异步编辑本地存储池
        仅支持修改名称，其他修改后续实现
        """
        # 检查参数完整性
        id = form.get("id", "")
        name = form.get("name", "")
        new_name = form.get("new_name", "")

        if not id or not name or not new_name:
            return {"code": 400, "msg": "参数不完整"}

        if name == new_name:
            return {"code": 200, "msg": "名称未修改"}

        try:
            with self.session_scope() as session:
                storagePool = session.query(StoragePool).filter(
                    StoragePool.id == id).first()
                if not storagePool:
                    return {"code": 400, "msg": "找不到存储池"}

                if not storagePool.storage_device:
                    return {"code": 400, "msg": "找不到主机"}

                # 准备异步任务参数
                task_form = {
                    "id": id,
                    "name": name,
                    "new_name": new_name,
                    "host_ip": storagePool.storage_device.ip_mgmt
                }

                # 获取队列名称
                queue = f"queue_{storagePool.storage_device.ip_mgmt}"
                # 启动异步任务
                result = put_local_pool.apply_async(
                    args=(task_form,),  # 注意这里用元组
                    queue=queue,
                    link=put_local_pool_callback.s().set(queue=QUEUE_NAME)
                )

                # 更新存储池名称
                session.query(StoragePool).filter(
                    StoragePool.id == id).update({"name": new_name})
                changes_made = True

        except Exception as e:
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

        # 根据操作结果返回
        if changes_made:
            return {"code": 200, "msg": "ok"}
        else:
            return {"code": 200, "msg": "未执行任何修改"}

    @error_decorator(error_message="/v5/store/pool/local/delete", ob="本地存储池", action="删除本地存储池",
                     desc="删除本地存储池")
    @delete(_path="/v5/store/pool/local/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_delete(self, form):
        """
        异步删除本地存储池
        """
        ids = form.get("ids", [])
        names = form.get("names", [])

        # 遍历检查每个存储池
        for _id, name in zip(ids, names):
            with self.session_scope() as session:
                storagePool = session.query(StoragePool).options(
                    joinedload(StoragePool.storage_volumes)
                ).filter(StoragePool.id == _id).first()

                if not storagePool:
                    continue

                # 检查存储池是否包含存储卷
                if len(storagePool.storage_volumes) > 0:
                    return {"code": 400, "msg": f"存储池{storagePool.name}下有存储卷，不能删除"}

                # 准备异步任务参数
                task_form = {
                    "id": str(_id),
                    "pool_name": storagePool.name,
                    "host_ip": storagePool.storage_device.ip_mgmt
                }

                # 获取队列名称
                queue = f"queue_{storagePool.storage_device.ip_mgmt}"

                # 启动异步任务
                result = delete_local_pool.apply_async(
                    args=(task_form,),  # 使用元组传参
                    queue=queue,
                    link=delete_local_pool_callback.s().set(queue=QUEUE_NAME)
                )

        return {"code": 200, "msg": "ok", "data": "删除任务已创建"}

    @error_decorator("/v5/store/pool/detail")
    @delete(_path="/v5/store/pool/detail", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_detail(self, form):
        """
        存储池详情
        """
        pass

    @error_decorator("/v5/store/pool/refresh")
    @post(_path="/v5/store/pool/refresh", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_refresh(self, form):
        """
        存储池->刷新
        """
        name = form.get("name", "")
        host = form.get("host", "")
        id = form.get("id", "")

        with self.session_scope() as session:
            storagePool = session.query(StoragePool).filter(
                StoragePool.id == id).first()
            if storagePool:
                libvirtClient = Client(host=host)
                pool = libvirtClient.get_storage_pool_info(libvirtClient, name)
                pool["id"] = id
                # 遍历字典参数并更新字段
                for key, value in pool.items():
                    if hasattr(storagePool, key) and getattr(storagePool, key) != value:
                        setattr(storagePool, key, value)
                        # 如果字段发生了变化，设置一个标志
                        changes_made = True
                # 如果有更改，保存更改
                if changes_made:
                    session.commit()
            else:
                return {"code": 200, "msg": "找不到数据"}

        return {"code": 200, "msg": "ok"}

    @error_decorator("/v5/store/pool/pause")
    @post(_path="/v5/store/pool/pause", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_pause(self, form):
        """
       存储池->暂停
       """
        id = form.get("id", "")
        name = form.get("name", "")
        host = form.get("host", "")

        new_form = {
            "id": id
        }

        with self.session_scope() as session:
            storagePool = session.query(StoragePool).filter(
                StoragePool.id == id).first()
            if storagePool:
                # 暂停本地存储池，获取存储池状态
                libvirtClient = Client(host=host)
                r = libvirtClient.pause_storage_pool(libvirtClient, name)

                new_form["status"] = r

                # 遍历字典参数并更新字段
                for key, value in new_form.items():
                    if hasattr(storagePool, key) and getattr(storagePool, key) != value:
                        setattr(storagePool, key, value)
                        # 如果字段发生了变化，设置一个标志
                        changes_made = True
                # 如果有更改，保存更改
                if changes_made:
                    session.commit()
            else:
                return {"code": 200, "msg": "找不到数据"}

        return {"code": 200, "msg": "ok"}

    @error_decorator("/v5/store/pool/unpause")
    @post(_path="/v5/store/pool/unpause", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_unpause(self, form):
        """
        存储池->启动
        """
        id = form.get("id", "")
        name = form.get("name", "")
        host = form.get("host", "")

        new_form = {
            "id": id
        }

        with self.session_scope() as session:
            storagePool = session.query(StoragePool).filter(
                StoragePool.id == id).first()
            if storagePool:
                # 暂停本地存储池，获取存储池状态
                libvirtClient = Client(host=host)
                r = libvirtClient.unpause_storage_pool(libvirtClient, name)

                new_form["status"] = r

                # 遍历字典参数并更新字段
                for key, value in new_form.items():
                    if hasattr(storagePool, key) and getattr(storagePool, key) != value:
                        setattr(storagePool, key, value)
                        # 如果字段发生了变化，设置一个标志
                        changes_made = True
                # 如果有更改，保存更改
                if changes_made:
                    session.commit()
            else:
                return {"code": 200, "msg": "找不到数据"}

        return {"code": 200, "msg": "ok"}

    @post(_path="/v5/storage/pool/volume", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_create_pool_volume(self, form):
        """
        创建存储池下的存储卷
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        form["role"] = role
        form["username"] = username
        name = form["name"]
        storage_pool_id = form["storage_pool_id"]
        volume_path = form.get("path", "")

        if name == "":
            return {"code": 200, "msg": "存储卷名称不能为空"}

        if storage_pool_id == "":
            return {"code": 200, "msg": "存储池id不能为空"}

        form["volume_format"] = self.get_local_storage_volume_by_type(
            self, form["volume_type"])

        # 参数整理
        # 存储池信息，获取存储池信息
        with self.session_scope() as session:
            storage_pool = session.query(StoragePool).filter_by(
                id=storage_pool_id).first()
            if storage_pool is None:
                return {"code": 200, "msg": "存储池不存在"}
            
            if storage_pool.type_code == "IP-SAN":
                # ISCSI存储的存储卷创建流程
                hosts_info = storage_pool.hosts
                hosts = [host.to_dict_merge() for host in hosts_info]
                host_info = random.choice(hosts)  # 随机选择一个主机
                form["host"] = host_info["ip"]
            elif storage_pool.type_code == "local":
                # 本地存储的存储卷创建流程
                form["host"] = storage_pool.storage_device.ip_mgmt
            else:
                # 其他类型存储池的处理逻辑
                return {"code": 200, "msg": "不支持的存储池类型: {}".format(storage_pool.type_code)}
            # 将数据存入数据表中
            if volume_path == "":
                form["path"] = storage_pool.storage_local_dir

            data = {
                "name": form["name"],
                "storage_pool_id": form["storage_pool_id"],
                # "type_code": form["volume_format"],
                "join_type": form["join_type"],
                "path": form["path"],
                "encrypt": form["encrypt"],
                "status": 4,
                "capacity": form["capacity"],
                "allocation": form["capacity"],
                "preallocation": form["preallocation"],
                "remark": form["remark"],
                "protocol_type": "",
                "volume_type": form["volume_format"],

            }
            volume = StorageVolume.from_dict(data)
            session.add(volume)
            session.commit()
            form["id"] = volume.id
            print(volume.id)

        form["storage_pool_name"] = storage_pool.name
        # form["storage_pool_dir"] = storage_pool.storage_local_dir

        form["storage_type_code"] = storage_pool.type_code

        queue = "queue_" + form["host"]
        result = (allocate_storage.apply_async((name, form),
                                               queue=queue,
                                               link=allocate_storage_call_back.s().set(queue=settings.QUEUE_NAME)))
        form["id"] = str(form["id"])
        return {"code": "200", "msg": "开始创建存储卷", "data": form}

    @put(_path="/v5/storage/pool/volume/update", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_update_pool_volume(self, form):
        """
        修改存储卷
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        _id = form.get("id", "")
        name = form.get("name", "")

        with self.session_scope() as session:
            volume = session.query(StorageVolume).filter_by(id=_id).first()
            if volume:
                storage_pool = volume.storage_pool
                form["storage_type_code"] = storage_pool.type_code
                form["host"] = volume.storage_pool.storage_device.ip_mgmt
                form["pool_name"] = volume.storage_pool.name
                form["volume_type"] = volume.volume_type
                form["path"] = volume.path
                
                
                if storage_pool.type_code == "IP-SAN":
                    # ISCSI存储的存储卷创建流程
                    hosts_info = storage_pool.hosts
                    hosts = [host.to_dict_merge() for host in hosts_info]
                    host_info = random.choice(hosts)  # 随机选择一个主机
                    form["host"] = host_info["ip"]
                elif storage_pool.type_code == "local":
                    # 本地存储的存储卷创建流程
                    form["host"] = storage_pool.storage_device.ip_mgmt
                else:
                    # 其他类型存储池的处理逻辑
                    return {"code": 200, "msg": "不支持的存储池类型：{}".format(storage_pool.type_code)}
   
                queue = "queue_" + form["host"]
                result = (update_storage.apply_async((form["name"], form),
                                                     queue=queue,
                                                     link=update_storage_call_back.s().set(queue=settings.QUEUE_NAME)))
        data = form
        return {"code": "200", "msg": "ok", "data": data}

    @delete(_path="/v5/storage/pool/volume/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_del_pool_volume(self, form):
        """
        删除存储卷
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        _ids = form.get("ids", [])
        _names = form.get("names", [])
        form["username"] = username
        form["role"] = role

        for _id, _name in zip(_ids, _names):
            with self.session_scope() as session:
                volume = session.query(StorageVolume).filter_by(id=_id).first()
                if volume:
                    storage_pool = volume.storage_pool
                    form["storage_type_code"] = storage_pool.type_code
                    form["volume_name"] = volume.name + \
                        "." + volume.volume_type
                    form["pool_name"] = volume.storage_pool.name
                    form["path"] = volume.path
                    form["_id"] = _id
                    if storage_pool.type_code == "IP-SAN":
                        # ISCSI存储的存储卷创建流程
                        hosts_info = storage_pool.hosts
                        hosts = [host.to_dict_merge() for host in hosts_info]
                        host_info = random.choice(hosts)  # 随机选择一个主机
                        form["host"] = host_info["ip"]
                    elif storage_pool.type_code == "local":
                        # 本地存储的存储卷创建流程
                        form["host"] = storage_pool.storage_device.ip_mgmt
                    else:
                        # 其他类型存储池的处理逻辑
                        return {"code": 200, "msg": "不支持的存储池类型: {}".format(storage_pool.type_code)}
                    # form["host"] = volume.storage_pool.storage_device.ip_mgmt
                    
                    queue = "queue_" + form["host"]
                    result = (delete_storage.apply_async((form["volume_name"], form),
                                                         queue=queue,
                                                         link=delete_storage_call_back.s().set(queue=settings.QUEUE_NAME)))
        data = form
        return {"code": "200", "msg": "正在删除存储卷", "data": data}

    @post(_path="/v5/storage/pool/volume/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_pool_volume_list(self, form):
        """
        获取存储卷列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)

        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "created_at")  # 默认使用创建时间排序
        search_str = form.get("search_str", "")
        pool_id = form.get("pool_id")
        volume_type = form.get("volume_type", "")  # 新增
        
        # 定义允许排序的字段列表
        allowed_order_fields = [
            "created_at",      # 创建时间
            "updated_at",      # 更新时间
            "name",           # 存储卷名称
            "capacity",       # 容量
            "allocation",     # 已分配空间
            "status",        # 状态
            "protocol_type", # 协议类型
            "volume_type"    # 卷类型
        ]
        
        with self.session_scope() as session:
            query = session.query(StorageVolume, Domain)\
                .outerjoin(DomainDisk, StorageVolume.id == DomainDisk.storage_vol_id)\
                .outerjoin(Domain, DomainDisk.domain_id == Domain.id)

            if pool_id != "":
                query = query.filter(StorageVolume.storage_pool_id == pool_id)

            if search_str != "":
                query = query.filter(
                    StorageVolume.name.like(f'%{search_str}%'))
            if volume_type != "":
                query = query.filter(StorageVolume.volume_type == volume_type)

            # 获取总记录数
            total_count = query.count()

            # 验证排序字段是否有效
            if order_by in allowed_order_fields:
                if order_type == "desc":
                    query = query.order_by(
                        desc(getattr(StorageVolume, order_by)))
                else:
                    query = query.order_by(
                        asc(getattr(StorageVolume, order_by)))
            else:
                # 如果排序字段无效，使用默认排序（创建时间降序）
                query = query.order_by(desc(StorageVolume.created_at))

            # 添加分页
            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            # 执行查询
            result = query.all()

        data = []
        for volume, domain in result:
            item = volume.to_dict()
            item["domain"] = domain.to_dict() if domain else None
            data.append(item)
            # volumes = session.query(StorageVolume).order_by(desc(StorageVolume.time)).all()

        # data = [StorageVolume.to_dict_merge(volume) for volume in volumes]
        r = {
            "code": "200",
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": int(total_count),
            "data": data
        }
        return r

    @get(_path="/v5/storage/pool/volume/detail", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_get_pool_volume_detail(self):
        """
        获取存储卷列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = self.get_argument("id")
        host = self.get_argument("host")
        pool_name = self.get_argument("pool_name")

        data = {}
        with self.session_scope() as session:
            volume = session.query(StorageVolume).filter_by(id=id).first()
            if volume:
                data = StorageVolume.to_dict(volume)
                libvirtClient = Client(host=host)
                r = libvirtClient.get_storage_pool_volume_info(
                    libvirtClient, pool_name, data["name"])

                data["path"] = r["path"]
                data["xml"] = r["xml"]
                data["key"] = r["key"]

        return {"code": "200", "msg": "成功", "data": data}



    @post(_path="/v5/store/pool/group/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_add(self, form):
        """
        新增存储池组
        """
        name = form.get("name", "")
        pid = form.get("pid", 0)
        remark = form.get("remark", "")

        if not name:
            return {"code": 400, "msg": "组名称不能为空"}

        with self.session_scope() as session:
            group = StoragePoolGroup(name=name, pid=pid, remark=remark)
            session.add(group)
            session.commit()
            return {"code": 200, "msg": "ok", "data": {"id": group.id}}

    @get(_path="/v5/store/pool/group/list", _consumes=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_list(self):
        """
        存储池组列表（树形结构）
        """
        # page = int(form.get("page", 1))
        # pagecount = int(form.get("pagecount", 1000))  # 拉全量，前端分页
        # search_str = form.get("search_str", "")

        with self.session_scope() as session:
            query = session.query(StoragePoolGroup)
            # if search_str:
            #     query = query.filter(StoragePoolGroup.name.like(f"%{search_str}%"))
            groups = query.order_by(StoragePoolGroup.id).all()
            group_dict = {g.id: g.to_dict() for g in groups}
            # 初始化 children
            for g in group_dict.values():
                g["children"] = []
            # 构建树
            root_list = []
            for g in group_dict.values():
                pid = g.get("pid", 0)
                if pid and pid in group_dict:
                    group_dict[pid]["children"].append(g)
                else:
                    root_list.append(g)
        return {
            "code": 200,
            "msg": "ok",
            "total": len(groups),
            "data": root_list
        }

    @put(_path="/v5/store/pool/group/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_put_pool_group_update(self, form):
        """
        修改存储池组
        """
        group_id = form.get("id")
        name = form.get("name", "")
        remark = form.get("remark", "")
        pid = form.get("pid", None)

        if not group_id:
            return {"code": 400, "msg": "id不能为空"}

        with self.session_scope() as session:
            group = session.query(StoragePoolGroup).filter_by(id=group_id).first()
            if not group:
                return {"code": 404, "msg": "未找到该组"}
            if name:
                group.name = name
            if remark != "":
                group.remark = remark
            if pid is not None:
                group.pid = pid
            session.commit()
            return {"code": 200, "msg": "ok"}

    @delete(_path="/v5/store/pool/group/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_pool_group_delete(self, form):
        """
        删除存储池组
        """
        group_id = form.get("id")
        if not group_id:
            return {"code": 400, "msg": "id不能为空"}
        def delete_group_and_children(session, gid):
            # 先删除所有子组
            children = session.query(StoragePoolGroup).filter_by(pid=gid).all()
            for child in children:
                delete_group_and_children(session, child.id)
            # 删除关联表
            session.query(StoragePoolGroupMapping).filter_by(storage_pool_group_id=gid).delete(synchronize_session=False)
            # 删除本组
            session.query(StoragePoolGroup).filter_by(id=gid).delete(synchronize_session=False)

        with self.session_scope() as session:
            delete_group_and_children(session, group_id)
            session.commit()
            return {"code": 200, "msg": "ok"}

    @post(_path="/v5/store/pool/group/mapping/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_mapping_add(self, form):
        """
        批量添加组与存储池的关联
        """
        group_id = form.get("group_id")
        pool_ids = form.get("pool_ids", [])  # 这里是列表
        if not group_id or not pool_ids or not isinstance(pool_ids, list):
            return {"code": 400, "msg": "参数不完整"}
        with self.session_scope() as session:
            added = []
            for pool_id in pool_ids:
                # 防止重复添加
                exists = session.query(StoragePoolGroupMapping).filter_by(
                    storage_pool_group_id=group_id, pool_id=pool_id).first()
                if not exists:
                    mapping = StoragePoolGroupMapping(storage_pool_group_id=group_id, pool_id=pool_id)
                    session.add(mapping)
                    session.flush()
                    added.append(mapping.id)
            session.commit()
            return {"code": 200, "msg": "ok", "data": {"ids": added}}

    @delete(_path="/v5/store/pool/group/mapping/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_pool_group_mapping(self, form):
        """
        批量移除组下的存储池
        """
        group_id = form.get("group_id")
        pool_ids = form.get("pool_ids", [])  # 这里是列表
        if not group_id or not pool_ids or not isinstance(pool_ids, list):
            return {"code": 400, "msg": "参数不完整"}
        with self.session_scope() as session:
            session.query(StoragePoolGroupMapping).filter(
                StoragePoolGroupMapping.storage_pool_group_id == group_id,
                StoragePoolGroupMapping.pool_id.in_(pool_ids)
            ).delete(synchronize_session=False)
            session.commit()
            return {"code": 200, "msg": "ok"}
    @post(_path="/v5/store/pool/group/mapping/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_mapping_list(self, form):
        """
        查询某组下的存储池
        """
        group_id = form.get("group_id")
        page = int(form.get("page", 1))
        pagecount = int(form.get("pagecount", 10))
        with self.session_scope() as session:
            query = session.query(StoragePoolGroupMapping).filter_by(storage_pool_group_id=group_id)
            total = query.count()
            mappings = query.offset((page-1)*pagecount).limit(pagecount).all()
            pool_ids = [m.pool_id for m in mappings]
            pools = session.query(StoragePool).filter(StoragePool.id.in_(pool_ids)).all()
            data = [p.to_dict() for p in pools]
        return {"code": 200, "msg": "ok", "total": total, "page": page, "pagecount": pagecount, "data": data}
    
    @post(_path="/v5/store/pool/group/mapping/move", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_mapping_move(self, form):
        """
        批量移动存储池到指定组
        参数：
        group_id: 目标组id
        pool_ids: 存储池id列表
        """
        group_id = form.get("group_id")
        
        pool_ids = form.get("pool_ids", [])
        group_name = form.get("group_name")
        pool_names = form.get("pool_names", [])
        if not group_id or not pool_ids or not isinstance(pool_ids, list):
            return {"code": 400, "msg": "参数不完整"}
        with self.session_scope() as session:
            # 1. 先删除这些存储池的所有旧组关系
            session.query(StoragePoolGroupMapping).filter(
                StoragePoolGroupMapping.pool_id.in_(pool_ids)
            ).delete(synchronize_session=False)
            # 2. 再添加到新组（防止重复）
            added = []
            for pool_id in pool_ids:
                exists = session.query(StoragePoolGroupMapping).filter_by(
                    storage_pool_group_id=group_id, pool_id=pool_id).first()
                if not exists:
                    mapping = StoragePoolGroupMapping(storage_pool_group_id=group_id, pool_id=pool_id)
                    session.add(mapping)
                    session.flush()
                    added.append(mapping.id)
            session.commit()
            return {"code": 200, "msg": "ok", "data": {"ids": added}}
# -*- coding: utf-8 -*-


from dataclasses import dataclass

@dataclass
class Volume(object):
    id : str
    status : str
    size : int
    
@dataclass
class VolumeDetailforlist(object):
    id : str
    status : str
    name : str
    size : int
    attachments : list
    bootable : str
    volume_type : str
    
@dataclass
class VolumeDetail(object):
    id : str
    status : str
    name : str
    size : int
    attachments : list
    bootable : str
    volume_type : str
    #volume_image_metadata : dict
    #description : str

@dataclass
class VolumeTypeDetail(object):
    id : str
    name : str
    is_public : bool
   
    
@dataclass
class VolumeSnapshotDetail(object):
    id : str
    status : str
    name : str
    size : int
    volume_id : str

class VolumesDetailFrom(object):
    page: int
    pagecount: int
    search: str
    
class VolumeCreateFrom:
    name : str
    size : int
    imageRef: str
    description : str
    
class VolumeEditFrom:
    id : str
    name : str
    
class VolumeDeleteFrom:
    id : str
    
class VolumeActionFrom:
    id : str
    action : str
    data : str
    
class VolumeAttachFrom:
    id : str
    vmid : str
    mountpoint : str
    
